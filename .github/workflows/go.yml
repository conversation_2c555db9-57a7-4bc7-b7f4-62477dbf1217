name: Go

on: [push, pull_request]

env:
  GOTOOLCHAIN: local

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        go-version: ["1.23", "1.24"]
    name: Build ${{ matrix.go-version == '1.24' && '(latest)' || '(old)' }}

    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ matrix.go-version }}

    - name: Build
      run: go build -v ./...

    - name: Test
      run: go test -v ./...

    - name: Install goimports
      run: |
        go install golang.org/x/tools/cmd/goimports@latest
        export PATH="$HOME/go/bin:$PATH"

    - name: Run pre-commit
      uses: pre-commit/action@v3.0.1
