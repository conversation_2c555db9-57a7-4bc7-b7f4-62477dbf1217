// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloCoreTypeLink/InstamadilloCoreTypeLink.proto

package instamadilloCoreTypeLink

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	instamadilloCoreTypeMedia "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeMedia"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Link struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          *string                `protobuf:"bytes,1,opt,name=text" json:"text,omitempty"`
	LinkContext   *LinkContext           `protobuf:"bytes,2,opt,name=linkContext" json:"linkContext,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Link) Reset() {
	*x = Link{}
	mi := &file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Link) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Link) ProtoMessage() {}

func (x *Link) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Link.ProtoReflect.Descriptor instead.
func (*Link) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescGZIP(), []int{0}
}

func (x *Link) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

func (x *Link) GetLinkContext() *LinkContext {
	if x != nil {
		return x.LinkContext
	}
	return nil
}

type LinkContext struct {
	state                            protoimpl.MessageState               `protogen:"open.v1"`
	LinkImageURL                     *ImageUrl                            `protobuf:"bytes,1,opt,name=linkImageURL" json:"linkImageURL,omitempty"`
	LinkPreviewTitle                 *string                              `protobuf:"bytes,2,opt,name=linkPreviewTitle" json:"linkPreviewTitle,omitempty"`
	LinkURL                          *string                              `protobuf:"bytes,3,opt,name=linkURL" json:"linkURL,omitempty"`
	LinkSummary                      *string                              `protobuf:"bytes,4,opt,name=linkSummary" json:"linkSummary,omitempty"`
	LinkMusicPreviewURL              *string                              `protobuf:"bytes,5,opt,name=linkMusicPreviewURL" json:"linkMusicPreviewURL,omitempty"`
	LinkMusicPreviewCountriesAllowed []string                             `protobuf:"bytes,6,rep,name=linkMusicPreviewCountriesAllowed" json:"linkMusicPreviewCountriesAllowed,omitempty"`
	LinkPreviewThumbnail             *instamadilloCoreTypeMedia.Thumbnail `protobuf:"bytes,7,opt,name=linkPreviewThumbnail" json:"linkPreviewThumbnail,omitempty"`
	LinkPreviewBody                  *string                              `protobuf:"bytes,8,opt,name=linkPreviewBody" json:"linkPreviewBody,omitempty"`
	unknownFields                    protoimpl.UnknownFields
	sizeCache                        protoimpl.SizeCache
}

func (x *LinkContext) Reset() {
	*x = LinkContext{}
	mi := &file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkContext) ProtoMessage() {}

func (x *LinkContext) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkContext.ProtoReflect.Descriptor instead.
func (*LinkContext) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescGZIP(), []int{1}
}

func (x *LinkContext) GetLinkImageURL() *ImageUrl {
	if x != nil {
		return x.LinkImageURL
	}
	return nil
}

func (x *LinkContext) GetLinkPreviewTitle() string {
	if x != nil && x.LinkPreviewTitle != nil {
		return *x.LinkPreviewTitle
	}
	return ""
}

func (x *LinkContext) GetLinkURL() string {
	if x != nil && x.LinkURL != nil {
		return *x.LinkURL
	}
	return ""
}

func (x *LinkContext) GetLinkSummary() string {
	if x != nil && x.LinkSummary != nil {
		return *x.LinkSummary
	}
	return ""
}

func (x *LinkContext) GetLinkMusicPreviewURL() string {
	if x != nil && x.LinkMusicPreviewURL != nil {
		return *x.LinkMusicPreviewURL
	}
	return ""
}

func (x *LinkContext) GetLinkMusicPreviewCountriesAllowed() []string {
	if x != nil {
		return x.LinkMusicPreviewCountriesAllowed
	}
	return nil
}

func (x *LinkContext) GetLinkPreviewThumbnail() *instamadilloCoreTypeMedia.Thumbnail {
	if x != nil {
		return x.LinkPreviewThumbnail
	}
	return nil
}

func (x *LinkContext) GetLinkPreviewBody() string {
	if x != nil && x.LinkPreviewBody != nil {
		return *x.LinkPreviewBody
	}
	return ""
}

type ImageUrl struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	URL           *string                `protobuf:"bytes,1,opt,name=URL" json:"URL,omitempty"`
	Width         *int32                 `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`
	Height        *int32                 `protobuf:"varint,3,opt,name=height" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageUrl) Reset() {
	*x = ImageUrl{}
	mi := &file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageUrl) ProtoMessage() {}

func (x *ImageUrl) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageUrl.ProtoReflect.Descriptor instead.
func (*ImageUrl) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescGZIP(), []int{2}
}

func (x *ImageUrl) GetURL() string {
	if x != nil && x.URL != nil {
		return *x.URL
	}
	return ""
}

func (x *ImageUrl) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *ImageUrl) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

var File_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto protoreflect.FileDescriptor

const file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDesc = "" +
	"\n" +
	"7instamadilloCoreTypeLink/InstamadilloCoreTypeLink.proto\x12\x18InstamadilloCoreTypeLink\x1a9instamadilloCoreTypeMedia/InstamadilloCoreTypeMedia.proto\"c\n" +
	"\x04Link\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12G\n" +
	"\vlinkContext\x18\x02 \x01(\v2%.InstamadilloCoreTypeLink.LinkContextR\vlinkContext\"\xbf\x03\n" +
	"\vLinkContext\x12F\n" +
	"\flinkImageURL\x18\x01 \x01(\v2\".InstamadilloCoreTypeLink.ImageUrlR\flinkImageURL\x12*\n" +
	"\x10linkPreviewTitle\x18\x02 \x01(\tR\x10linkPreviewTitle\x12\x18\n" +
	"\alinkURL\x18\x03 \x01(\tR\alinkURL\x12 \n" +
	"\vlinkSummary\x18\x04 \x01(\tR\vlinkSummary\x120\n" +
	"\x13linkMusicPreviewURL\x18\x05 \x01(\tR\x13linkMusicPreviewURL\x12J\n" +
	" linkMusicPreviewCountriesAllowed\x18\x06 \x03(\tR linkMusicPreviewCountriesAllowed\x12X\n" +
	"\x14linkPreviewThumbnail\x18\a \x01(\v2$.InstamadilloCoreTypeMedia.ThumbnailR\x14linkPreviewThumbnail\x12(\n" +
	"\x0flinkPreviewBody\x18\b \x01(\tR\x0flinkPreviewBody\"J\n" +
	"\bImageUrl\x12\x10\n" +
	"\x03URL\x18\x01 \x01(\tR\x03URL\x12\x14\n" +
	"\x05width\x18\x02 \x01(\x05R\x05width\x12\x16\n" +
	"\x06height\x18\x03 \x01(\x05R\x06heightB4Z2go.mau.fi/whatsmeow/proto/instamadilloCoreTypeLink"

var (
	file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescOnce sync.Once
	file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescData []byte
)

func file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescGZIP() []byte {
	file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescOnce.Do(func() {
		file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDesc), len(file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDesc)))
	})
	return file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDescData
}

var file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_goTypes = []any{
	(*Link)(nil),        // 0: InstamadilloCoreTypeLink.Link
	(*LinkContext)(nil), // 1: InstamadilloCoreTypeLink.LinkContext
	(*ImageUrl)(nil),    // 2: InstamadilloCoreTypeLink.ImageUrl
	(*instamadilloCoreTypeMedia.Thumbnail)(nil), // 3: InstamadilloCoreTypeMedia.Thumbnail
}
var file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_depIdxs = []int32{
	1, // 0: InstamadilloCoreTypeLink.Link.linkContext:type_name -> InstamadilloCoreTypeLink.LinkContext
	2, // 1: InstamadilloCoreTypeLink.LinkContext.linkImageURL:type_name -> InstamadilloCoreTypeLink.ImageUrl
	3, // 2: InstamadilloCoreTypeLink.LinkContext.linkPreviewThumbnail:type_name -> InstamadilloCoreTypeMedia.Thumbnail
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_init() }
func file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_init() {
	if File_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDesc), len(file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_goTypes,
		DependencyIndexes: file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_depIdxs,
		MessageInfos:      file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_msgTypes,
	}.Build()
	File_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto = out.File
	file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_goTypes = nil
	file_instamadilloCoreTypeLink_InstamadilloCoreTypeLink_proto_depIdxs = nil
}
