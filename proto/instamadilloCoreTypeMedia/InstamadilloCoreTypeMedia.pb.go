// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloCoreTypeMedia/InstamadilloCoreTypeMedia.proto

package instamadilloCoreTypeMedia

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PjpegScanConfiguration int32

const (
	PjpegScanConfiguration_PJPEG_SCAN_CONFIGURATION_UNSPECIFIED PjpegScanConfiguration = 0
	PjpegScanConfiguration_PJPEG_SCAN_CONFIGURATION_WA          PjpegScanConfiguration = 1
	PjpegScanConfiguration_PJPEG_SCAN_CONFIGURATION_E15         PjpegScanConfiguration = 2
	PjpegScanConfiguration_PJPEG_SCAN_CONFIGURATION_E35         PjpegScanConfiguration = 3
)

// Enum value maps for PjpegScanConfiguration.
var (
	PjpegScanConfiguration_name = map[int32]string{
		0: "PJPEG_SCAN_CONFIGURATION_UNSPECIFIED",
		1: "PJPEG_SCAN_CONFIGURATION_WA",
		2: "PJPEG_SCAN_CONFIGURATION_E15",
		3: "PJPEG_SCAN_CONFIGURATION_E35",
	}
	PjpegScanConfiguration_value = map[string]int32{
		"PJPEG_SCAN_CONFIGURATION_UNSPECIFIED": 0,
		"PJPEG_SCAN_CONFIGURATION_WA":          1,
		"PJPEG_SCAN_CONFIGURATION_E15":         2,
		"PJPEG_SCAN_CONFIGURATION_E35":         3,
	}
)

func (x PjpegScanConfiguration) Enum() *PjpegScanConfiguration {
	p := new(PjpegScanConfiguration)
	*p = x
	return p
}

func (x PjpegScanConfiguration) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PjpegScanConfiguration) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes[0].Descriptor()
}

func (PjpegScanConfiguration) Type() protoreflect.EnumType {
	return &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes[0]
}

func (x PjpegScanConfiguration) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PjpegScanConfiguration) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PjpegScanConfiguration(num)
	return nil
}

// Deprecated: Use PjpegScanConfiguration.Descriptor instead.
func (PjpegScanConfiguration) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{0}
}

type Media_InterventionType int32

const (
	Media_UNSET Media_InterventionType = 0
	Media_NONE  Media_InterventionType = 1
	Media_NUDE  Media_InterventionType = 2
)

// Enum value maps for Media_InterventionType.
var (
	Media_InterventionType_name = map[int32]string{
		0: "UNSET",
		1: "NONE",
		2: "NUDE",
	}
	Media_InterventionType_value = map[string]int32{
		"UNSET": 0,
		"NONE":  1,
		"NUDE":  2,
	}
)

func (x Media_InterventionType) Enum() *Media_InterventionType {
	p := new(Media_InterventionType)
	*p = x
	return p
}

func (x Media_InterventionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Media_InterventionType) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes[1].Descriptor()
}

func (Media_InterventionType) Type() protoreflect.EnumType {
	return &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes[1]
}

func (x Media_InterventionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Media_InterventionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Media_InterventionType(num)
	return nil
}

// Deprecated: Use Media_InterventionType.Descriptor instead.
func (Media_InterventionType) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{0, 0}
}

type Raven_ViewMode int32

const (
	Raven_RAVEN_VIEW_MODEL_UNSPECIFIED Raven_ViewMode = 0
	Raven_RAVEN_VIEW_MODEL_ONCE        Raven_ViewMode = 1
	Raven_RAVEN_VIEW_MODEL_REPLAYABLE  Raven_ViewMode = 2
	Raven_RAVEN_VIEW_MODEL_PERMANENT   Raven_ViewMode = 3
)

// Enum value maps for Raven_ViewMode.
var (
	Raven_ViewMode_name = map[int32]string{
		0: "RAVEN_VIEW_MODEL_UNSPECIFIED",
		1: "RAVEN_VIEW_MODEL_ONCE",
		2: "RAVEN_VIEW_MODEL_REPLAYABLE",
		3: "RAVEN_VIEW_MODEL_PERMANENT",
	}
	Raven_ViewMode_value = map[string]int32{
		"RAVEN_VIEW_MODEL_UNSPECIFIED": 0,
		"RAVEN_VIEW_MODEL_ONCE":        1,
		"RAVEN_VIEW_MODEL_REPLAYABLE":  2,
		"RAVEN_VIEW_MODEL_PERMANENT":   3,
	}
)

func (x Raven_ViewMode) Enum() *Raven_ViewMode {
	p := new(Raven_ViewMode)
	*p = x
	return p
}

func (x Raven_ViewMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Raven_ViewMode) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes[2].Descriptor()
}

func (Raven_ViewMode) Type() protoreflect.EnumType {
	return &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes[2]
}

func (x Raven_ViewMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Raven_ViewMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Raven_ViewMode(num)
	return nil
}

// Deprecated: Use Raven_ViewMode.Descriptor instead.
func (Raven_ViewMode) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{6, 0}
}

type Media struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Media:
	//
	//	*Media_StaticPhoto
	//	*Media_Voice
	//	*Media_Video
	//	*Media_Raven
	//	*Media_Gif
	//	*Media_AvatarSticker
	Media         isMedia_Media `protobuf_oneof:"media"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Media) Reset() {
	*x = Media{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Media) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Media) ProtoMessage() {}

func (x *Media) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Media.ProtoReflect.Descriptor instead.
func (*Media) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{0}
}

func (x *Media) GetMedia() isMedia_Media {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *Media) GetStaticPhoto() *StaticPhoto {
	if x != nil {
		if x, ok := x.Media.(*Media_StaticPhoto); ok {
			return x.StaticPhoto
		}
	}
	return nil
}

func (x *Media) GetVoice() *Voice {
	if x != nil {
		if x, ok := x.Media.(*Media_Voice); ok {
			return x.Voice
		}
	}
	return nil
}

func (x *Media) GetVideo() *Video {
	if x != nil {
		if x, ok := x.Media.(*Media_Video); ok {
			return x.Video
		}
	}
	return nil
}

func (x *Media) GetRaven() *Raven {
	if x != nil {
		if x, ok := x.Media.(*Media_Raven); ok {
			return x.Raven
		}
	}
	return nil
}

func (x *Media) GetGif() *Gif {
	if x != nil {
		if x, ok := x.Media.(*Media_Gif); ok {
			return x.Gif
		}
	}
	return nil
}

func (x *Media) GetAvatarSticker() *AvatarSticker {
	if x != nil {
		if x, ok := x.Media.(*Media_AvatarSticker); ok {
			return x.AvatarSticker
		}
	}
	return nil
}

type isMedia_Media interface {
	isMedia_Media()
}

type Media_StaticPhoto struct {
	StaticPhoto *StaticPhoto `protobuf:"bytes,1,opt,name=staticPhoto,oneof"`
}

type Media_Voice struct {
	Voice *Voice `protobuf:"bytes,2,opt,name=voice,oneof"`
}

type Media_Video struct {
	Video *Video `protobuf:"bytes,3,opt,name=video,oneof"`
}

type Media_Raven struct {
	Raven *Raven `protobuf:"bytes,4,opt,name=raven,oneof"`
}

type Media_Gif struct {
	Gif *Gif `protobuf:"bytes,5,opt,name=gif,oneof"`
}

type Media_AvatarSticker struct {
	AvatarSticker *AvatarSticker `protobuf:"bytes,6,opt,name=avatarSticker,oneof"`
}

func (*Media_StaticPhoto) isMedia_Media() {}

func (*Media_Voice) isMedia_Media() {}

func (*Media_Video) isMedia_Media() {}

func (*Media_Raven) isMedia_Media() {}

func (*Media_Gif) isMedia_Media() {}

func (*Media_AvatarSticker) isMedia_Media() {}

type StaticPhoto struct {
	state                  protoimpl.MessageState  `protogen:"open.v1"`
	MediaTransport         *CommonMediaTransport   `protobuf:"bytes,1,opt,name=mediaTransport" json:"mediaTransport,omitempty"`
	Height                 *int32                  `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
	Width                  *int32                  `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	ScanLengths            []int32                 `protobuf:"varint,4,rep,packed,name=scanLengths" json:"scanLengths,omitempty"`
	Thumbnail              *Thumbnail              `protobuf:"bytes,5,opt,name=thumbnail" json:"thumbnail,omitempty"`
	PjpegScanConfiguration *PjpegScanConfiguration `protobuf:"varint,6,opt,name=pjpegScanConfiguration,enum=InstamadilloCoreTypeMedia.PjpegScanConfiguration" json:"pjpegScanConfiguration,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *StaticPhoto) Reset() {
	*x = StaticPhoto{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaticPhoto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticPhoto) ProtoMessage() {}

func (x *StaticPhoto) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticPhoto.ProtoReflect.Descriptor instead.
func (*StaticPhoto) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{1}
}

func (x *StaticPhoto) GetMediaTransport() *CommonMediaTransport {
	if x != nil {
		return x.MediaTransport
	}
	return nil
}

func (x *StaticPhoto) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *StaticPhoto) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *StaticPhoto) GetScanLengths() []int32 {
	if x != nil {
		return x.ScanLengths
	}
	return nil
}

func (x *StaticPhoto) GetThumbnail() *Thumbnail {
	if x != nil {
		return x.Thumbnail
	}
	return nil
}

func (x *StaticPhoto) GetPjpegScanConfiguration() PjpegScanConfiguration {
	if x != nil && x.PjpegScanConfiguration != nil {
		return *x.PjpegScanConfiguration
	}
	return PjpegScanConfiguration_PJPEG_SCAN_CONFIGURATION_UNSPECIFIED
}

type Voice struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	MediaTransport              *CommonMediaTransport  `protobuf:"bytes,1,opt,name=mediaTransport" json:"mediaTransport,omitempty"`
	Duration                    *int32                 `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"`
	Waveforms                   []float32              `protobuf:"fixed32,3,rep,packed,name=waveforms" json:"waveforms,omitempty"`
	WaveformSamplingFrequencyHz *int32                 `protobuf:"varint,4,opt,name=waveformSamplingFrequencyHz" json:"waveformSamplingFrequencyHz,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *Voice) Reset() {
	*x = Voice{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Voice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Voice) ProtoMessage() {}

func (x *Voice) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Voice.ProtoReflect.Descriptor instead.
func (*Voice) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{2}
}

func (x *Voice) GetMediaTransport() *CommonMediaTransport {
	if x != nil {
		return x.MediaTransport
	}
	return nil
}

func (x *Voice) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *Voice) GetWaveforms() []float32 {
	if x != nil {
		return x.Waveforms
	}
	return nil
}

func (x *Voice) GetWaveformSamplingFrequencyHz() int32 {
	if x != nil && x.WaveformSamplingFrequencyHz != nil {
		return *x.WaveformSamplingFrequencyHz
	}
	return 0
}

type Video struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	MediaTransport     *CommonMediaTransport  `protobuf:"bytes,1,opt,name=mediaTransport" json:"mediaTransport,omitempty"`
	Height             *int32                 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
	Width              *int32                 `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	Thumbnail          *Thumbnail             `protobuf:"bytes,4,opt,name=thumbnail" json:"thumbnail,omitempty"`
	VideoExtraMetadata *VideoExtraMetadata    `protobuf:"bytes,5,opt,name=videoExtraMetadata" json:"videoExtraMetadata,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Video) Reset() {
	*x = Video{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{3}
}

func (x *Video) GetMediaTransport() *CommonMediaTransport {
	if x != nil {
		return x.MediaTransport
	}
	return nil
}

func (x *Video) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Video) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Video) GetThumbnail() *Thumbnail {
	if x != nil {
		return x.Thumbnail
	}
	return nil
}

func (x *Video) GetVideoExtraMetadata() *VideoExtraMetadata {
	if x != nil {
		return x.VideoExtraMetadata
	}
	return nil
}

type Gif struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MediaTransport *CommonMediaTransport  `protobuf:"bytes,1,opt,name=mediaTransport" json:"mediaTransport,omitempty"`
	Height         *int32                 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
	Width          *int32                 `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	IsSticker      *bool                  `protobuf:"varint,4,opt,name=isSticker" json:"isSticker,omitempty"`
	StickerID      *string                `protobuf:"bytes,5,opt,name=stickerID" json:"stickerID,omitempty"`
	GifURL         *string                `protobuf:"bytes,6,opt,name=gifURL" json:"gifURL,omitempty"`
	GifSize        *int32                 `protobuf:"varint,7,opt,name=gifSize" json:"gifSize,omitempty"`
	IsRandom       *bool                  `protobuf:"varint,8,opt,name=isRandom" json:"isRandom,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Gif) Reset() {
	*x = Gif{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Gif) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gif) ProtoMessage() {}

func (x *Gif) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gif.ProtoReflect.Descriptor instead.
func (*Gif) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{4}
}

func (x *Gif) GetMediaTransport() *CommonMediaTransport {
	if x != nil {
		return x.MediaTransport
	}
	return nil
}

func (x *Gif) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Gif) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *Gif) GetIsSticker() bool {
	if x != nil && x.IsSticker != nil {
		return *x.IsSticker
	}
	return false
}

func (x *Gif) GetStickerID() string {
	if x != nil && x.StickerID != nil {
		return *x.StickerID
	}
	return ""
}

func (x *Gif) GetGifURL() string {
	if x != nil && x.GifURL != nil {
		return *x.GifURL
	}
	return ""
}

func (x *Gif) GetGifSize() int32 {
	if x != nil && x.GifSize != nil {
		return *x.GifSize
	}
	return 0
}

func (x *Gif) GetIsRandom() bool {
	if x != nil && x.IsRandom != nil {
		return *x.IsRandom
	}
	return false
}

type AvatarSticker struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	MediaTransport  *CommonMediaTransport  `protobuf:"bytes,1,opt,name=mediaTransport" json:"mediaTransport,omitempty"`
	IsAnimated      *bool                  `protobuf:"varint,2,opt,name=isAnimated" json:"isAnimated,omitempty"`
	StickerID       *string                `protobuf:"bytes,3,opt,name=stickerID" json:"stickerID,omitempty"`
	StickerTemplate *string                `protobuf:"bytes,4,opt,name=stickerTemplate" json:"stickerTemplate,omitempty"`
	NuxType         *int32                 `protobuf:"varint,5,opt,name=nuxType" json:"nuxType,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AvatarSticker) Reset() {
	*x = AvatarSticker{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvatarSticker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarSticker) ProtoMessage() {}

func (x *AvatarSticker) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarSticker.ProtoReflect.Descriptor instead.
func (*AvatarSticker) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{5}
}

func (x *AvatarSticker) GetMediaTransport() *CommonMediaTransport {
	if x != nil {
		return x.MediaTransport
	}
	return nil
}

func (x *AvatarSticker) GetIsAnimated() bool {
	if x != nil && x.IsAnimated != nil {
		return *x.IsAnimated
	}
	return false
}

func (x *AvatarSticker) GetStickerID() string {
	if x != nil && x.StickerID != nil {
		return *x.StickerID
	}
	return ""
}

func (x *AvatarSticker) GetStickerTemplate() string {
	if x != nil && x.StickerTemplate != nil {
		return *x.StickerTemplate
	}
	return ""
}

func (x *AvatarSticker) GetNuxType() int32 {
	if x != nil && x.NuxType != nil {
		return *x.NuxType
	}
	return 0
}

type Raven struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ViewMode      *Raven_ViewMode        `protobuf:"varint,1,opt,name=viewMode,enum=InstamadilloCoreTypeMedia.Raven_ViewMode" json:"viewMode,omitempty"`
	Content       *RavenContent          `protobuf:"bytes,2,opt,name=content" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Raven) Reset() {
	*x = Raven{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Raven) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Raven) ProtoMessage() {}

func (x *Raven) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Raven.ProtoReflect.Descriptor instead.
func (*Raven) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{6}
}

func (x *Raven) GetViewMode() Raven_ViewMode {
	if x != nil && x.ViewMode != nil {
		return *x.ViewMode
	}
	return Raven_RAVEN_VIEW_MODEL_UNSPECIFIED
}

func (x *Raven) GetContent() *RavenContent {
	if x != nil {
		return x.Content
	}
	return nil
}

type RavenContent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to RavenContent:
	//
	//	*RavenContent_StaticPhoto
	//	*RavenContent_Video
	RavenContent  isRavenContent_RavenContent `protobuf_oneof:"ravenContent"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RavenContent) Reset() {
	*x = RavenContent{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RavenContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RavenContent) ProtoMessage() {}

func (x *RavenContent) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RavenContent.ProtoReflect.Descriptor instead.
func (*RavenContent) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{7}
}

func (x *RavenContent) GetRavenContent() isRavenContent_RavenContent {
	if x != nil {
		return x.RavenContent
	}
	return nil
}

func (x *RavenContent) GetStaticPhoto() *StaticPhoto {
	if x != nil {
		if x, ok := x.RavenContent.(*RavenContent_StaticPhoto); ok {
			return x.StaticPhoto
		}
	}
	return nil
}

func (x *RavenContent) GetVideo() *Video {
	if x != nil {
		if x, ok := x.RavenContent.(*RavenContent_Video); ok {
			return x.Video
		}
	}
	return nil
}

type isRavenContent_RavenContent interface {
	isRavenContent_RavenContent()
}

type RavenContent_StaticPhoto struct {
	StaticPhoto *StaticPhoto `protobuf:"bytes,1,opt,name=staticPhoto,oneof"`
}

type RavenContent_Video struct {
	Video *Video `protobuf:"bytes,2,opt,name=video,oneof"`
}

func (*RavenContent_StaticPhoto) isRavenContent_RavenContent() {}

func (*RavenContent_Video) isRavenContent_RavenContent() {}

type Thumbnail struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MediaTransport *CommonMediaTransport  `protobuf:"bytes,1,opt,name=mediaTransport" json:"mediaTransport,omitempty"`
	Height         *int32                 `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
	Width          *int32                 `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Thumbnail) Reset() {
	*x = Thumbnail{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Thumbnail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Thumbnail) ProtoMessage() {}

func (x *Thumbnail) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Thumbnail.ProtoReflect.Descriptor instead.
func (*Thumbnail) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{8}
}

func (x *Thumbnail) GetMediaTransport() *CommonMediaTransport {
	if x != nil {
		return x.MediaTransport
	}
	return nil
}

func (x *Thumbnail) GetHeight() int32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *Thumbnail) GetWidth() int32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

type CommonMediaTransport struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	MediaID           *string                `protobuf:"bytes,1,opt,name=mediaID" json:"mediaID,omitempty"`
	FileSHA256        *string                `protobuf:"bytes,2,opt,name=fileSHA256" json:"fileSHA256,omitempty"`
	MediaKey          *string                `protobuf:"bytes,3,opt,name=mediaKey" json:"mediaKey,omitempty"`
	FileEncSHA256     *string                `protobuf:"bytes,4,opt,name=fileEncSHA256" json:"fileEncSHA256,omitempty"`
	DirectPath        *string                `protobuf:"bytes,5,opt,name=directPath" json:"directPath,omitempty"`
	MediaKeyTimestamp *string                `protobuf:"bytes,6,opt,name=mediaKeyTimestamp" json:"mediaKeyTimestamp,omitempty"`
	Sidecar           *string                `protobuf:"bytes,7,opt,name=sidecar" json:"sidecar,omitempty"`
	FileLength        *int32                 `protobuf:"varint,8,opt,name=fileLength" json:"fileLength,omitempty"`
	Mimetype          *string                `protobuf:"bytes,9,opt,name=mimetype" json:"mimetype,omitempty"`
	ObjectID          *string                `protobuf:"bytes,10,opt,name=objectID" json:"objectID,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CommonMediaTransport) Reset() {
	*x = CommonMediaTransport{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonMediaTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonMediaTransport) ProtoMessage() {}

func (x *CommonMediaTransport) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonMediaTransport.ProtoReflect.Descriptor instead.
func (*CommonMediaTransport) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{9}
}

func (x *CommonMediaTransport) GetMediaID() string {
	if x != nil && x.MediaID != nil {
		return *x.MediaID
	}
	return ""
}

func (x *CommonMediaTransport) GetFileSHA256() string {
	if x != nil && x.FileSHA256 != nil {
		return *x.FileSHA256
	}
	return ""
}

func (x *CommonMediaTransport) GetMediaKey() string {
	if x != nil && x.MediaKey != nil {
		return *x.MediaKey
	}
	return ""
}

func (x *CommonMediaTransport) GetFileEncSHA256() string {
	if x != nil && x.FileEncSHA256 != nil {
		return *x.FileEncSHA256
	}
	return ""
}

func (x *CommonMediaTransport) GetDirectPath() string {
	if x != nil && x.DirectPath != nil {
		return *x.DirectPath
	}
	return ""
}

func (x *CommonMediaTransport) GetMediaKeyTimestamp() string {
	if x != nil && x.MediaKeyTimestamp != nil {
		return *x.MediaKeyTimestamp
	}
	return ""
}

func (x *CommonMediaTransport) GetSidecar() string {
	if x != nil && x.Sidecar != nil {
		return *x.Sidecar
	}
	return ""
}

func (x *CommonMediaTransport) GetFileLength() int32 {
	if x != nil && x.FileLength != nil {
		return *x.FileLength
	}
	return 0
}

func (x *CommonMediaTransport) GetMimetype() string {
	if x != nil && x.Mimetype != nil {
		return *x.Mimetype
	}
	return ""
}

func (x *CommonMediaTransport) GetObjectID() string {
	if x != nil && x.ObjectID != nil {
		return *x.ObjectID
	}
	return ""
}

type VideoExtraMetadata struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	UploadMosClientScore *float32               `protobuf:"fixed32,1,opt,name=uploadMosClientScore" json:"uploadMosClientScore,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *VideoExtraMetadata) Reset() {
	*x = VideoExtraMetadata{}
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoExtraMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoExtraMetadata) ProtoMessage() {}

func (x *VideoExtraMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoExtraMetadata.ProtoReflect.Descriptor instead.
func (*VideoExtraMetadata) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP(), []int{10}
}

func (x *VideoExtraMetadata) GetUploadMosClientScore() float32 {
	if x != nil && x.UploadMosClientScore != nil {
		return *x.UploadMosClientScore
	}
	return 0
}

var File_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto protoreflect.FileDescriptor

const file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDesc = "" +
	"\n" +
	"9instamadilloCoreTypeMedia/InstamadilloCoreTypeMedia.proto\x12\x19InstamadilloCoreTypeMedia\"\xc3\x03\n" +
	"\x05Media\x12J\n" +
	"\vstaticPhoto\x18\x01 \x01(\v2&.InstamadilloCoreTypeMedia.StaticPhotoH\x00R\vstaticPhoto\x128\n" +
	"\x05voice\x18\x02 \x01(\v2 .InstamadilloCoreTypeMedia.VoiceH\x00R\x05voice\x128\n" +
	"\x05video\x18\x03 \x01(\v2 .InstamadilloCoreTypeMedia.VideoH\x00R\x05video\x128\n" +
	"\x05raven\x18\x04 \x01(\v2 .InstamadilloCoreTypeMedia.RavenH\x00R\x05raven\x122\n" +
	"\x03gif\x18\x05 \x01(\v2\x1e.InstamadilloCoreTypeMedia.GifH\x00R\x03gif\x12P\n" +
	"\ravatarSticker\x18\x06 \x01(\v2(.InstamadilloCoreTypeMedia.AvatarStickerH\x00R\ravatarSticker\"1\n" +
	"\x10InterventionType\x12\t\n" +
	"\x05UNSET\x10\x00\x12\b\n" +
	"\x04NONE\x10\x01\x12\b\n" +
	"\x04NUDE\x10\x02B\a\n" +
	"\x05media\"\xe9\x02\n" +
	"\vStaticPhoto\x12W\n" +
	"\x0emediaTransport\x18\x01 \x01(\v2/.InstamadilloCoreTypeMedia.CommonMediaTransportR\x0emediaTransport\x12\x16\n" +
	"\x06height\x18\x02 \x01(\x05R\x06height\x12\x14\n" +
	"\x05width\x18\x03 \x01(\x05R\x05width\x12$\n" +
	"\vscanLengths\x18\x04 \x03(\x05B\x02\x10\x01R\vscanLengths\x12B\n" +
	"\tthumbnail\x18\x05 \x01(\v2$.InstamadilloCoreTypeMedia.ThumbnailR\tthumbnail\x12i\n" +
	"\x16pjpegScanConfiguration\x18\x06 \x01(\x0e21.InstamadilloCoreTypeMedia.PjpegScanConfigurationR\x16pjpegScanConfiguration\"\xe0\x01\n" +
	"\x05Voice\x12W\n" +
	"\x0emediaTransport\x18\x01 \x01(\v2/.InstamadilloCoreTypeMedia.CommonMediaTransportR\x0emediaTransport\x12\x1a\n" +
	"\bduration\x18\x02 \x01(\x05R\bduration\x12 \n" +
	"\twaveforms\x18\x03 \x03(\x02B\x02\x10\x01R\twaveforms\x12@\n" +
	"\x1bwaveformSamplingFrequencyHz\x18\x04 \x01(\x05R\x1bwaveformSamplingFrequencyHz\"\xb1\x02\n" +
	"\x05Video\x12W\n" +
	"\x0emediaTransport\x18\x01 \x01(\v2/.InstamadilloCoreTypeMedia.CommonMediaTransportR\x0emediaTransport\x12\x16\n" +
	"\x06height\x18\x02 \x01(\x05R\x06height\x12\x14\n" +
	"\x05width\x18\x03 \x01(\x05R\x05width\x12B\n" +
	"\tthumbnail\x18\x04 \x01(\v2$.InstamadilloCoreTypeMedia.ThumbnailR\tthumbnail\x12]\n" +
	"\x12videoExtraMetadata\x18\x05 \x01(\v2-.InstamadilloCoreTypeMedia.VideoExtraMetadataR\x12videoExtraMetadata\"\x96\x02\n" +
	"\x03Gif\x12W\n" +
	"\x0emediaTransport\x18\x01 \x01(\v2/.InstamadilloCoreTypeMedia.CommonMediaTransportR\x0emediaTransport\x12\x16\n" +
	"\x06height\x18\x02 \x01(\x05R\x06height\x12\x14\n" +
	"\x05width\x18\x03 \x01(\x05R\x05width\x12\x1c\n" +
	"\tisSticker\x18\x04 \x01(\bR\tisSticker\x12\x1c\n" +
	"\tstickerID\x18\x05 \x01(\tR\tstickerID\x12\x16\n" +
	"\x06gifURL\x18\x06 \x01(\tR\x06gifURL\x12\x18\n" +
	"\agifSize\x18\a \x01(\x05R\agifSize\x12\x1a\n" +
	"\bisRandom\x18\b \x01(\bR\bisRandom\"\xea\x01\n" +
	"\rAvatarSticker\x12W\n" +
	"\x0emediaTransport\x18\x01 \x01(\v2/.InstamadilloCoreTypeMedia.CommonMediaTransportR\x0emediaTransport\x12\x1e\n" +
	"\n" +
	"isAnimated\x18\x02 \x01(\bR\n" +
	"isAnimated\x12\x1c\n" +
	"\tstickerID\x18\x03 \x01(\tR\tstickerID\x12(\n" +
	"\x0fstickerTemplate\x18\x04 \x01(\tR\x0fstickerTemplate\x12\x18\n" +
	"\anuxType\x18\x05 \x01(\x05R\anuxType\"\x9c\x02\n" +
	"\x05Raven\x12E\n" +
	"\bviewMode\x18\x01 \x01(\x0e2).InstamadilloCoreTypeMedia.Raven.ViewModeR\bviewMode\x12A\n" +
	"\acontent\x18\x02 \x01(\v2'.InstamadilloCoreTypeMedia.RavenContentR\acontent\"\x88\x01\n" +
	"\bViewMode\x12 \n" +
	"\x1cRAVEN_VIEW_MODEL_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15RAVEN_VIEW_MODEL_ONCE\x10\x01\x12\x1f\n" +
	"\x1bRAVEN_VIEW_MODEL_REPLAYABLE\x10\x02\x12\x1e\n" +
	"\x1aRAVEN_VIEW_MODEL_PERMANENT\x10\x03\"\xa4\x01\n" +
	"\fRavenContent\x12J\n" +
	"\vstaticPhoto\x18\x01 \x01(\v2&.InstamadilloCoreTypeMedia.StaticPhotoH\x00R\vstaticPhoto\x128\n" +
	"\x05video\x18\x02 \x01(\v2 .InstamadilloCoreTypeMedia.VideoH\x00R\x05videoB\x0e\n" +
	"\fravenContent\"\x92\x01\n" +
	"\tThumbnail\x12W\n" +
	"\x0emediaTransport\x18\x01 \x01(\v2/.InstamadilloCoreTypeMedia.CommonMediaTransportR\x0emediaTransport\x12\x16\n" +
	"\x06height\x18\x02 \x01(\x05R\x06height\x12\x14\n" +
	"\x05width\x18\x03 \x01(\x05R\x05width\"\xd2\x02\n" +
	"\x14CommonMediaTransport\x12\x18\n" +
	"\amediaID\x18\x01 \x01(\tR\amediaID\x12\x1e\n" +
	"\n" +
	"fileSHA256\x18\x02 \x01(\tR\n" +
	"fileSHA256\x12\x1a\n" +
	"\bmediaKey\x18\x03 \x01(\tR\bmediaKey\x12$\n" +
	"\rfileEncSHA256\x18\x04 \x01(\tR\rfileEncSHA256\x12\x1e\n" +
	"\n" +
	"directPath\x18\x05 \x01(\tR\n" +
	"directPath\x12,\n" +
	"\x11mediaKeyTimestamp\x18\x06 \x01(\tR\x11mediaKeyTimestamp\x12\x18\n" +
	"\asidecar\x18\a \x01(\tR\asidecar\x12\x1e\n" +
	"\n" +
	"fileLength\x18\b \x01(\x05R\n" +
	"fileLength\x12\x1a\n" +
	"\bmimetype\x18\t \x01(\tR\bmimetype\x12\x1a\n" +
	"\bobjectID\x18\n" +
	" \x01(\tR\bobjectID\"H\n" +
	"\x12VideoExtraMetadata\x122\n" +
	"\x14uploadMosClientScore\x18\x01 \x01(\x02R\x14uploadMosClientScore*\xa7\x01\n" +
	"\x16PjpegScanConfiguration\x12(\n" +
	"$PJPEG_SCAN_CONFIGURATION_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bPJPEG_SCAN_CONFIGURATION_WA\x10\x01\x12 \n" +
	"\x1cPJPEG_SCAN_CONFIGURATION_E15\x10\x02\x12 \n" +
	"\x1cPJPEG_SCAN_CONFIGURATION_E35\x10\x03B5Z3go.mau.fi/whatsmeow/proto/instamadilloCoreTypeMedia"

var (
	file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescOnce sync.Once
	file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescData []byte
)

func file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescGZIP() []byte {
	file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescOnce.Do(func() {
		file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDesc), len(file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDesc)))
	})
	return file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDescData
}

var file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_goTypes = []any{
	(PjpegScanConfiguration)(0),  // 0: InstamadilloCoreTypeMedia.PjpegScanConfiguration
	(Media_InterventionType)(0),  // 1: InstamadilloCoreTypeMedia.Media.InterventionType
	(Raven_ViewMode)(0),          // 2: InstamadilloCoreTypeMedia.Raven.ViewMode
	(*Media)(nil),                // 3: InstamadilloCoreTypeMedia.Media
	(*StaticPhoto)(nil),          // 4: InstamadilloCoreTypeMedia.StaticPhoto
	(*Voice)(nil),                // 5: InstamadilloCoreTypeMedia.Voice
	(*Video)(nil),                // 6: InstamadilloCoreTypeMedia.Video
	(*Gif)(nil),                  // 7: InstamadilloCoreTypeMedia.Gif
	(*AvatarSticker)(nil),        // 8: InstamadilloCoreTypeMedia.AvatarSticker
	(*Raven)(nil),                // 9: InstamadilloCoreTypeMedia.Raven
	(*RavenContent)(nil),         // 10: InstamadilloCoreTypeMedia.RavenContent
	(*Thumbnail)(nil),            // 11: InstamadilloCoreTypeMedia.Thumbnail
	(*CommonMediaTransport)(nil), // 12: InstamadilloCoreTypeMedia.CommonMediaTransport
	(*VideoExtraMetadata)(nil),   // 13: InstamadilloCoreTypeMedia.VideoExtraMetadata
}
var file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_depIdxs = []int32{
	4,  // 0: InstamadilloCoreTypeMedia.Media.staticPhoto:type_name -> InstamadilloCoreTypeMedia.StaticPhoto
	5,  // 1: InstamadilloCoreTypeMedia.Media.voice:type_name -> InstamadilloCoreTypeMedia.Voice
	6,  // 2: InstamadilloCoreTypeMedia.Media.video:type_name -> InstamadilloCoreTypeMedia.Video
	9,  // 3: InstamadilloCoreTypeMedia.Media.raven:type_name -> InstamadilloCoreTypeMedia.Raven
	7,  // 4: InstamadilloCoreTypeMedia.Media.gif:type_name -> InstamadilloCoreTypeMedia.Gif
	8,  // 5: InstamadilloCoreTypeMedia.Media.avatarSticker:type_name -> InstamadilloCoreTypeMedia.AvatarSticker
	12, // 6: InstamadilloCoreTypeMedia.StaticPhoto.mediaTransport:type_name -> InstamadilloCoreTypeMedia.CommonMediaTransport
	11, // 7: InstamadilloCoreTypeMedia.StaticPhoto.thumbnail:type_name -> InstamadilloCoreTypeMedia.Thumbnail
	0,  // 8: InstamadilloCoreTypeMedia.StaticPhoto.pjpegScanConfiguration:type_name -> InstamadilloCoreTypeMedia.PjpegScanConfiguration
	12, // 9: InstamadilloCoreTypeMedia.Voice.mediaTransport:type_name -> InstamadilloCoreTypeMedia.CommonMediaTransport
	12, // 10: InstamadilloCoreTypeMedia.Video.mediaTransport:type_name -> InstamadilloCoreTypeMedia.CommonMediaTransport
	11, // 11: InstamadilloCoreTypeMedia.Video.thumbnail:type_name -> InstamadilloCoreTypeMedia.Thumbnail
	13, // 12: InstamadilloCoreTypeMedia.Video.videoExtraMetadata:type_name -> InstamadilloCoreTypeMedia.VideoExtraMetadata
	12, // 13: InstamadilloCoreTypeMedia.Gif.mediaTransport:type_name -> InstamadilloCoreTypeMedia.CommonMediaTransport
	12, // 14: InstamadilloCoreTypeMedia.AvatarSticker.mediaTransport:type_name -> InstamadilloCoreTypeMedia.CommonMediaTransport
	2,  // 15: InstamadilloCoreTypeMedia.Raven.viewMode:type_name -> InstamadilloCoreTypeMedia.Raven.ViewMode
	10, // 16: InstamadilloCoreTypeMedia.Raven.content:type_name -> InstamadilloCoreTypeMedia.RavenContent
	4,  // 17: InstamadilloCoreTypeMedia.RavenContent.staticPhoto:type_name -> InstamadilloCoreTypeMedia.StaticPhoto
	6,  // 18: InstamadilloCoreTypeMedia.RavenContent.video:type_name -> InstamadilloCoreTypeMedia.Video
	12, // 19: InstamadilloCoreTypeMedia.Thumbnail.mediaTransport:type_name -> InstamadilloCoreTypeMedia.CommonMediaTransport
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_init() }
func file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_init() {
	if File_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto != nil {
		return
	}
	file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[0].OneofWrappers = []any{
		(*Media_StaticPhoto)(nil),
		(*Media_Voice)(nil),
		(*Media_Video)(nil),
		(*Media_Raven)(nil),
		(*Media_Gif)(nil),
		(*Media_AvatarSticker)(nil),
	}
	file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes[7].OneofWrappers = []any{
		(*RavenContent_StaticPhoto)(nil),
		(*RavenContent_Video)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDesc), len(file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_goTypes,
		DependencyIndexes: file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_depIdxs,
		EnumInfos:         file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_enumTypes,
		MessageInfos:      file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_msgTypes,
	}.Build()
	File_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto = out.File
	file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_goTypes = nil
	file_instamadilloCoreTypeMedia_InstamadilloCoreTypeMedia_proto_depIdxs = nil
}
