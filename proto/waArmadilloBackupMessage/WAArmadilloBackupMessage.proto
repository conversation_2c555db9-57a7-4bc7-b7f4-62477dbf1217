syntax = "proto2";
package WAArmadilloBackupMessage;
option go_package = "go.mau.fi/whatsmeow/proto/waArmadilloBackupMessage";

import "waArmadilloBackupCommon/WAArmadilloBackupCommon.proto";

message BackupMessage {
	message Metadata {
		message FrankingMetadata {
			optional bytes frankingTag = 3;
			optional bytes reportingTag = 4;
		}

		optional string senderID = 1;
		optional string messageID = 2;
		optional int64 timestampMS = 3;
		optional FrankingMetadata frankingMetadata = 4;
		optional int32 payloadVersion = 5;
		optional int32 futureProofBehavior = 6;
		optional int32 threadTypeTag = 7;
	}

	oneof payload {
		bytes encryptedTransportMessage = 2;
		WAArmadilloBackupCommon.Subprotocol encryptedTransportEvent = 5;
		WAArmadilloBackupCommon.Subprotocol encryptedTransportLocallyTransformedMessage = 6;
		WAArmadilloBackupCommon.Subprotocol miTransportAdminMessage = 7;
	}

	optional Metadata metadata = 1;
}
