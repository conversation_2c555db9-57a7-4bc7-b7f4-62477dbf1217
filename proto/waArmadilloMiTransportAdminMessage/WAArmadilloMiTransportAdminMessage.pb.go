// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waArmadilloMiTransportAdminMessage/WAArmadilloMiTransportAdminMessage.proto

package waArmadilloMiTransportAdminMessage

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MiTransportAdminMessage_LimitSharingChanged_SharingType int32

const (
	MiTransportAdminMessage_LimitSharingChanged_UNSET    MiTransportAdminMessage_LimitSharingChanged_SharingType = 0
	MiTransportAdminMessage_LimitSharingChanged_DISABLED MiTransportAdminMessage_LimitSharingChanged_SharingType = 1
	MiTransportAdminMessage_LimitSharingChanged_ENABLED  MiTransportAdminMessage_LimitSharingChanged_SharingType = 2
)

// Enum value maps for MiTransportAdminMessage_LimitSharingChanged_SharingType.
var (
	MiTransportAdminMessage_LimitSharingChanged_SharingType_name = map[int32]string{
		0: "UNSET",
		1: "DISABLED",
		2: "ENABLED",
	}
	MiTransportAdminMessage_LimitSharingChanged_SharingType_value = map[string]int32{
		"UNSET":    0,
		"DISABLED": 1,
		"ENABLED":  2,
	}
)

func (x MiTransportAdminMessage_LimitSharingChanged_SharingType) Enum() *MiTransportAdminMessage_LimitSharingChanged_SharingType {
	p := new(MiTransportAdminMessage_LimitSharingChanged_SharingType)
	*p = x
	return p
}

func (x MiTransportAdminMessage_LimitSharingChanged_SharingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiTransportAdminMessage_LimitSharingChanged_SharingType) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[0].Descriptor()
}

func (MiTransportAdminMessage_LimitSharingChanged_SharingType) Type() protoreflect.EnumType {
	return &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[0]
}

func (x MiTransportAdminMessage_LimitSharingChanged_SharingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MiTransportAdminMessage_LimitSharingChanged_SharingType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MiTransportAdminMessage_LimitSharingChanged_SharingType(num)
	return nil
}

// Deprecated: Use MiTransportAdminMessage_LimitSharingChanged_SharingType.Descriptor instead.
func (MiTransportAdminMessage_LimitSharingChanged_SharingType) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 0, 0}
}

type MiTransportAdminMessage_GroupImageChanged_Action int32

const (
	MiTransportAdminMessage_GroupImageChanged_UNSET   MiTransportAdminMessage_GroupImageChanged_Action = 0
	MiTransportAdminMessage_GroupImageChanged_CHANGED MiTransportAdminMessage_GroupImageChanged_Action = 1
	MiTransportAdminMessage_GroupImageChanged_REMOVED MiTransportAdminMessage_GroupImageChanged_Action = 2
)

// Enum value maps for MiTransportAdminMessage_GroupImageChanged_Action.
var (
	MiTransportAdminMessage_GroupImageChanged_Action_name = map[int32]string{
		0: "UNSET",
		1: "CHANGED",
		2: "REMOVED",
	}
	MiTransportAdminMessage_GroupImageChanged_Action_value = map[string]int32{
		"UNSET":   0,
		"CHANGED": 1,
		"REMOVED": 2,
	}
)

func (x MiTransportAdminMessage_GroupImageChanged_Action) Enum() *MiTransportAdminMessage_GroupImageChanged_Action {
	p := new(MiTransportAdminMessage_GroupImageChanged_Action)
	*p = x
	return p
}

func (x MiTransportAdminMessage_GroupImageChanged_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiTransportAdminMessage_GroupImageChanged_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[1].Descriptor()
}

func (MiTransportAdminMessage_GroupImageChanged_Action) Type() protoreflect.EnumType {
	return &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[1]
}

func (x MiTransportAdminMessage_GroupImageChanged_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MiTransportAdminMessage_GroupImageChanged_Action) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MiTransportAdminMessage_GroupImageChanged_Action(num)
	return nil
}

// Deprecated: Use MiTransportAdminMessage_GroupImageChanged_Action.Descriptor instead.
func (MiTransportAdminMessage_GroupImageChanged_Action) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 1, 0}
}

type MiTransportAdminMessage_MessagePinned_Action int32

const (
	MiTransportAdminMessage_MessagePinned_UNSET    MiTransportAdminMessage_MessagePinned_Action = 0
	MiTransportAdminMessage_MessagePinned_PINNED   MiTransportAdminMessage_MessagePinned_Action = 1
	MiTransportAdminMessage_MessagePinned_UNPINNED MiTransportAdminMessage_MessagePinned_Action = 2
)

// Enum value maps for MiTransportAdminMessage_MessagePinned_Action.
var (
	MiTransportAdminMessage_MessagePinned_Action_name = map[int32]string{
		0: "UNSET",
		1: "PINNED",
		2: "UNPINNED",
	}
	MiTransportAdminMessage_MessagePinned_Action_value = map[string]int32{
		"UNSET":    0,
		"PINNED":   1,
		"UNPINNED": 2,
	}
)

func (x MiTransportAdminMessage_MessagePinned_Action) Enum() *MiTransportAdminMessage_MessagePinned_Action {
	p := new(MiTransportAdminMessage_MessagePinned_Action)
	*p = x
	return p
}

func (x MiTransportAdminMessage_MessagePinned_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiTransportAdminMessage_MessagePinned_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[2].Descriptor()
}

func (MiTransportAdminMessage_MessagePinned_Action) Type() protoreflect.EnumType {
	return &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[2]
}

func (x MiTransportAdminMessage_MessagePinned_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MiTransportAdminMessage_MessagePinned_Action) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MiTransportAdminMessage_MessagePinned_Action(num)
	return nil
}

// Deprecated: Use MiTransportAdminMessage_MessagePinned_Action.Descriptor instead.
func (MiTransportAdminMessage_MessagePinned_Action) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 2, 0}
}

type MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode int32

const (
	MiTransportAdminMessage_GroupMembershipAddModeChanged_UNSET       MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode = 0
	MiTransportAdminMessage_GroupMembershipAddModeChanged_ALL_MEMBERS MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode = 1
	MiTransportAdminMessage_GroupMembershipAddModeChanged_ADMINS_ONLY MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode = 2
)

// Enum value maps for MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode.
var (
	MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode_name = map[int32]string{
		0: "UNSET",
		1: "ALL_MEMBERS",
		2: "ADMINS_ONLY",
	}
	MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode_value = map[string]int32{
		"UNSET":       0,
		"ALL_MEMBERS": 1,
		"ADMINS_ONLY": 2,
	}
)

func (x MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode) Enum() *MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode {
	p := new(MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode)
	*p = x
	return p
}

func (x MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[3].Descriptor()
}

func (MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode) Type() protoreflect.EnumType {
	return &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[3]
}

func (x MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode(num)
	return nil
}

// Deprecated: Use MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode.Descriptor instead.
func (MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 3, 0}
}

type MiTransportAdminMessage_GroupAdminChanged_Action int32

const (
	MiTransportAdminMessage_GroupAdminChanged_UNSET   MiTransportAdminMessage_GroupAdminChanged_Action = 0
	MiTransportAdminMessage_GroupAdminChanged_ADDED   MiTransportAdminMessage_GroupAdminChanged_Action = 1
	MiTransportAdminMessage_GroupAdminChanged_REMOVED MiTransportAdminMessage_GroupAdminChanged_Action = 2
)

// Enum value maps for MiTransportAdminMessage_GroupAdminChanged_Action.
var (
	MiTransportAdminMessage_GroupAdminChanged_Action_name = map[int32]string{
		0: "UNSET",
		1: "ADDED",
		2: "REMOVED",
	}
	MiTransportAdminMessage_GroupAdminChanged_Action_value = map[string]int32{
		"UNSET":   0,
		"ADDED":   1,
		"REMOVED": 2,
	}
)

func (x MiTransportAdminMessage_GroupAdminChanged_Action) Enum() *MiTransportAdminMessage_GroupAdminChanged_Action {
	p := new(MiTransportAdminMessage_GroupAdminChanged_Action)
	*p = x
	return p
}

func (x MiTransportAdminMessage_GroupAdminChanged_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiTransportAdminMessage_GroupAdminChanged_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[4].Descriptor()
}

func (MiTransportAdminMessage_GroupAdminChanged_Action) Type() protoreflect.EnumType {
	return &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[4]
}

func (x MiTransportAdminMessage_GroupAdminChanged_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MiTransportAdminMessage_GroupAdminChanged_Action) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MiTransportAdminMessage_GroupAdminChanged_Action(num)
	return nil
}

// Deprecated: Use MiTransportAdminMessage_GroupAdminChanged_Action.Descriptor instead.
func (MiTransportAdminMessage_GroupAdminChanged_Action) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 4, 0}
}

type MiTransportAdminMessage_GroupParticipantChanged_Action int32

const (
	MiTransportAdminMessage_GroupParticipantChanged_UNSET   MiTransportAdminMessage_GroupParticipantChanged_Action = 0
	MiTransportAdminMessage_GroupParticipantChanged_ADDED   MiTransportAdminMessage_GroupParticipantChanged_Action = 1
	MiTransportAdminMessage_GroupParticipantChanged_REMOVED MiTransportAdminMessage_GroupParticipantChanged_Action = 2
)

// Enum value maps for MiTransportAdminMessage_GroupParticipantChanged_Action.
var (
	MiTransportAdminMessage_GroupParticipantChanged_Action_name = map[int32]string{
		0: "UNSET",
		1: "ADDED",
		2: "REMOVED",
	}
	MiTransportAdminMessage_GroupParticipantChanged_Action_value = map[string]int32{
		"UNSET":   0,
		"ADDED":   1,
		"REMOVED": 2,
	}
)

func (x MiTransportAdminMessage_GroupParticipantChanged_Action) Enum() *MiTransportAdminMessage_GroupParticipantChanged_Action {
	p := new(MiTransportAdminMessage_GroupParticipantChanged_Action)
	*p = x
	return p
}

func (x MiTransportAdminMessage_GroupParticipantChanged_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MiTransportAdminMessage_GroupParticipantChanged_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[5].Descriptor()
}

func (MiTransportAdminMessage_GroupParticipantChanged_Action) Type() protoreflect.EnumType {
	return &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes[5]
}

func (x MiTransportAdminMessage_GroupParticipantChanged_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MiTransportAdminMessage_GroupParticipantChanged_Action) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MiTransportAdminMessage_GroupParticipantChanged_Action(num)
	return nil
}

// Deprecated: Use MiTransportAdminMessage_GroupParticipantChanged_Action.Descriptor instead.
func (MiTransportAdminMessage_GroupParticipantChanged_Action) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 5, 0}
}

type MiTransportAdminMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Content:
	//
	//	*MiTransportAdminMessage_ChatThemeChanged_
	//	*MiTransportAdminMessage_NicknameChanged_
	//	*MiTransportAdminMessage_GroupParticipantChanged_
	//	*MiTransportAdminMessage_GroupAdminChanged_
	//	*MiTransportAdminMessage_GroupNameChanged_
	//	*MiTransportAdminMessage_GroupMembershipAddModeChanged_
	//	*MiTransportAdminMessage_MessagePinned_
	//	*MiTransportAdminMessage_GroupImageChanged_
	//	*MiTransportAdminMessage_QuickReactionChanged_
	//	*MiTransportAdminMessage_LinkCta_
	//	*MiTransportAdminMessage_IconChanged_
	//	*MiTransportAdminMessage_DisappearingSettingChanged_
	//	*MiTransportAdminMessage_LimitSharingChanged_
	Content       isMiTransportAdminMessage_Content `protobuf_oneof:"content"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage) Reset() {
	*x = MiTransportAdminMessage{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage) ProtoMessage() {}

func (x *MiTransportAdminMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0}
}

func (x *MiTransportAdminMessage) GetContent() isMiTransportAdminMessage_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *MiTransportAdminMessage) GetChatThemeChanged() *MiTransportAdminMessage_ChatThemeChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_ChatThemeChanged_); ok {
			return x.ChatThemeChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetNicknameChanged() *MiTransportAdminMessage_NicknameChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_NicknameChanged_); ok {
			return x.NicknameChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetGroupParticipantChanged() *MiTransportAdminMessage_GroupParticipantChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_GroupParticipantChanged_); ok {
			return x.GroupParticipantChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetGroupAdminChanged() *MiTransportAdminMessage_GroupAdminChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_GroupAdminChanged_); ok {
			return x.GroupAdminChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetGroupNameChanged() *MiTransportAdminMessage_GroupNameChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_GroupNameChanged_); ok {
			return x.GroupNameChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetGroupMembershipAddModeChanged() *MiTransportAdminMessage_GroupMembershipAddModeChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_GroupMembershipAddModeChanged_); ok {
			return x.GroupMembershipAddModeChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetMessagePinned() *MiTransportAdminMessage_MessagePinned {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_MessagePinned_); ok {
			return x.MessagePinned
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetGroupImageChanged() *MiTransportAdminMessage_GroupImageChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_GroupImageChanged_); ok {
			return x.GroupImageChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetQuickReactionChanged() *MiTransportAdminMessage_QuickReactionChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_QuickReactionChanged_); ok {
			return x.QuickReactionChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetLinkCta() *MiTransportAdminMessage_LinkCta {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_LinkCta_); ok {
			return x.LinkCta
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetIconChanged() *MiTransportAdminMessage_IconChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_IconChanged_); ok {
			return x.IconChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetDisappearingSettingChanged() *MiTransportAdminMessage_DisappearingSettingChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_DisappearingSettingChanged_); ok {
			return x.DisappearingSettingChanged
		}
	}
	return nil
}

func (x *MiTransportAdminMessage) GetLimitSharingChanged() *MiTransportAdminMessage_LimitSharingChanged {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_LimitSharingChanged_); ok {
			return x.LimitSharingChanged
		}
	}
	return nil
}

type isMiTransportAdminMessage_Content interface {
	isMiTransportAdminMessage_Content()
}

type MiTransportAdminMessage_ChatThemeChanged_ struct {
	ChatThemeChanged *MiTransportAdminMessage_ChatThemeChanged `protobuf:"bytes,1,opt,name=chatThemeChanged,oneof"`
}

type MiTransportAdminMessage_NicknameChanged_ struct {
	NicknameChanged *MiTransportAdminMessage_NicknameChanged `protobuf:"bytes,2,opt,name=nicknameChanged,oneof"`
}

type MiTransportAdminMessage_GroupParticipantChanged_ struct {
	GroupParticipantChanged *MiTransportAdminMessage_GroupParticipantChanged `protobuf:"bytes,3,opt,name=groupParticipantChanged,oneof"`
}

type MiTransportAdminMessage_GroupAdminChanged_ struct {
	GroupAdminChanged *MiTransportAdminMessage_GroupAdminChanged `protobuf:"bytes,4,opt,name=groupAdminChanged,oneof"`
}

type MiTransportAdminMessage_GroupNameChanged_ struct {
	GroupNameChanged *MiTransportAdminMessage_GroupNameChanged `protobuf:"bytes,5,opt,name=groupNameChanged,oneof"`
}

type MiTransportAdminMessage_GroupMembershipAddModeChanged_ struct {
	GroupMembershipAddModeChanged *MiTransportAdminMessage_GroupMembershipAddModeChanged `protobuf:"bytes,6,opt,name=groupMembershipAddModeChanged,oneof"`
}

type MiTransportAdminMessage_MessagePinned_ struct {
	MessagePinned *MiTransportAdminMessage_MessagePinned `protobuf:"bytes,7,opt,name=messagePinned,oneof"`
}

type MiTransportAdminMessage_GroupImageChanged_ struct {
	GroupImageChanged *MiTransportAdminMessage_GroupImageChanged `protobuf:"bytes,8,opt,name=groupImageChanged,oneof"`
}

type MiTransportAdminMessage_QuickReactionChanged_ struct {
	QuickReactionChanged *MiTransportAdminMessage_QuickReactionChanged `protobuf:"bytes,9,opt,name=quickReactionChanged,oneof"`
}

type MiTransportAdminMessage_LinkCta_ struct {
	LinkCta *MiTransportAdminMessage_LinkCta `protobuf:"bytes,10,opt,name=linkCta,oneof"`
}

type MiTransportAdminMessage_IconChanged_ struct {
	IconChanged *MiTransportAdminMessage_IconChanged `protobuf:"bytes,11,opt,name=iconChanged,oneof"`
}

type MiTransportAdminMessage_DisappearingSettingChanged_ struct {
	DisappearingSettingChanged *MiTransportAdminMessage_DisappearingSettingChanged `protobuf:"bytes,12,opt,name=disappearingSettingChanged,oneof"`
}

type MiTransportAdminMessage_LimitSharingChanged_ struct {
	LimitSharingChanged *MiTransportAdminMessage_LimitSharingChanged `protobuf:"bytes,13,opt,name=limitSharingChanged,oneof"`
}

func (*MiTransportAdminMessage_ChatThemeChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_NicknameChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_GroupParticipantChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_GroupAdminChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_GroupNameChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_GroupMembershipAddModeChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_MessagePinned_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_GroupImageChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_QuickReactionChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_LinkCta_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_IconChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_DisappearingSettingChanged_) isMiTransportAdminMessage_Content() {}

func (*MiTransportAdminMessage_LimitSharingChanged_) isMiTransportAdminMessage_Content() {}

type MiTransportAdminMessage_LimitSharingChanged struct {
	state         protoimpl.MessageState                                   `protogen:"open.v1"`
	SharingType   *MiTransportAdminMessage_LimitSharingChanged_SharingType `protobuf:"varint,1,opt,name=sharingType,enum=WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage_LimitSharingChanged_SharingType" json:"sharingType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_LimitSharingChanged) Reset() {
	*x = MiTransportAdminMessage_LimitSharingChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_LimitSharingChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_LimitSharingChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_LimitSharingChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_LimitSharingChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_LimitSharingChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 0}
}

func (x *MiTransportAdminMessage_LimitSharingChanged) GetSharingType() MiTransportAdminMessage_LimitSharingChanged_SharingType {
	if x != nil && x.SharingType != nil {
		return *x.SharingType
	}
	return MiTransportAdminMessage_LimitSharingChanged_UNSET
}

type MiTransportAdminMessage_GroupImageChanged struct {
	state         protoimpl.MessageState                            `protogen:"open.v1"`
	Action        *MiTransportAdminMessage_GroupImageChanged_Action `protobuf:"varint,1,opt,name=action,enum=WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage_GroupImageChanged_Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_GroupImageChanged) Reset() {
	*x = MiTransportAdminMessage_GroupImageChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_GroupImageChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_GroupImageChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_GroupImageChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_GroupImageChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_GroupImageChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 1}
}

func (x *MiTransportAdminMessage_GroupImageChanged) GetAction() MiTransportAdminMessage_GroupImageChanged_Action {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return MiTransportAdminMessage_GroupImageChanged_UNSET
}

type MiTransportAdminMessage_MessagePinned struct {
	state         protoimpl.MessageState                        `protogen:"open.v1"`
	Action        *MiTransportAdminMessage_MessagePinned_Action `protobuf:"varint,1,opt,name=action,enum=WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage_MessagePinned_Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_MessagePinned) Reset() {
	*x = MiTransportAdminMessage_MessagePinned{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_MessagePinned) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_MessagePinned) ProtoMessage() {}

func (x *MiTransportAdminMessage_MessagePinned) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_MessagePinned.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_MessagePinned) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 2}
}

func (x *MiTransportAdminMessage_MessagePinned) GetAction() MiTransportAdminMessage_MessagePinned_Action {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return MiTransportAdminMessage_MessagePinned_UNSET
}

type MiTransportAdminMessage_GroupMembershipAddModeChanged struct {
	state         protoimpl.MessageState                                      `protogen:"open.v1"`
	Mode          *MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode `protobuf:"varint,1,opt,name=mode,enum=WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode" json:"mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_GroupMembershipAddModeChanged) Reset() {
	*x = MiTransportAdminMessage_GroupMembershipAddModeChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_GroupMembershipAddModeChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_GroupMembershipAddModeChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_GroupMembershipAddModeChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_GroupMembershipAddModeChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_GroupMembershipAddModeChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 3}
}

func (x *MiTransportAdminMessage_GroupMembershipAddModeChanged) GetMode() MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return MiTransportAdminMessage_GroupMembershipAddModeChanged_UNSET
}

type MiTransportAdminMessage_GroupAdminChanged struct {
	state         protoimpl.MessageState                            `protogen:"open.v1"`
	TargetUserID  []string                                          `protobuf:"bytes,1,rep,name=targetUserID" json:"targetUserID,omitempty"`
	Action        *MiTransportAdminMessage_GroupAdminChanged_Action `protobuf:"varint,2,opt,name=action,enum=WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage_GroupAdminChanged_Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_GroupAdminChanged) Reset() {
	*x = MiTransportAdminMessage_GroupAdminChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_GroupAdminChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_GroupAdminChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_GroupAdminChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_GroupAdminChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_GroupAdminChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 4}
}

func (x *MiTransportAdminMessage_GroupAdminChanged) GetTargetUserID() []string {
	if x != nil {
		return x.TargetUserID
	}
	return nil
}

func (x *MiTransportAdminMessage_GroupAdminChanged) GetAction() MiTransportAdminMessage_GroupAdminChanged_Action {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return MiTransportAdminMessage_GroupAdminChanged_UNSET
}

type MiTransportAdminMessage_GroupParticipantChanged struct {
	state         protoimpl.MessageState                                  `protogen:"open.v1"`
	TargetUserID  []string                                                `protobuf:"bytes,1,rep,name=targetUserID" json:"targetUserID,omitempty"`
	Action        *MiTransportAdminMessage_GroupParticipantChanged_Action `protobuf:"varint,2,opt,name=action,enum=WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage_GroupParticipantChanged_Action" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_GroupParticipantChanged) Reset() {
	*x = MiTransportAdminMessage_GroupParticipantChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_GroupParticipantChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_GroupParticipantChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_GroupParticipantChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_GroupParticipantChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_GroupParticipantChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 5}
}

func (x *MiTransportAdminMessage_GroupParticipantChanged) GetTargetUserID() []string {
	if x != nil {
		return x.TargetUserID
	}
	return nil
}

func (x *MiTransportAdminMessage_GroupParticipantChanged) GetAction() MiTransportAdminMessage_GroupParticipantChanged_Action {
	if x != nil && x.Action != nil {
		return *x.Action
	}
	return MiTransportAdminMessage_GroupParticipantChanged_UNSET
}

type MiTransportAdminMessage_DisappearingSettingChanged struct {
	state                                 protoimpl.MessageState `protogen:"open.v1"`
	DisappearingSettingDurationSeconds    *int32                 `protobuf:"varint,1,opt,name=disappearingSettingDurationSeconds" json:"disappearingSettingDurationSeconds,omitempty"`
	OldDisappearingSettingDurationSeconds *int32                 `protobuf:"varint,2,opt,name=oldDisappearingSettingDurationSeconds" json:"oldDisappearingSettingDurationSeconds,omitempty"`
	unknownFields                         protoimpl.UnknownFields
	sizeCache                             protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_DisappearingSettingChanged) Reset() {
	*x = MiTransportAdminMessage_DisappearingSettingChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_DisappearingSettingChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_DisappearingSettingChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_DisappearingSettingChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_DisappearingSettingChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_DisappearingSettingChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 6}
}

func (x *MiTransportAdminMessage_DisappearingSettingChanged) GetDisappearingSettingDurationSeconds() int32 {
	if x != nil && x.DisappearingSettingDurationSeconds != nil {
		return *x.DisappearingSettingDurationSeconds
	}
	return 0
}

func (x *MiTransportAdminMessage_DisappearingSettingChanged) GetOldDisappearingSettingDurationSeconds() int32 {
	if x != nil && x.OldDisappearingSettingDurationSeconds != nil {
		return *x.OldDisappearingSettingDurationSeconds
	}
	return 0
}

type MiTransportAdminMessage_IconChanged struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadIcon    *string                `protobuf:"bytes,1,opt,name=threadIcon" json:"threadIcon,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_IconChanged) Reset() {
	*x = MiTransportAdminMessage_IconChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_IconChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_IconChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_IconChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_IconChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_IconChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 7}
}

func (x *MiTransportAdminMessage_IconChanged) GetThreadIcon() string {
	if x != nil && x.ThreadIcon != nil {
		return *x.ThreadIcon
	}
	return ""
}

type MiTransportAdminMessage_LinkCta struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Content:
	//
	//	*MiTransportAdminMessage_LinkCta_UkOsaAdminText_
	Content       isMiTransportAdminMessage_LinkCta_Content `protobuf_oneof:"content"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_LinkCta) Reset() {
	*x = MiTransportAdminMessage_LinkCta{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_LinkCta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_LinkCta) ProtoMessage() {}

func (x *MiTransportAdminMessage_LinkCta) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_LinkCta.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_LinkCta) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 8}
}

func (x *MiTransportAdminMessage_LinkCta) GetContent() isMiTransportAdminMessage_LinkCta_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *MiTransportAdminMessage_LinkCta) GetUkOsaAdminText() *MiTransportAdminMessage_LinkCta_UkOsaAdminText {
	if x != nil {
		if x, ok := x.Content.(*MiTransportAdminMessage_LinkCta_UkOsaAdminText_); ok {
			return x.UkOsaAdminText
		}
	}
	return nil
}

type isMiTransportAdminMessage_LinkCta_Content interface {
	isMiTransportAdminMessage_LinkCta_Content()
}

type MiTransportAdminMessage_LinkCta_UkOsaAdminText_ struct {
	UkOsaAdminText *MiTransportAdminMessage_LinkCta_UkOsaAdminText `protobuf:"bytes,1,opt,name=ukOsaAdminText,oneof"`
}

func (*MiTransportAdminMessage_LinkCta_UkOsaAdminText_) isMiTransportAdminMessage_LinkCta_Content() {}

type MiTransportAdminMessage_QuickReactionChanged struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EmojiName     *string                `protobuf:"bytes,1,opt,name=emojiName" json:"emojiName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_QuickReactionChanged) Reset() {
	*x = MiTransportAdminMessage_QuickReactionChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_QuickReactionChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_QuickReactionChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_QuickReactionChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_QuickReactionChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_QuickReactionChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 9}
}

func (x *MiTransportAdminMessage_QuickReactionChanged) GetEmojiName() string {
	if x != nil && x.EmojiName != nil {
		return *x.EmojiName
	}
	return ""
}

type MiTransportAdminMessage_GroupNameChanged struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GroupName     *string                `protobuf:"bytes,1,opt,name=groupName" json:"groupName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_GroupNameChanged) Reset() {
	*x = MiTransportAdminMessage_GroupNameChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_GroupNameChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_GroupNameChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_GroupNameChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_GroupNameChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_GroupNameChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 10}
}

func (x *MiTransportAdminMessage_GroupNameChanged) GetGroupName() string {
	if x != nil && x.GroupName != nil {
		return *x.GroupName
	}
	return ""
}

type MiTransportAdminMessage_NicknameChanged struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TargetUserID  *string                `protobuf:"bytes,1,opt,name=targetUserID" json:"targetUserID,omitempty"`
	Nickname      *string                `protobuf:"bytes,2,opt,name=nickname" json:"nickname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_NicknameChanged) Reset() {
	*x = MiTransportAdminMessage_NicknameChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_NicknameChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_NicknameChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_NicknameChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_NicknameChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_NicknameChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 11}
}

func (x *MiTransportAdminMessage_NicknameChanged) GetTargetUserID() string {
	if x != nil && x.TargetUserID != nil {
		return *x.TargetUserID
	}
	return ""
}

func (x *MiTransportAdminMessage_NicknameChanged) GetNickname() string {
	if x != nil && x.Nickname != nil {
		return *x.Nickname
	}
	return ""
}

type MiTransportAdminMessage_ChatThemeChanged struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThemeName     *string                `protobuf:"bytes,1,opt,name=themeName" json:"themeName,omitempty"`
	ThemeEmoji    *string                `protobuf:"bytes,2,opt,name=themeEmoji" json:"themeEmoji,omitempty"`
	ThemeType     *int32                 `protobuf:"varint,3,opt,name=themeType" json:"themeType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_ChatThemeChanged) Reset() {
	*x = MiTransportAdminMessage_ChatThemeChanged{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_ChatThemeChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_ChatThemeChanged) ProtoMessage() {}

func (x *MiTransportAdminMessage_ChatThemeChanged) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_ChatThemeChanged.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_ChatThemeChanged) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 12}
}

func (x *MiTransportAdminMessage_ChatThemeChanged) GetThemeName() string {
	if x != nil && x.ThemeName != nil {
		return *x.ThemeName
	}
	return ""
}

func (x *MiTransportAdminMessage_ChatThemeChanged) GetThemeEmoji() string {
	if x != nil && x.ThemeEmoji != nil {
		return *x.ThemeEmoji
	}
	return ""
}

func (x *MiTransportAdminMessage_ChatThemeChanged) GetThemeType() int32 {
	if x != nil && x.ThemeType != nil {
		return *x.ThemeType
	}
	return 0
}

type MiTransportAdminMessage_LinkCta_UkOsaAdminText struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	InitiatorUserID *string                `protobuf:"bytes,2,opt,name=initiatorUserID" json:"initiatorUserID,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MiTransportAdminMessage_LinkCta_UkOsaAdminText) Reset() {
	*x = MiTransportAdminMessage_LinkCta_UkOsaAdminText{}
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MiTransportAdminMessage_LinkCta_UkOsaAdminText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiTransportAdminMessage_LinkCta_UkOsaAdminText) ProtoMessage() {}

func (x *MiTransportAdminMessage_LinkCta_UkOsaAdminText) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiTransportAdminMessage_LinkCta_UkOsaAdminText.ProtoReflect.Descriptor instead.
func (*MiTransportAdminMessage_LinkCta_UkOsaAdminText) Descriptor() ([]byte, []int) {
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP(), []int{0, 8, 0}
}

func (x *MiTransportAdminMessage_LinkCta_UkOsaAdminText) GetInitiatorUserID() string {
	if x != nil && x.InitiatorUserID != nil {
		return *x.InitiatorUserID
	}
	return ""
}

var File_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto protoreflect.FileDescriptor

const file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDesc = "" +
	"\n" +
	"KwaArmadilloMiTransportAdminMessage/WAArmadilloMiTransportAdminMessage.proto\x12\"WAArmadilloMiTransportAdminMessage\"\xcf\x1c\n" +
	"\x17MiTransportAdminMessage\x12z\n" +
	"\x10chatThemeChanged\x18\x01 \x01(\v2L.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.ChatThemeChangedH\x00R\x10chatThemeChanged\x12w\n" +
	"\x0fnicknameChanged\x18\x02 \x01(\v2K.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.NicknameChangedH\x00R\x0fnicknameChanged\x12\x8f\x01\n" +
	"\x17groupParticipantChanged\x18\x03 \x01(\v2S.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupParticipantChangedH\x00R\x17groupParticipantChanged\x12}\n" +
	"\x11groupAdminChanged\x18\x04 \x01(\v2M.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupAdminChangedH\x00R\x11groupAdminChanged\x12z\n" +
	"\x10groupNameChanged\x18\x05 \x01(\v2L.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupNameChangedH\x00R\x10groupNameChanged\x12\xa1\x01\n" +
	"\x1dgroupMembershipAddModeChanged\x18\x06 \x01(\v2Y.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupMembershipAddModeChangedH\x00R\x1dgroupMembershipAddModeChanged\x12q\n" +
	"\rmessagePinned\x18\a \x01(\v2I.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.MessagePinnedH\x00R\rmessagePinned\x12}\n" +
	"\x11groupImageChanged\x18\b \x01(\v2M.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupImageChangedH\x00R\x11groupImageChanged\x12\x86\x01\n" +
	"\x14quickReactionChanged\x18\t \x01(\v2P.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.QuickReactionChangedH\x00R\x14quickReactionChanged\x12_\n" +
	"\alinkCta\x18\n" +
	" \x01(\v2C.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LinkCtaH\x00R\alinkCta\x12k\n" +
	"\viconChanged\x18\v \x01(\v2G.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.IconChangedH\x00R\viconChanged\x12\x98\x01\n" +
	"\x1adisappearingSettingChanged\x18\f \x01(\v2V.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.DisappearingSettingChangedH\x00R\x1adisappearingSettingChanged\x12\x83\x01\n" +
	"\x13limitSharingChanged\x18\r \x01(\v2O.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LimitSharingChangedH\x00R\x13limitSharingChanged\x1a\xc9\x01\n" +
	"\x13LimitSharingChanged\x12}\n" +
	"\vsharingType\x18\x01 \x01(\x0e2[.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LimitSharingChanged.SharingTypeR\vsharingType\"3\n" +
	"\vSharingType\x12\t\n" +
	"\x05UNSET\x10\x00\x12\f\n" +
	"\bDISABLED\x10\x01\x12\v\n" +
	"\aENABLED\x10\x02\x1a\xb0\x01\n" +
	"\x11GroupImageChanged\x12l\n" +
	"\x06action\x18\x01 \x01(\x0e2T.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupImageChanged.ActionR\x06action\"-\n" +
	"\x06Action\x12\t\n" +
	"\x05UNSET\x10\x00\x12\v\n" +
	"\aCHANGED\x10\x01\x12\v\n" +
	"\aREMOVED\x10\x02\x1a\xa8\x01\n" +
	"\rMessagePinned\x12h\n" +
	"\x06action\x18\x01 \x01(\x0e2P.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.MessagePinned.ActionR\x06action\"-\n" +
	"\x06Action\x12\t\n" +
	"\x05UNSET\x10\x00\x12\n" +
	"\n" +
	"\x06PINNED\x10\x01\x12\f\n" +
	"\bUNPINNED\x10\x02\x1a\xc8\x01\n" +
	"\x1dGroupMembershipAddModeChanged\x12r\n" +
	"\x04mode\x18\x01 \x01(\x0e2^.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupMembershipAddModeChanged.ModeR\x04mode\"3\n" +
	"\x04Mode\x12\t\n" +
	"\x05UNSET\x10\x00\x12\x0f\n" +
	"\vALL_MEMBERS\x10\x01\x12\x0f\n" +
	"\vADMINS_ONLY\x10\x02\x1a\xd2\x01\n" +
	"\x11GroupAdminChanged\x12\"\n" +
	"\ftargetUserID\x18\x01 \x03(\tR\ftargetUserID\x12l\n" +
	"\x06action\x18\x02 \x01(\x0e2T.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupAdminChanged.ActionR\x06action\"+\n" +
	"\x06Action\x12\t\n" +
	"\x05UNSET\x10\x00\x12\t\n" +
	"\x05ADDED\x10\x01\x12\v\n" +
	"\aREMOVED\x10\x02\x1a\xde\x01\n" +
	"\x17GroupParticipantChanged\x12\"\n" +
	"\ftargetUserID\x18\x01 \x03(\tR\ftargetUserID\x12r\n" +
	"\x06action\x18\x02 \x01(\x0e2Z.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupParticipantChanged.ActionR\x06action\"+\n" +
	"\x06Action\x12\t\n" +
	"\x05UNSET\x10\x00\x12\t\n" +
	"\x05ADDED\x10\x01\x12\v\n" +
	"\aREMOVED\x10\x02\x1a\xc2\x01\n" +
	"\x1aDisappearingSettingChanged\x12N\n" +
	"\"disappearingSettingDurationSeconds\x18\x01 \x01(\x05R\"disappearingSettingDurationSeconds\x12T\n" +
	"%oldDisappearingSettingDurationSeconds\x18\x02 \x01(\x05R%oldDisappearingSettingDurationSeconds\x1a-\n" +
	"\vIconChanged\x12\x1e\n" +
	"\n" +
	"threadIcon\x18\x01 \x01(\tR\n" +
	"threadIcon\x1a\xce\x01\n" +
	"\aLinkCta\x12|\n" +
	"\x0eukOsaAdminText\x18\x01 \x01(\v2R.WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LinkCta.UkOsaAdminTextH\x00R\x0eukOsaAdminText\x1a:\n" +
	"\x0eUkOsaAdminText\x12(\n" +
	"\x0finitiatorUserID\x18\x02 \x01(\tR\x0finitiatorUserIDB\t\n" +
	"\acontent\x1a4\n" +
	"\x14QuickReactionChanged\x12\x1c\n" +
	"\temojiName\x18\x01 \x01(\tR\temojiName\x1a0\n" +
	"\x10GroupNameChanged\x12\x1c\n" +
	"\tgroupName\x18\x01 \x01(\tR\tgroupName\x1aQ\n" +
	"\x0fNicknameChanged\x12\"\n" +
	"\ftargetUserID\x18\x01 \x01(\tR\ftargetUserID\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\x1an\n" +
	"\x10ChatThemeChanged\x12\x1c\n" +
	"\tthemeName\x18\x01 \x01(\tR\tthemeName\x12\x1e\n" +
	"\n" +
	"themeEmoji\x18\x02 \x01(\tR\n" +
	"themeEmoji\x12\x1c\n" +
	"\tthemeType\x18\x03 \x01(\x05R\tthemeTypeB\t\n" +
	"\acontentB>Z<go.mau.fi/whatsmeow/proto/waArmadilloMiTransportAdminMessage"

var (
	file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescOnce sync.Once
	file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescData []byte
)

func file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescGZIP() []byte {
	file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescOnce.Do(func() {
		file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDesc), len(file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDesc)))
	})
	return file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDescData
}

var file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_goTypes = []any{
	(MiTransportAdminMessage_LimitSharingChanged_SharingType)(0),    // 0: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LimitSharingChanged.SharingType
	(MiTransportAdminMessage_GroupImageChanged_Action)(0),           // 1: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupImageChanged.Action
	(MiTransportAdminMessage_MessagePinned_Action)(0),               // 2: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.MessagePinned.Action
	(MiTransportAdminMessage_GroupMembershipAddModeChanged_Mode)(0), // 3: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupMembershipAddModeChanged.Mode
	(MiTransportAdminMessage_GroupAdminChanged_Action)(0),           // 4: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupAdminChanged.Action
	(MiTransportAdminMessage_GroupParticipantChanged_Action)(0),     // 5: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupParticipantChanged.Action
	(*MiTransportAdminMessage)(nil),                                 // 6: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage
	(*MiTransportAdminMessage_LimitSharingChanged)(nil),             // 7: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LimitSharingChanged
	(*MiTransportAdminMessage_GroupImageChanged)(nil),               // 8: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupImageChanged
	(*MiTransportAdminMessage_MessagePinned)(nil),                   // 9: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.MessagePinned
	(*MiTransportAdminMessage_GroupMembershipAddModeChanged)(nil),   // 10: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupMembershipAddModeChanged
	(*MiTransportAdminMessage_GroupAdminChanged)(nil),               // 11: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupAdminChanged
	(*MiTransportAdminMessage_GroupParticipantChanged)(nil),         // 12: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupParticipantChanged
	(*MiTransportAdminMessage_DisappearingSettingChanged)(nil),      // 13: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.DisappearingSettingChanged
	(*MiTransportAdminMessage_IconChanged)(nil),                     // 14: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.IconChanged
	(*MiTransportAdminMessage_LinkCta)(nil),                         // 15: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LinkCta
	(*MiTransportAdminMessage_QuickReactionChanged)(nil),            // 16: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.QuickReactionChanged
	(*MiTransportAdminMessage_GroupNameChanged)(nil),                // 17: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupNameChanged
	(*MiTransportAdminMessage_NicknameChanged)(nil),                 // 18: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.NicknameChanged
	(*MiTransportAdminMessage_ChatThemeChanged)(nil),                // 19: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.ChatThemeChanged
	(*MiTransportAdminMessage_LinkCta_UkOsaAdminText)(nil),          // 20: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LinkCta.UkOsaAdminText
}
var file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_depIdxs = []int32{
	19, // 0: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.chatThemeChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.ChatThemeChanged
	18, // 1: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.nicknameChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.NicknameChanged
	12, // 2: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.groupParticipantChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupParticipantChanged
	11, // 3: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.groupAdminChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupAdminChanged
	17, // 4: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.groupNameChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupNameChanged
	10, // 5: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.groupMembershipAddModeChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupMembershipAddModeChanged
	9,  // 6: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.messagePinned:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.MessagePinned
	8,  // 7: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.groupImageChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupImageChanged
	16, // 8: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.quickReactionChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.QuickReactionChanged
	15, // 9: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.linkCta:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LinkCta
	14, // 10: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.iconChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.IconChanged
	13, // 11: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.disappearingSettingChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.DisappearingSettingChanged
	7,  // 12: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.limitSharingChanged:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LimitSharingChanged
	0,  // 13: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LimitSharingChanged.sharingType:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LimitSharingChanged.SharingType
	1,  // 14: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupImageChanged.action:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupImageChanged.Action
	2,  // 15: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.MessagePinned.action:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.MessagePinned.Action
	3,  // 16: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupMembershipAddModeChanged.mode:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupMembershipAddModeChanged.Mode
	4,  // 17: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupAdminChanged.action:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupAdminChanged.Action
	5,  // 18: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupParticipantChanged.action:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.GroupParticipantChanged.Action
	20, // 19: WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LinkCta.ukOsaAdminText:type_name -> WAArmadilloMiTransportAdminMessage.MiTransportAdminMessage.LinkCta.UkOsaAdminText
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_init() }
func file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_init() {
	if File_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto != nil {
		return
	}
	file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[0].OneofWrappers = []any{
		(*MiTransportAdminMessage_ChatThemeChanged_)(nil),
		(*MiTransportAdminMessage_NicknameChanged_)(nil),
		(*MiTransportAdminMessage_GroupParticipantChanged_)(nil),
		(*MiTransportAdminMessage_GroupAdminChanged_)(nil),
		(*MiTransportAdminMessage_GroupNameChanged_)(nil),
		(*MiTransportAdminMessage_GroupMembershipAddModeChanged_)(nil),
		(*MiTransportAdminMessage_MessagePinned_)(nil),
		(*MiTransportAdminMessage_GroupImageChanged_)(nil),
		(*MiTransportAdminMessage_QuickReactionChanged_)(nil),
		(*MiTransportAdminMessage_LinkCta_)(nil),
		(*MiTransportAdminMessage_IconChanged_)(nil),
		(*MiTransportAdminMessage_DisappearingSettingChanged_)(nil),
		(*MiTransportAdminMessage_LimitSharingChanged_)(nil),
	}
	file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes[9].OneofWrappers = []any{
		(*MiTransportAdminMessage_LinkCta_UkOsaAdminText_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDesc), len(file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_rawDesc)),
			NumEnums:      6,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_goTypes,
		DependencyIndexes: file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_depIdxs,
		EnumInfos:         file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_enumTypes,
		MessageInfos:      file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_msgTypes,
	}.Build()
	File_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto = out.File
	file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_goTypes = nil
	file_waArmadilloMiTransportAdminMessage_WAArmadilloMiTransportAdminMessage_proto_depIdxs = nil
}
