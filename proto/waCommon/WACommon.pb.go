// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waCommon/WACommon.proto

package waCommon

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FutureProofBehavior int32

const (
	FutureProofBehavior_PLACEHOLDER    FutureProofBehavior = 0
	FutureProofBehavior_NO_PLACEHOLDER FutureProofBehavior = 1
	FutureProofBehavior_IGNORE         FutureProofBehavior = 2
)

// Enum value maps for FutureProofBehavior.
var (
	FutureProofBehavior_name = map[int32]string{
		0: "PLACEHOLDER",
		1: "NO_PLACEHOLDER",
		2: "IGNORE",
	}
	FutureProofBehavior_value = map[string]int32{
		"PLACEHOLDER":    0,
		"NO_PLACEHOLDER": 1,
		"IGNORE":         2,
	}
)

func (x FutureProofBehavior) Enum() *FutureProofBehavior {
	p := new(FutureProofBehavior)
	*p = x
	return p
}

func (x FutureProofBehavior) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FutureProofBehavior) Descriptor() protoreflect.EnumDescriptor {
	return file_waCommon_WACommon_proto_enumTypes[0].Descriptor()
}

func (FutureProofBehavior) Type() protoreflect.EnumType {
	return &file_waCommon_WACommon_proto_enumTypes[0]
}

func (x FutureProofBehavior) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *FutureProofBehavior) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = FutureProofBehavior(num)
	return nil
}

// Deprecated: Use FutureProofBehavior.Descriptor instead.
func (FutureProofBehavior) EnumDescriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{0}
}

type Command_CommandType int32

const (
	Command_EVERYONE   Command_CommandType = 1
	Command_SILENT     Command_CommandType = 2
	Command_AI         Command_CommandType = 3
	Command_AI_IMAGINE Command_CommandType = 4
)

// Enum value maps for Command_CommandType.
var (
	Command_CommandType_name = map[int32]string{
		1: "EVERYONE",
		2: "SILENT",
		3: "AI",
		4: "AI_IMAGINE",
	}
	Command_CommandType_value = map[string]int32{
		"EVERYONE":   1,
		"SILENT":     2,
		"AI":         3,
		"AI_IMAGINE": 4,
	}
)

func (x Command_CommandType) Enum() *Command_CommandType {
	p := new(Command_CommandType)
	*p = x
	return p
}

func (x Command_CommandType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Command_CommandType) Descriptor() protoreflect.EnumDescriptor {
	return file_waCommon_WACommon_proto_enumTypes[1].Descriptor()
}

func (Command_CommandType) Type() protoreflect.EnumType {
	return &file_waCommon_WACommon_proto_enumTypes[1]
}

func (x Command_CommandType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Command_CommandType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Command_CommandType(num)
	return nil
}

// Deprecated: Use Command_CommandType.Descriptor instead.
func (Command_CommandType) EnumDescriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{1, 0}
}

type Mention_MentionType int32

const (
	Mention_PROFILE Mention_MentionType = 0
)

// Enum value maps for Mention_MentionType.
var (
	Mention_MentionType_name = map[int32]string{
		0: "PROFILE",
	}
	Mention_MentionType_value = map[string]int32{
		"PROFILE": 0,
	}
)

func (x Mention_MentionType) Enum() *Mention_MentionType {
	p := new(Mention_MentionType)
	*p = x
	return p
}

func (x Mention_MentionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Mention_MentionType) Descriptor() protoreflect.EnumDescriptor {
	return file_waCommon_WACommon_proto_enumTypes[2].Descriptor()
}

func (Mention_MentionType) Type() protoreflect.EnumType {
	return &file_waCommon_WACommon_proto_enumTypes[2]
}

func (x Mention_MentionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Mention_MentionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Mention_MentionType(num)
	return nil
}

// Deprecated: Use Mention_MentionType.Descriptor instead.
func (Mention_MentionType) EnumDescriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{2, 0}
}

type LimitSharing_Trigger int32

const (
	LimitSharing_UNKNOWN                 LimitSharing_Trigger = 0
	LimitSharing_CHAT_SETTING            LimitSharing_Trigger = 1
	LimitSharing_BIZ_SUPPORTS_FB_HOSTING LimitSharing_Trigger = 2
	LimitSharing_UNKNOWN_GROUP           LimitSharing_Trigger = 3
)

// Enum value maps for LimitSharing_Trigger.
var (
	LimitSharing_Trigger_name = map[int32]string{
		0: "UNKNOWN",
		1: "CHAT_SETTING",
		2: "BIZ_SUPPORTS_FB_HOSTING",
		3: "UNKNOWN_GROUP",
	}
	LimitSharing_Trigger_value = map[string]int32{
		"UNKNOWN":                 0,
		"CHAT_SETTING":            1,
		"BIZ_SUPPORTS_FB_HOSTING": 2,
		"UNKNOWN_GROUP":           3,
	}
)

func (x LimitSharing_Trigger) Enum() *LimitSharing_Trigger {
	p := new(LimitSharing_Trigger)
	*p = x
	return p
}

func (x LimitSharing_Trigger) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LimitSharing_Trigger) Descriptor() protoreflect.EnumDescriptor {
	return file_waCommon_WACommon_proto_enumTypes[3].Descriptor()
}

func (LimitSharing_Trigger) Type() protoreflect.EnumType {
	return &file_waCommon_WACommon_proto_enumTypes[3]
}

func (x LimitSharing_Trigger) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *LimitSharing_Trigger) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = LimitSharing_Trigger(num)
	return nil
}

// Deprecated: Use LimitSharing_Trigger.Descriptor instead.
func (LimitSharing_Trigger) EnumDescriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{5, 0}
}

type MessageKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RemoteJID     *string                `protobuf:"bytes,1,opt,name=remoteJID" json:"remoteJID,omitempty"`
	FromMe        *bool                  `protobuf:"varint,2,opt,name=fromMe" json:"fromMe,omitempty"`
	ID            *string                `protobuf:"bytes,3,opt,name=ID" json:"ID,omitempty"`
	Participant   *string                `protobuf:"bytes,4,opt,name=participant" json:"participant,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageKey) Reset() {
	*x = MessageKey{}
	mi := &file_waCommon_WACommon_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageKey) ProtoMessage() {}

func (x *MessageKey) ProtoReflect() protoreflect.Message {
	mi := &file_waCommon_WACommon_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageKey.ProtoReflect.Descriptor instead.
func (*MessageKey) Descriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{0}
}

func (x *MessageKey) GetRemoteJID() string {
	if x != nil && x.RemoteJID != nil {
		return *x.RemoteJID
	}
	return ""
}

func (x *MessageKey) GetFromMe() bool {
	if x != nil && x.FromMe != nil {
		return *x.FromMe
	}
	return false
}

func (x *MessageKey) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *MessageKey) GetParticipant() string {
	if x != nil && x.Participant != nil {
		return *x.Participant
	}
	return ""
}

type Command struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	CommandType     *Command_CommandType   `protobuf:"varint,1,opt,name=commandType,enum=WACommon.Command_CommandType" json:"commandType,omitempty"`
	Offset          *uint32                `protobuf:"varint,2,opt,name=offset" json:"offset,omitempty"`
	Length          *uint32                `protobuf:"varint,3,opt,name=length" json:"length,omitempty"`
	ValidationToken *string                `protobuf:"bytes,4,opt,name=validationToken" json:"validationToken,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Command) Reset() {
	*x = Command{}
	mi := &file_waCommon_WACommon_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Command) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Command) ProtoMessage() {}

func (x *Command) ProtoReflect() protoreflect.Message {
	mi := &file_waCommon_WACommon_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Command.ProtoReflect.Descriptor instead.
func (*Command) Descriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{1}
}

func (x *Command) GetCommandType() Command_CommandType {
	if x != nil && x.CommandType != nil {
		return *x.CommandType
	}
	return Command_EVERYONE
}

func (x *Command) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *Command) GetLength() uint32 {
	if x != nil && x.Length != nil {
		return *x.Length
	}
	return 0
}

func (x *Command) GetValidationToken() string {
	if x != nil && x.ValidationToken != nil {
		return *x.ValidationToken
	}
	return ""
}

type Mention struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MentionType   *Mention_MentionType   `protobuf:"varint,1,opt,name=mentionType,enum=WACommon.Mention_MentionType" json:"mentionType,omitempty"`
	MentionedJID  *string                `protobuf:"bytes,2,opt,name=mentionedJID" json:"mentionedJID,omitempty"`
	Offset        *uint32                `protobuf:"varint,3,opt,name=offset" json:"offset,omitempty"`
	Length        *uint32                `protobuf:"varint,4,opt,name=length" json:"length,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mention) Reset() {
	*x = Mention{}
	mi := &file_waCommon_WACommon_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mention) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mention) ProtoMessage() {}

func (x *Mention) ProtoReflect() protoreflect.Message {
	mi := &file_waCommon_WACommon_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mention.ProtoReflect.Descriptor instead.
func (*Mention) Descriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{2}
}

func (x *Mention) GetMentionType() Mention_MentionType {
	if x != nil && x.MentionType != nil {
		return *x.MentionType
	}
	return Mention_PROFILE
}

func (x *Mention) GetMentionedJID() string {
	if x != nil && x.MentionedJID != nil {
		return *x.MentionedJID
	}
	return ""
}

func (x *Mention) GetOffset() uint32 {
	if x != nil && x.Offset != nil {
		return *x.Offset
	}
	return 0
}

func (x *Mention) GetLength() uint32 {
	if x != nil && x.Length != nil {
		return *x.Length
	}
	return 0
}

type MessageText struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          *string                `protobuf:"bytes,1,opt,name=text" json:"text,omitempty"`
	MentionedJID  []string               `protobuf:"bytes,2,rep,name=mentionedJID" json:"mentionedJID,omitempty"`
	Commands      []*Command             `protobuf:"bytes,3,rep,name=commands" json:"commands,omitempty"`
	Mentions      []*Mention             `protobuf:"bytes,4,rep,name=mentions" json:"mentions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageText) Reset() {
	*x = MessageText{}
	mi := &file_waCommon_WACommon_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageText) ProtoMessage() {}

func (x *MessageText) ProtoReflect() protoreflect.Message {
	mi := &file_waCommon_WACommon_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageText.ProtoReflect.Descriptor instead.
func (*MessageText) Descriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{3}
}

func (x *MessageText) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

func (x *MessageText) GetMentionedJID() []string {
	if x != nil {
		return x.MentionedJID
	}
	return nil
}

func (x *MessageText) GetCommands() []*Command {
	if x != nil {
		return x.Commands
	}
	return nil
}

func (x *MessageText) GetMentions() []*Mention {
	if x != nil {
		return x.Mentions
	}
	return nil
}

type SubProtocol struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Payload       []byte                 `protobuf:"bytes,1,opt,name=payload" json:"payload,omitempty"`
	Version       *int32                 `protobuf:"varint,2,opt,name=version" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubProtocol) Reset() {
	*x = SubProtocol{}
	mi := &file_waCommon_WACommon_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubProtocol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubProtocol) ProtoMessage() {}

func (x *SubProtocol) ProtoReflect() protoreflect.Message {
	mi := &file_waCommon_WACommon_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubProtocol.ProtoReflect.Descriptor instead.
func (*SubProtocol) Descriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{4}
}

func (x *SubProtocol) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *SubProtocol) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

type LimitSharing struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	SharingLimited               *bool                  `protobuf:"varint,1,opt,name=sharingLimited" json:"sharingLimited,omitempty"`
	Trigger                      *LimitSharing_Trigger  `protobuf:"varint,2,opt,name=trigger,enum=WACommon.LimitSharing_Trigger" json:"trigger,omitempty"`
	LimitSharingSettingTimestamp *int64                 `protobuf:"varint,3,opt,name=limitSharingSettingTimestamp" json:"limitSharingSettingTimestamp,omitempty"`
	InitiatedByMe                *bool                  `protobuf:"varint,4,opt,name=initiatedByMe" json:"initiatedByMe,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *LimitSharing) Reset() {
	*x = LimitSharing{}
	mi := &file_waCommon_WACommon_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LimitSharing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitSharing) ProtoMessage() {}

func (x *LimitSharing) ProtoReflect() protoreflect.Message {
	mi := &file_waCommon_WACommon_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitSharing.ProtoReflect.Descriptor instead.
func (*LimitSharing) Descriptor() ([]byte, []int) {
	return file_waCommon_WACommon_proto_rawDescGZIP(), []int{5}
}

func (x *LimitSharing) GetSharingLimited() bool {
	if x != nil && x.SharingLimited != nil {
		return *x.SharingLimited
	}
	return false
}

func (x *LimitSharing) GetTrigger() LimitSharing_Trigger {
	if x != nil && x.Trigger != nil {
		return *x.Trigger
	}
	return LimitSharing_UNKNOWN
}

func (x *LimitSharing) GetLimitSharingSettingTimestamp() int64 {
	if x != nil && x.LimitSharingSettingTimestamp != nil {
		return *x.LimitSharingSettingTimestamp
	}
	return 0
}

func (x *LimitSharing) GetInitiatedByMe() bool {
	if x != nil && x.InitiatedByMe != nil {
		return *x.InitiatedByMe
	}
	return false
}

var File_waCommon_WACommon_proto protoreflect.FileDescriptor

const file_waCommon_WACommon_proto_rawDesc = "" +
	"\n" +
	"\x17waCommon/WACommon.proto\x12\bWACommon\"t\n" +
	"\n" +
	"MessageKey\x12\x1c\n" +
	"\tremoteJID\x18\x01 \x01(\tR\tremoteJID\x12\x16\n" +
	"\x06fromMe\x18\x02 \x01(\bR\x06fromMe\x12\x0e\n" +
	"\x02ID\x18\x03 \x01(\tR\x02ID\x12 \n" +
	"\vparticipant\x18\x04 \x01(\tR\vparticipant\"\xe5\x01\n" +
	"\aCommand\x12?\n" +
	"\vcommandType\x18\x01 \x01(\x0e2\x1d.WACommon.Command.CommandTypeR\vcommandType\x12\x16\n" +
	"\x06offset\x18\x02 \x01(\rR\x06offset\x12\x16\n" +
	"\x06length\x18\x03 \x01(\rR\x06length\x12(\n" +
	"\x0fvalidationToken\x18\x04 \x01(\tR\x0fvalidationToken\"?\n" +
	"\vCommandType\x12\f\n" +
	"\bEVERYONE\x10\x01\x12\n" +
	"\n" +
	"\x06SILENT\x10\x02\x12\x06\n" +
	"\x02AI\x10\x03\x12\x0e\n" +
	"\n" +
	"AI_IMAGINE\x10\x04\"\xba\x01\n" +
	"\aMention\x12?\n" +
	"\vmentionType\x18\x01 \x01(\x0e2\x1d.WACommon.Mention.MentionTypeR\vmentionType\x12\"\n" +
	"\fmentionedJID\x18\x02 \x01(\tR\fmentionedJID\x12\x16\n" +
	"\x06offset\x18\x03 \x01(\rR\x06offset\x12\x16\n" +
	"\x06length\x18\x04 \x01(\rR\x06length\"\x1a\n" +
	"\vMentionType\x12\v\n" +
	"\aPROFILE\x10\x00\"\xa3\x01\n" +
	"\vMessageText\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\"\n" +
	"\fmentionedJID\x18\x02 \x03(\tR\fmentionedJID\x12-\n" +
	"\bcommands\x18\x03 \x03(\v2\x11.WACommon.CommandR\bcommands\x12-\n" +
	"\bmentions\x18\x04 \x03(\v2\x11.WACommon.MentionR\bmentions\"A\n" +
	"\vSubProtocol\x12\x18\n" +
	"\apayload\x18\x01 \x01(\fR\apayload\x12\x18\n" +
	"\aversion\x18\x02 \x01(\x05R\aversion\"\xb4\x02\n" +
	"\fLimitSharing\x12&\n" +
	"\x0esharingLimited\x18\x01 \x01(\bR\x0esharingLimited\x128\n" +
	"\atrigger\x18\x02 \x01(\x0e2\x1e.WACommon.LimitSharing.TriggerR\atrigger\x12B\n" +
	"\x1climitSharingSettingTimestamp\x18\x03 \x01(\x03R\x1climitSharingSettingTimestamp\x12$\n" +
	"\rinitiatedByMe\x18\x04 \x01(\bR\rinitiatedByMe\"X\n" +
	"\aTrigger\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\x10\n" +
	"\fCHAT_SETTING\x10\x01\x12\x1b\n" +
	"\x17BIZ_SUPPORTS_FB_HOSTING\x10\x02\x12\x11\n" +
	"\rUNKNOWN_GROUP\x10\x03*F\n" +
	"\x13FutureProofBehavior\x12\x0f\n" +
	"\vPLACEHOLDER\x10\x00\x12\x12\n" +
	"\x0eNO_PLACEHOLDER\x10\x01\x12\n" +
	"\n" +
	"\x06IGNORE\x10\x02B$Z\"go.mau.fi/whatsmeow/proto/waCommon"

var (
	file_waCommon_WACommon_proto_rawDescOnce sync.Once
	file_waCommon_WACommon_proto_rawDescData []byte
)

func file_waCommon_WACommon_proto_rawDescGZIP() []byte {
	file_waCommon_WACommon_proto_rawDescOnce.Do(func() {
		file_waCommon_WACommon_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waCommon_WACommon_proto_rawDesc), len(file_waCommon_WACommon_proto_rawDesc)))
	})
	return file_waCommon_WACommon_proto_rawDescData
}

var file_waCommon_WACommon_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_waCommon_WACommon_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_waCommon_WACommon_proto_goTypes = []any{
	(FutureProofBehavior)(0),  // 0: WACommon.FutureProofBehavior
	(Command_CommandType)(0),  // 1: WACommon.Command.CommandType
	(Mention_MentionType)(0),  // 2: WACommon.Mention.MentionType
	(LimitSharing_Trigger)(0), // 3: WACommon.LimitSharing.Trigger
	(*MessageKey)(nil),        // 4: WACommon.MessageKey
	(*Command)(nil),           // 5: WACommon.Command
	(*Mention)(nil),           // 6: WACommon.Mention
	(*MessageText)(nil),       // 7: WACommon.MessageText
	(*SubProtocol)(nil),       // 8: WACommon.SubProtocol
	(*LimitSharing)(nil),      // 9: WACommon.LimitSharing
}
var file_waCommon_WACommon_proto_depIdxs = []int32{
	1, // 0: WACommon.Command.commandType:type_name -> WACommon.Command.CommandType
	2, // 1: WACommon.Mention.mentionType:type_name -> WACommon.Mention.MentionType
	5, // 2: WACommon.MessageText.commands:type_name -> WACommon.Command
	6, // 3: WACommon.MessageText.mentions:type_name -> WACommon.Mention
	3, // 4: WACommon.LimitSharing.trigger:type_name -> WACommon.LimitSharing.Trigger
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_waCommon_WACommon_proto_init() }
func file_waCommon_WACommon_proto_init() {
	if File_waCommon_WACommon_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waCommon_WACommon_proto_rawDesc), len(file_waCommon_WACommon_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waCommon_WACommon_proto_goTypes,
		DependencyIndexes: file_waCommon_WACommon_proto_depIdxs,
		EnumInfos:         file_waCommon_WACommon_proto_enumTypes,
		MessageInfos:      file_waCommon_WACommon_proto_msgTypes,
	}.Build()
	File_waCommon_WACommon_proto = out.File
	file_waCommon_WACommon_proto_goTypes = nil
	file_waCommon_WACommon_proto_depIdxs = nil
}
