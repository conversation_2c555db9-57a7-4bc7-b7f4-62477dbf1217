// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waConsumerApplicationParameterised/WAConsumerApplicationParameterised.proto

package waConsumerApplicationParameterised

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waCommonParameterised "go.mau.fi/whatsmeow/proto/waCommonParameterised"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConsumerApplication_Metadata_SpecialTextSize int32

const (
	ConsumerApplication_Metadata_SMALL  ConsumerApplication_Metadata_SpecialTextSize = 1
	ConsumerApplication_Metadata_MEDIUM ConsumerApplication_Metadata_SpecialTextSize = 2
	ConsumerApplication_Metadata_LARGE  ConsumerApplication_Metadata_SpecialTextSize = 3
)

// Enum value maps for ConsumerApplication_Metadata_SpecialTextSize.
var (
	ConsumerApplication_Metadata_SpecialTextSize_name = map[int32]string{
		1: "SMALL",
		2: "MEDIUM",
		3: "LARGE",
	}
	ConsumerApplication_Metadata_SpecialTextSize_value = map[string]int32{
		"SMALL":  1,
		"MEDIUM": 2,
		"LARGE":  3,
	}
)

func (x ConsumerApplication_Metadata_SpecialTextSize) Enum() *ConsumerApplication_Metadata_SpecialTextSize {
	p := new(ConsumerApplication_Metadata_SpecialTextSize)
	*p = x
	return p
}

func (x ConsumerApplication_Metadata_SpecialTextSize) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsumerApplication_Metadata_SpecialTextSize) Descriptor() protoreflect.EnumDescriptor {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes[0].Descriptor()
}

func (ConsumerApplication_Metadata_SpecialTextSize) Type() protoreflect.EnumType {
	return &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes[0]
}

func (x ConsumerApplication_Metadata_SpecialTextSize) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ConsumerApplication_Metadata_SpecialTextSize) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ConsumerApplication_Metadata_SpecialTextSize(num)
	return nil
}

// Deprecated: Use ConsumerApplication_Metadata_SpecialTextSize.Descriptor instead.
func (ConsumerApplication_Metadata_SpecialTextSize) EnumDescriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 2, 0}
}

type ConsumerApplication_StatusTextMesage_FontType int32

const (
	ConsumerApplication_StatusTextMesage_SANS_SERIF        ConsumerApplication_StatusTextMesage_FontType = 0
	ConsumerApplication_StatusTextMesage_SERIF             ConsumerApplication_StatusTextMesage_FontType = 1
	ConsumerApplication_StatusTextMesage_NORICAN_REGULAR   ConsumerApplication_StatusTextMesage_FontType = 2
	ConsumerApplication_StatusTextMesage_BRYNDAN_WRITE     ConsumerApplication_StatusTextMesage_FontType = 3
	ConsumerApplication_StatusTextMesage_BEBASNEUE_REGULAR ConsumerApplication_StatusTextMesage_FontType = 4
	ConsumerApplication_StatusTextMesage_OSWALD_HEAVY      ConsumerApplication_StatusTextMesage_FontType = 5
)

// Enum value maps for ConsumerApplication_StatusTextMesage_FontType.
var (
	ConsumerApplication_StatusTextMesage_FontType_name = map[int32]string{
		0: "SANS_SERIF",
		1: "SERIF",
		2: "NORICAN_REGULAR",
		3: "BRYNDAN_WRITE",
		4: "BEBASNEUE_REGULAR",
		5: "OSWALD_HEAVY",
	}
	ConsumerApplication_StatusTextMesage_FontType_value = map[string]int32{
		"SANS_SERIF":        0,
		"SERIF":             1,
		"NORICAN_REGULAR":   2,
		"BRYNDAN_WRITE":     3,
		"BEBASNEUE_REGULAR": 4,
		"OSWALD_HEAVY":      5,
	}
)

func (x ConsumerApplication_StatusTextMesage_FontType) Enum() *ConsumerApplication_StatusTextMesage_FontType {
	p := new(ConsumerApplication_StatusTextMesage_FontType)
	*p = x
	return p
}

func (x ConsumerApplication_StatusTextMesage_FontType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsumerApplication_StatusTextMesage_FontType) Descriptor() protoreflect.EnumDescriptor {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes[1].Descriptor()
}

func (ConsumerApplication_StatusTextMesage_FontType) Type() protoreflect.EnumType {
	return &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes[1]
}

func (x ConsumerApplication_StatusTextMesage_FontType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ConsumerApplication_StatusTextMesage_FontType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ConsumerApplication_StatusTextMesage_FontType(num)
	return nil
}

// Deprecated: Use ConsumerApplication_StatusTextMesage_FontType.Descriptor instead.
func (ConsumerApplication_StatusTextMesage_FontType) EnumDescriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 20, 0}
}

type ConsumerApplication_ExtendedTextMessage_PreviewType int32

const (
	ConsumerApplication_ExtendedTextMessage_NONE  ConsumerApplication_ExtendedTextMessage_PreviewType = 0
	ConsumerApplication_ExtendedTextMessage_VIDEO ConsumerApplication_ExtendedTextMessage_PreviewType = 1
)

// Enum value maps for ConsumerApplication_ExtendedTextMessage_PreviewType.
var (
	ConsumerApplication_ExtendedTextMessage_PreviewType_name = map[int32]string{
		0: "NONE",
		1: "VIDEO",
	}
	ConsumerApplication_ExtendedTextMessage_PreviewType_value = map[string]int32{
		"NONE":  0,
		"VIDEO": 1,
	}
)

func (x ConsumerApplication_ExtendedTextMessage_PreviewType) Enum() *ConsumerApplication_ExtendedTextMessage_PreviewType {
	p := new(ConsumerApplication_ExtendedTextMessage_PreviewType)
	*p = x
	return p
}

func (x ConsumerApplication_ExtendedTextMessage_PreviewType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsumerApplication_ExtendedTextMessage_PreviewType) Descriptor() protoreflect.EnumDescriptor {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes[2].Descriptor()
}

func (ConsumerApplication_ExtendedTextMessage_PreviewType) Type() protoreflect.EnumType {
	return &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes[2]
}

func (x ConsumerApplication_ExtendedTextMessage_PreviewType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ConsumerApplication_ExtendedTextMessage_PreviewType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ConsumerApplication_ExtendedTextMessage_PreviewType(num)
	return nil
}

// Deprecated: Use ConsumerApplication_ExtendedTextMessage_PreviewType.Descriptor instead.
func (ConsumerApplication_ExtendedTextMessage_PreviewType) EnumDescriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 21, 0}
}

type ConsumerApplication struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Payload       *ConsumerApplication_Payload  `protobuf:"bytes,1,opt,name=payload" json:"payload,omitempty"`
	Metadata      *ConsumerApplication_Metadata `protobuf:"bytes,2,opt,name=metadata" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication) Reset() {
	*x = ConsumerApplication{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication) ProtoMessage() {}

func (x *ConsumerApplication) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication.ProtoReflect.Descriptor instead.
func (*ConsumerApplication) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0}
}

func (x *ConsumerApplication) GetPayload() *ConsumerApplication_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *ConsumerApplication) GetMetadata() *ConsumerApplication_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type ConsumerApplication_Payload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Payload:
	//
	//	*ConsumerApplication_Payload_Content
	//	*ConsumerApplication_Payload_ApplicationData
	//	*ConsumerApplication_Payload_Signal
	//	*ConsumerApplication_Payload_SubProtocol
	Payload       isConsumerApplication_Payload_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_Payload) Reset() {
	*x = ConsumerApplication_Payload{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_Payload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_Payload) ProtoMessage() {}

func (x *ConsumerApplication_Payload) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_Payload.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_Payload) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ConsumerApplication_Payload) GetPayload() isConsumerApplication_Payload_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *ConsumerApplication_Payload) GetContent() *ConsumerApplication_Content {
	if x != nil {
		if x, ok := x.Payload.(*ConsumerApplication_Payload_Content); ok {
			return x.Content
		}
	}
	return nil
}

func (x *ConsumerApplication_Payload) GetApplicationData() *ConsumerApplication_ApplicationData {
	if x != nil {
		if x, ok := x.Payload.(*ConsumerApplication_Payload_ApplicationData); ok {
			return x.ApplicationData
		}
	}
	return nil
}

func (x *ConsumerApplication_Payload) GetSignal() *ConsumerApplication_Signal {
	if x != nil {
		if x, ok := x.Payload.(*ConsumerApplication_Payload_Signal); ok {
			return x.Signal
		}
	}
	return nil
}

func (x *ConsumerApplication_Payload) GetSubProtocol() *ConsumerApplication_SubProtocolPayload {
	if x != nil {
		if x, ok := x.Payload.(*ConsumerApplication_Payload_SubProtocol); ok {
			return x.SubProtocol
		}
	}
	return nil
}

type isConsumerApplication_Payload_Payload interface {
	isConsumerApplication_Payload_Payload()
}

type ConsumerApplication_Payload_Content struct {
	Content *ConsumerApplication_Content `protobuf:"bytes,1,opt,name=content,oneof"`
}

type ConsumerApplication_Payload_ApplicationData struct {
	ApplicationData *ConsumerApplication_ApplicationData `protobuf:"bytes,2,opt,name=applicationData,oneof"`
}

type ConsumerApplication_Payload_Signal struct {
	Signal *ConsumerApplication_Signal `protobuf:"bytes,3,opt,name=signal,oneof"`
}

type ConsumerApplication_Payload_SubProtocol struct {
	SubProtocol *ConsumerApplication_SubProtocolPayload `protobuf:"bytes,4,opt,name=subProtocol,oneof"`
}

func (*ConsumerApplication_Payload_Content) isConsumerApplication_Payload_Payload() {}

func (*ConsumerApplication_Payload_ApplicationData) isConsumerApplication_Payload_Payload() {}

func (*ConsumerApplication_Payload_Signal) isConsumerApplication_Payload_Payload() {}

func (*ConsumerApplication_Payload_SubProtocol) isConsumerApplication_Payload_Payload() {}

type ConsumerApplication_SubProtocolPayload struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	FutureProof   *waCommonParameterised.FutureProofBehavior `protobuf:"varint,1,opt,name=futureProof,enum=WACommonParameterised.FutureProofBehavior" json:"futureProof,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_SubProtocolPayload) Reset() {
	*x = ConsumerApplication_SubProtocolPayload{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_SubProtocolPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_SubProtocolPayload) ProtoMessage() {}

func (x *ConsumerApplication_SubProtocolPayload) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_SubProtocolPayload.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_SubProtocolPayload) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ConsumerApplication_SubProtocolPayload) GetFutureProof() waCommonParameterised.FutureProofBehavior {
	if x != nil && x.FutureProof != nil {
		return *x.FutureProof
	}
	return waCommonParameterised.FutureProofBehavior(0)
}

type ConsumerApplication_Metadata struct {
	state           protoimpl.MessageState                        `protogen:"open.v1"`
	SpecialTextSize *ConsumerApplication_Metadata_SpecialTextSize `protobuf:"varint,1,opt,name=specialTextSize,enum=WAConsumerApplicationParameterised.ConsumerApplication_Metadata_SpecialTextSize" json:"specialTextSize,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ConsumerApplication_Metadata) Reset() {
	*x = ConsumerApplication_Metadata{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_Metadata) ProtoMessage() {}

func (x *ConsumerApplication_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_Metadata.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_Metadata) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 2}
}

func (x *ConsumerApplication_Metadata) GetSpecialTextSize() ConsumerApplication_Metadata_SpecialTextSize {
	if x != nil && x.SpecialTextSize != nil {
		return *x.SpecialTextSize
	}
	return ConsumerApplication_Metadata_SMALL
}

type ConsumerApplication_Signal struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_Signal) Reset() {
	*x = ConsumerApplication_Signal{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_Signal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_Signal) ProtoMessage() {}

func (x *ConsumerApplication_Signal) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_Signal.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_Signal) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 3}
}

type ConsumerApplication_ApplicationData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ApplicationContent:
	//
	//	*ConsumerApplication_ApplicationData_Revoke
	ApplicationContent isConsumerApplication_ApplicationData_ApplicationContent `protobuf_oneof:"applicationContent"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ConsumerApplication_ApplicationData) Reset() {
	*x = ConsumerApplication_ApplicationData{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_ApplicationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_ApplicationData) ProtoMessage() {}

func (x *ConsumerApplication_ApplicationData) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_ApplicationData.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_ApplicationData) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 4}
}

func (x *ConsumerApplication_ApplicationData) GetApplicationContent() isConsumerApplication_ApplicationData_ApplicationContent {
	if x != nil {
		return x.ApplicationContent
	}
	return nil
}

func (x *ConsumerApplication_ApplicationData) GetRevoke() *ConsumerApplication_RevokeMessage {
	if x != nil {
		if x, ok := x.ApplicationContent.(*ConsumerApplication_ApplicationData_Revoke); ok {
			return x.Revoke
		}
	}
	return nil
}

type isConsumerApplication_ApplicationData_ApplicationContent interface {
	isConsumerApplication_ApplicationData_ApplicationContent()
}

type ConsumerApplication_ApplicationData_Revoke struct {
	Revoke *ConsumerApplication_RevokeMessage `protobuf:"bytes,1,opt,name=revoke,oneof"`
}

func (*ConsumerApplication_ApplicationData_Revoke) isConsumerApplication_ApplicationData_ApplicationContent() {
}

type ConsumerApplication_Content struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Content:
	//
	//	*ConsumerApplication_Content_MessageText
	//	*ConsumerApplication_Content_ImageMessage
	//	*ConsumerApplication_Content_ContactMessage
	//	*ConsumerApplication_Content_LocationMessage
	//	*ConsumerApplication_Content_ExtendedTextMessage
	//	*ConsumerApplication_Content_StatusTextMessage
	//	*ConsumerApplication_Content_DocumentMessage
	//	*ConsumerApplication_Content_AudioMessage
	//	*ConsumerApplication_Content_VideoMessage
	//	*ConsumerApplication_Content_ContactsArrayMessage
	//	*ConsumerApplication_Content_LiveLocationMessage
	//	*ConsumerApplication_Content_StickerMessage
	//	*ConsumerApplication_Content_GroupInviteMessage
	//	*ConsumerApplication_Content_ViewOnceMessage
	//	*ConsumerApplication_Content_ReactionMessage
	//	*ConsumerApplication_Content_PollCreationMessage
	//	*ConsumerApplication_Content_PollUpdateMessage
	//	*ConsumerApplication_Content_EditMessage
	Content       isConsumerApplication_Content_Content `protobuf_oneof:"content"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_Content) Reset() {
	*x = ConsumerApplication_Content{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_Content) ProtoMessage() {}

func (x *ConsumerApplication_Content) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_Content.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_Content) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 5}
}

func (x *ConsumerApplication_Content) GetContent() isConsumerApplication_Content_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *ConsumerApplication_Content) GetMessageText() *waCommonParameterised.MessageText {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_MessageText); ok {
			return x.MessageText
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetImageMessage() *ConsumerApplication_ImageMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_ImageMessage); ok {
			return x.ImageMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetContactMessage() *ConsumerApplication_ContactMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_ContactMessage); ok {
			return x.ContactMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetLocationMessage() *ConsumerApplication_LocationMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_LocationMessage); ok {
			return x.LocationMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetExtendedTextMessage() *ConsumerApplication_ExtendedTextMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_ExtendedTextMessage); ok {
			return x.ExtendedTextMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetStatusTextMessage() *ConsumerApplication_StatusTextMesage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_StatusTextMessage); ok {
			return x.StatusTextMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetDocumentMessage() *ConsumerApplication_DocumentMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_DocumentMessage); ok {
			return x.DocumentMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetAudioMessage() *ConsumerApplication_AudioMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_AudioMessage); ok {
			return x.AudioMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetVideoMessage() *ConsumerApplication_VideoMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_VideoMessage); ok {
			return x.VideoMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetContactsArrayMessage() *ConsumerApplication_ContactsArrayMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_ContactsArrayMessage); ok {
			return x.ContactsArrayMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetLiveLocationMessage() *ConsumerApplication_LiveLocationMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_LiveLocationMessage); ok {
			return x.LiveLocationMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetStickerMessage() *ConsumerApplication_StickerMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_StickerMessage); ok {
			return x.StickerMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetGroupInviteMessage() *ConsumerApplication_GroupInviteMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_GroupInviteMessage); ok {
			return x.GroupInviteMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetViewOnceMessage() *ConsumerApplication_ViewOnceMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_ViewOnceMessage); ok {
			return x.ViewOnceMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetReactionMessage() *ConsumerApplication_ReactionMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_ReactionMessage); ok {
			return x.ReactionMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetPollCreationMessage() *ConsumerApplication_PollCreationMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_PollCreationMessage); ok {
			return x.PollCreationMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetPollUpdateMessage() *ConsumerApplication_PollUpdateMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_PollUpdateMessage); ok {
			return x.PollUpdateMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_Content) GetEditMessage() *ConsumerApplication_EditMessage {
	if x != nil {
		if x, ok := x.Content.(*ConsumerApplication_Content_EditMessage); ok {
			return x.EditMessage
		}
	}
	return nil
}

type isConsumerApplication_Content_Content interface {
	isConsumerApplication_Content_Content()
}

type ConsumerApplication_Content_MessageText struct {
	MessageText *waCommonParameterised.MessageText `protobuf:"bytes,1,opt,name=messageText,oneof"`
}

type ConsumerApplication_Content_ImageMessage struct {
	ImageMessage *ConsumerApplication_ImageMessage `protobuf:"bytes,2,opt,name=imageMessage,oneof"`
}

type ConsumerApplication_Content_ContactMessage struct {
	ContactMessage *ConsumerApplication_ContactMessage `protobuf:"bytes,3,opt,name=contactMessage,oneof"`
}

type ConsumerApplication_Content_LocationMessage struct {
	LocationMessage *ConsumerApplication_LocationMessage `protobuf:"bytes,4,opt,name=locationMessage,oneof"`
}

type ConsumerApplication_Content_ExtendedTextMessage struct {
	ExtendedTextMessage *ConsumerApplication_ExtendedTextMessage `protobuf:"bytes,5,opt,name=extendedTextMessage,oneof"`
}

type ConsumerApplication_Content_StatusTextMessage struct {
	StatusTextMessage *ConsumerApplication_StatusTextMesage `protobuf:"bytes,6,opt,name=statusTextMessage,oneof"`
}

type ConsumerApplication_Content_DocumentMessage struct {
	DocumentMessage *ConsumerApplication_DocumentMessage `protobuf:"bytes,7,opt,name=documentMessage,oneof"`
}

type ConsumerApplication_Content_AudioMessage struct {
	AudioMessage *ConsumerApplication_AudioMessage `protobuf:"bytes,8,opt,name=audioMessage,oneof"`
}

type ConsumerApplication_Content_VideoMessage struct {
	VideoMessage *ConsumerApplication_VideoMessage `protobuf:"bytes,9,opt,name=videoMessage,oneof"`
}

type ConsumerApplication_Content_ContactsArrayMessage struct {
	ContactsArrayMessage *ConsumerApplication_ContactsArrayMessage `protobuf:"bytes,10,opt,name=contactsArrayMessage,oneof"`
}

type ConsumerApplication_Content_LiveLocationMessage struct {
	LiveLocationMessage *ConsumerApplication_LiveLocationMessage `protobuf:"bytes,11,opt,name=liveLocationMessage,oneof"`
}

type ConsumerApplication_Content_StickerMessage struct {
	StickerMessage *ConsumerApplication_StickerMessage `protobuf:"bytes,12,opt,name=stickerMessage,oneof"`
}

type ConsumerApplication_Content_GroupInviteMessage struct {
	GroupInviteMessage *ConsumerApplication_GroupInviteMessage `protobuf:"bytes,13,opt,name=groupInviteMessage,oneof"`
}

type ConsumerApplication_Content_ViewOnceMessage struct {
	ViewOnceMessage *ConsumerApplication_ViewOnceMessage `protobuf:"bytes,14,opt,name=viewOnceMessage,oneof"`
}

type ConsumerApplication_Content_ReactionMessage struct {
	ReactionMessage *ConsumerApplication_ReactionMessage `protobuf:"bytes,16,opt,name=reactionMessage,oneof"`
}

type ConsumerApplication_Content_PollCreationMessage struct {
	PollCreationMessage *ConsumerApplication_PollCreationMessage `protobuf:"bytes,17,opt,name=pollCreationMessage,oneof"`
}

type ConsumerApplication_Content_PollUpdateMessage struct {
	PollUpdateMessage *ConsumerApplication_PollUpdateMessage `protobuf:"bytes,18,opt,name=pollUpdateMessage,oneof"`
}

type ConsumerApplication_Content_EditMessage struct {
	EditMessage *ConsumerApplication_EditMessage `protobuf:"bytes,19,opt,name=editMessage,oneof"`
}

func (*ConsumerApplication_Content_MessageText) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_ImageMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_ContactMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_LocationMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_ExtendedTextMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_StatusTextMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_DocumentMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_AudioMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_VideoMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_ContactsArrayMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_LiveLocationMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_StickerMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_GroupInviteMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_ViewOnceMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_ReactionMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_PollCreationMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_PollUpdateMessage) isConsumerApplication_Content_Content() {}

func (*ConsumerApplication_Content_EditMessage) isConsumerApplication_Content_Content() {}

type ConsumerApplication_EditMessage struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Key           *waCommonParameterised.MessageKey  `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Message       *waCommonParameterised.MessageText `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	TimestampMS   *int64                             `protobuf:"varint,3,opt,name=timestampMS" json:"timestampMS,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_EditMessage) Reset() {
	*x = ConsumerApplication_EditMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_EditMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_EditMessage) ProtoMessage() {}

func (x *ConsumerApplication_EditMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_EditMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_EditMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 6}
}

func (x *ConsumerApplication_EditMessage) GetKey() *waCommonParameterised.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *ConsumerApplication_EditMessage) GetMessage() *waCommonParameterised.MessageText {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ConsumerApplication_EditMessage) GetTimestampMS() int64 {
	if x != nil && x.TimestampMS != nil {
		return *x.TimestampMS
	}
	return 0
}

type ConsumerApplication_PollAddOptionMessage struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	PollOption    []*ConsumerApplication_Option `protobuf:"bytes,1,rep,name=pollOption" json:"pollOption,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_PollAddOptionMessage) Reset() {
	*x = ConsumerApplication_PollAddOptionMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_PollAddOptionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_PollAddOptionMessage) ProtoMessage() {}

func (x *ConsumerApplication_PollAddOptionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_PollAddOptionMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_PollAddOptionMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 7}
}

func (x *ConsumerApplication_PollAddOptionMessage) GetPollOption() []*ConsumerApplication_Option {
	if x != nil {
		return x.PollOption
	}
	return nil
}

type ConsumerApplication_PollVoteMessage struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SelectedOptions   [][]byte               `protobuf:"bytes,1,rep,name=selectedOptions" json:"selectedOptions,omitempty"`
	SenderTimestampMS *int64                 `protobuf:"varint,2,opt,name=senderTimestampMS" json:"senderTimestampMS,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ConsumerApplication_PollVoteMessage) Reset() {
	*x = ConsumerApplication_PollVoteMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_PollVoteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_PollVoteMessage) ProtoMessage() {}

func (x *ConsumerApplication_PollVoteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_PollVoteMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_PollVoteMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 8}
}

func (x *ConsumerApplication_PollVoteMessage) GetSelectedOptions() [][]byte {
	if x != nil {
		return x.SelectedOptions
	}
	return nil
}

func (x *ConsumerApplication_PollVoteMessage) GetSenderTimestampMS() int64 {
	if x != nil && x.SenderTimestampMS != nil {
		return *x.SenderTimestampMS
	}
	return 0
}

type ConsumerApplication_PollEncValue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EncPayload    []byte                 `protobuf:"bytes,1,opt,name=encPayload" json:"encPayload,omitempty"`
	EncIV         []byte                 `protobuf:"bytes,2,opt,name=encIV" json:"encIV,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_PollEncValue) Reset() {
	*x = ConsumerApplication_PollEncValue{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_PollEncValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_PollEncValue) ProtoMessage() {}

func (x *ConsumerApplication_PollEncValue) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_PollEncValue.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_PollEncValue) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 9}
}

func (x *ConsumerApplication_PollEncValue) GetEncPayload() []byte {
	if x != nil {
		return x.EncPayload
	}
	return nil
}

func (x *ConsumerApplication_PollEncValue) GetEncIV() []byte {
	if x != nil {
		return x.EncIV
	}
	return nil
}

type ConsumerApplication_PollUpdateMessage struct {
	state                  protoimpl.MessageState            `protogen:"open.v1"`
	PollCreationMessageKey *waCommonParameterised.MessageKey `protobuf:"bytes,1,opt,name=pollCreationMessageKey" json:"pollCreationMessageKey,omitempty"`
	Vote                   *ConsumerApplication_PollEncValue `protobuf:"bytes,2,opt,name=vote" json:"vote,omitempty"`
	AddOption              *ConsumerApplication_PollEncValue `protobuf:"bytes,3,opt,name=addOption" json:"addOption,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ConsumerApplication_PollUpdateMessage) Reset() {
	*x = ConsumerApplication_PollUpdateMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_PollUpdateMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_PollUpdateMessage) ProtoMessage() {}

func (x *ConsumerApplication_PollUpdateMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_PollUpdateMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_PollUpdateMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 10}
}

func (x *ConsumerApplication_PollUpdateMessage) GetPollCreationMessageKey() *waCommonParameterised.MessageKey {
	if x != nil {
		return x.PollCreationMessageKey
	}
	return nil
}

func (x *ConsumerApplication_PollUpdateMessage) GetVote() *ConsumerApplication_PollEncValue {
	if x != nil {
		return x.Vote
	}
	return nil
}

func (x *ConsumerApplication_PollUpdateMessage) GetAddOption() *ConsumerApplication_PollEncValue {
	if x != nil {
		return x.AddOption
	}
	return nil
}

type ConsumerApplication_PollCreationMessage struct {
	state                  protoimpl.MessageState        `protogen:"open.v1"`
	EncKey                 []byte                        `protobuf:"bytes,1,opt,name=encKey" json:"encKey,omitempty"`
	Name                   *string                       `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Options                []*ConsumerApplication_Option `protobuf:"bytes,3,rep,name=options" json:"options,omitempty"`
	SelectableOptionsCount *uint32                       `protobuf:"varint,4,opt,name=selectableOptionsCount" json:"selectableOptionsCount,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ConsumerApplication_PollCreationMessage) Reset() {
	*x = ConsumerApplication_PollCreationMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_PollCreationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_PollCreationMessage) ProtoMessage() {}

func (x *ConsumerApplication_PollCreationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_PollCreationMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_PollCreationMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 11}
}

func (x *ConsumerApplication_PollCreationMessage) GetEncKey() []byte {
	if x != nil {
		return x.EncKey
	}
	return nil
}

func (x *ConsumerApplication_PollCreationMessage) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ConsumerApplication_PollCreationMessage) GetOptions() []*ConsumerApplication_Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *ConsumerApplication_PollCreationMessage) GetSelectableOptionsCount() uint32 {
	if x != nil && x.SelectableOptionsCount != nil {
		return *x.SelectableOptionsCount
	}
	return 0
}

type ConsumerApplication_Option struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OptionName    *string                `protobuf:"bytes,1,opt,name=optionName" json:"optionName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_Option) Reset() {
	*x = ConsumerApplication_Option{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_Option) ProtoMessage() {}

func (x *ConsumerApplication_Option) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_Option.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_Option) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 12}
}

func (x *ConsumerApplication_Option) GetOptionName() string {
	if x != nil && x.OptionName != nil {
		return *x.OptionName
	}
	return ""
}

type ConsumerApplication_ReactionMessage struct {
	state                         protoimpl.MessageState            `protogen:"open.v1"`
	Key                           *waCommonParameterised.MessageKey `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Text                          *string                           `protobuf:"bytes,2,opt,name=text" json:"text,omitempty"`
	GroupingKey                   *string                           `protobuf:"bytes,3,opt,name=groupingKey" json:"groupingKey,omitempty"`
	SenderTimestampMS             *int64                            `protobuf:"varint,4,opt,name=senderTimestampMS" json:"senderTimestampMS,omitempty"`
	ReactionMetadataDataclassData *string                           `protobuf:"bytes,5,opt,name=reactionMetadataDataclassData" json:"reactionMetadataDataclassData,omitempty"`
	Style                         *int32                            `protobuf:"varint,6,opt,name=style" json:"style,omitempty"`
	unknownFields                 protoimpl.UnknownFields
	sizeCache                     protoimpl.SizeCache
}

func (x *ConsumerApplication_ReactionMessage) Reset() {
	*x = ConsumerApplication_ReactionMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_ReactionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_ReactionMessage) ProtoMessage() {}

func (x *ConsumerApplication_ReactionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_ReactionMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_ReactionMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 13}
}

func (x *ConsumerApplication_ReactionMessage) GetKey() *waCommonParameterised.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *ConsumerApplication_ReactionMessage) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

func (x *ConsumerApplication_ReactionMessage) GetGroupingKey() string {
	if x != nil && x.GroupingKey != nil {
		return *x.GroupingKey
	}
	return ""
}

func (x *ConsumerApplication_ReactionMessage) GetSenderTimestampMS() int64 {
	if x != nil && x.SenderTimestampMS != nil {
		return *x.SenderTimestampMS
	}
	return 0
}

func (x *ConsumerApplication_ReactionMessage) GetReactionMetadataDataclassData() string {
	if x != nil && x.ReactionMetadataDataclassData != nil {
		return *x.ReactionMetadataDataclassData
	}
	return ""
}

func (x *ConsumerApplication_ReactionMessage) GetStyle() int32 {
	if x != nil && x.Style != nil {
		return *x.Style
	}
	return 0
}

type ConsumerApplication_RevokeMessage struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Key           *waCommonParameterised.MessageKey `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_RevokeMessage) Reset() {
	*x = ConsumerApplication_RevokeMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_RevokeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_RevokeMessage) ProtoMessage() {}

func (x *ConsumerApplication_RevokeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_RevokeMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_RevokeMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 14}
}

func (x *ConsumerApplication_RevokeMessage) GetKey() *waCommonParameterised.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type ConsumerApplication_ViewOnceMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ViewOnceContent:
	//
	//	*ConsumerApplication_ViewOnceMessage_ImageMessage
	//	*ConsumerApplication_ViewOnceMessage_VideoMessage
	ViewOnceContent isConsumerApplication_ViewOnceMessage_ViewOnceContent `protobuf_oneof:"viewOnceContent"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ConsumerApplication_ViewOnceMessage) Reset() {
	*x = ConsumerApplication_ViewOnceMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_ViewOnceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_ViewOnceMessage) ProtoMessage() {}

func (x *ConsumerApplication_ViewOnceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_ViewOnceMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_ViewOnceMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 15}
}

func (x *ConsumerApplication_ViewOnceMessage) GetViewOnceContent() isConsumerApplication_ViewOnceMessage_ViewOnceContent {
	if x != nil {
		return x.ViewOnceContent
	}
	return nil
}

func (x *ConsumerApplication_ViewOnceMessage) GetImageMessage() *ConsumerApplication_ImageMessage {
	if x != nil {
		if x, ok := x.ViewOnceContent.(*ConsumerApplication_ViewOnceMessage_ImageMessage); ok {
			return x.ImageMessage
		}
	}
	return nil
}

func (x *ConsumerApplication_ViewOnceMessage) GetVideoMessage() *ConsumerApplication_VideoMessage {
	if x != nil {
		if x, ok := x.ViewOnceContent.(*ConsumerApplication_ViewOnceMessage_VideoMessage); ok {
			return x.VideoMessage
		}
	}
	return nil
}

type isConsumerApplication_ViewOnceMessage_ViewOnceContent interface {
	isConsumerApplication_ViewOnceMessage_ViewOnceContent()
}

type ConsumerApplication_ViewOnceMessage_ImageMessage struct {
	ImageMessage *ConsumerApplication_ImageMessage `protobuf:"bytes,1,opt,name=imageMessage,oneof"`
}

type ConsumerApplication_ViewOnceMessage_VideoMessage struct {
	VideoMessage *ConsumerApplication_VideoMessage `protobuf:"bytes,2,opt,name=videoMessage,oneof"`
}

func (*ConsumerApplication_ViewOnceMessage_ImageMessage) isConsumerApplication_ViewOnceMessage_ViewOnceContent() {
}

func (*ConsumerApplication_ViewOnceMessage_VideoMessage) isConsumerApplication_ViewOnceMessage_ViewOnceContent() {
}

type ConsumerApplication_GroupInviteMessage struct {
	state            protoimpl.MessageState             `protogen:"open.v1"`
	GroupJID         *string                            `protobuf:"bytes,1,opt,name=groupJID" json:"groupJID,omitempty"`
	InviteCode       *string                            `protobuf:"bytes,2,opt,name=inviteCode" json:"inviteCode,omitempty"`
	InviteExpiration *int64                             `protobuf:"varint,3,opt,name=inviteExpiration" json:"inviteExpiration,omitempty"`
	GroupName        *string                            `protobuf:"bytes,4,opt,name=groupName" json:"groupName,omitempty"`
	JPEGThumbnail    []byte                             `protobuf:"bytes,5,opt,name=JPEGThumbnail" json:"JPEGThumbnail,omitempty"`
	Caption          *waCommonParameterised.MessageText `protobuf:"bytes,6,opt,name=caption" json:"caption,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ConsumerApplication_GroupInviteMessage) Reset() {
	*x = ConsumerApplication_GroupInviteMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_GroupInviteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_GroupInviteMessage) ProtoMessage() {}

func (x *ConsumerApplication_GroupInviteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_GroupInviteMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_GroupInviteMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 16}
}

func (x *ConsumerApplication_GroupInviteMessage) GetGroupJID() string {
	if x != nil && x.GroupJID != nil {
		return *x.GroupJID
	}
	return ""
}

func (x *ConsumerApplication_GroupInviteMessage) GetInviteCode() string {
	if x != nil && x.InviteCode != nil {
		return *x.InviteCode
	}
	return ""
}

func (x *ConsumerApplication_GroupInviteMessage) GetInviteExpiration() int64 {
	if x != nil && x.InviteExpiration != nil {
		return *x.InviteExpiration
	}
	return 0
}

func (x *ConsumerApplication_GroupInviteMessage) GetGroupName() string {
	if x != nil && x.GroupName != nil {
		return *x.GroupName
	}
	return ""
}

func (x *ConsumerApplication_GroupInviteMessage) GetJPEGThumbnail() []byte {
	if x != nil {
		return x.JPEGThumbnail
	}
	return nil
}

func (x *ConsumerApplication_GroupInviteMessage) GetCaption() *waCommonParameterised.MessageText {
	if x != nil {
		return x.Caption
	}
	return nil
}

type ConsumerApplication_LiveLocationMessage struct {
	state                             protoimpl.MessageState             `protogen:"open.v1"`
	Location                          *ConsumerApplication_Location      `protobuf:"bytes,1,opt,name=location" json:"location,omitempty"`
	AccuracyInMeters                  *uint32                            `protobuf:"varint,2,opt,name=accuracyInMeters" json:"accuracyInMeters,omitempty"`
	SpeedInMps                        *float32                           `protobuf:"fixed32,3,opt,name=speedInMps" json:"speedInMps,omitempty"`
	DegreesClockwiseFromMagneticNorth *uint32                            `protobuf:"varint,4,opt,name=degreesClockwiseFromMagneticNorth" json:"degreesClockwiseFromMagneticNorth,omitempty"`
	Caption                           *waCommonParameterised.MessageText `protobuf:"bytes,5,opt,name=caption" json:"caption,omitempty"`
	SequenceNumber                    *int64                             `protobuf:"varint,6,opt,name=sequenceNumber" json:"sequenceNumber,omitempty"`
	TimeOffset                        *uint32                            `protobuf:"varint,7,opt,name=timeOffset" json:"timeOffset,omitempty"`
	unknownFields                     protoimpl.UnknownFields
	sizeCache                         protoimpl.SizeCache
}

func (x *ConsumerApplication_LiveLocationMessage) Reset() {
	*x = ConsumerApplication_LiveLocationMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_LiveLocationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_LiveLocationMessage) ProtoMessage() {}

func (x *ConsumerApplication_LiveLocationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_LiveLocationMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_LiveLocationMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 17}
}

func (x *ConsumerApplication_LiveLocationMessage) GetLocation() *ConsumerApplication_Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *ConsumerApplication_LiveLocationMessage) GetAccuracyInMeters() uint32 {
	if x != nil && x.AccuracyInMeters != nil {
		return *x.AccuracyInMeters
	}
	return 0
}

func (x *ConsumerApplication_LiveLocationMessage) GetSpeedInMps() float32 {
	if x != nil && x.SpeedInMps != nil {
		return *x.SpeedInMps
	}
	return 0
}

func (x *ConsumerApplication_LiveLocationMessage) GetDegreesClockwiseFromMagneticNorth() uint32 {
	if x != nil && x.DegreesClockwiseFromMagneticNorth != nil {
		return *x.DegreesClockwiseFromMagneticNorth
	}
	return 0
}

func (x *ConsumerApplication_LiveLocationMessage) GetCaption() *waCommonParameterised.MessageText {
	if x != nil {
		return x.Caption
	}
	return nil
}

func (x *ConsumerApplication_LiveLocationMessage) GetSequenceNumber() int64 {
	if x != nil && x.SequenceNumber != nil {
		return *x.SequenceNumber
	}
	return 0
}

func (x *ConsumerApplication_LiveLocationMessage) GetTimeOffset() uint32 {
	if x != nil && x.TimeOffset != nil {
		return *x.TimeOffset
	}
	return 0
}

type ConsumerApplication_ContactsArrayMessage struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	DisplayName   *string                               `protobuf:"bytes,1,opt,name=displayName" json:"displayName,omitempty"`
	Contacts      []*ConsumerApplication_ContactMessage `protobuf:"bytes,2,rep,name=contacts" json:"contacts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_ContactsArrayMessage) Reset() {
	*x = ConsumerApplication_ContactsArrayMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_ContactsArrayMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_ContactsArrayMessage) ProtoMessage() {}

func (x *ConsumerApplication_ContactsArrayMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_ContactsArrayMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_ContactsArrayMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 18}
}

func (x *ConsumerApplication_ContactsArrayMessage) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *ConsumerApplication_ContactsArrayMessage) GetContacts() []*ConsumerApplication_ContactMessage {
	if x != nil {
		return x.Contacts
	}
	return nil
}

type ConsumerApplication_ContactMessage struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Contact       *waCommonParameterised.SubProtocol `protobuf:"bytes,1,opt,name=contact" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_ContactMessage) Reset() {
	*x = ConsumerApplication_ContactMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_ContactMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_ContactMessage) ProtoMessage() {}

func (x *ConsumerApplication_ContactMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_ContactMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_ContactMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 19}
}

func (x *ConsumerApplication_ContactMessage) GetContact() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Contact
	}
	return nil
}

type ConsumerApplication_StatusTextMesage struct {
	state          protoimpl.MessageState                         `protogen:"open.v1"`
	Text           *ConsumerApplication_ExtendedTextMessage       `protobuf:"bytes,1,opt,name=text" json:"text,omitempty"`
	TextArgb       *uint32                                        `protobuf:"fixed32,6,opt,name=textArgb" json:"textArgb,omitempty"`
	BackgroundArgb *uint32                                        `protobuf:"fixed32,7,opt,name=backgroundArgb" json:"backgroundArgb,omitempty"`
	Font           *ConsumerApplication_StatusTextMesage_FontType `protobuf:"varint,8,opt,name=font,enum=WAConsumerApplicationParameterised.ConsumerApplication_StatusTextMesage_FontType" json:"font,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ConsumerApplication_StatusTextMesage) Reset() {
	*x = ConsumerApplication_StatusTextMesage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_StatusTextMesage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_StatusTextMesage) ProtoMessage() {}

func (x *ConsumerApplication_StatusTextMesage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_StatusTextMesage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_StatusTextMesage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 20}
}

func (x *ConsumerApplication_StatusTextMesage) GetText() *ConsumerApplication_ExtendedTextMessage {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *ConsumerApplication_StatusTextMesage) GetTextArgb() uint32 {
	if x != nil && x.TextArgb != nil {
		return *x.TextArgb
	}
	return 0
}

func (x *ConsumerApplication_StatusTextMesage) GetBackgroundArgb() uint32 {
	if x != nil && x.BackgroundArgb != nil {
		return *x.BackgroundArgb
	}
	return 0
}

func (x *ConsumerApplication_StatusTextMesage) GetFont() ConsumerApplication_StatusTextMesage_FontType {
	if x != nil && x.Font != nil {
		return *x.Font
	}
	return ConsumerApplication_StatusTextMesage_SANS_SERIF
}

type ConsumerApplication_ExtendedTextMessage struct {
	state         protoimpl.MessageState                               `protogen:"open.v1"`
	Text          *waCommonParameterised.MessageText                   `protobuf:"bytes,1,opt,name=text" json:"text,omitempty"`
	MatchedText   *string                                              `protobuf:"bytes,2,opt,name=matchedText" json:"matchedText,omitempty"`
	CanonicalURL  *string                                              `protobuf:"bytes,3,opt,name=canonicalURL" json:"canonicalURL,omitempty"`
	Description   *string                                              `protobuf:"bytes,4,opt,name=description" json:"description,omitempty"`
	Title         *string                                              `protobuf:"bytes,5,opt,name=title" json:"title,omitempty"`
	Thumbnail     *waCommonParameterised.SubProtocol                   `protobuf:"bytes,6,opt,name=thumbnail" json:"thumbnail,omitempty"`
	PreviewType   *ConsumerApplication_ExtendedTextMessage_PreviewType `protobuf:"varint,7,opt,name=previewType,enum=WAConsumerApplicationParameterised.ConsumerApplication_ExtendedTextMessage_PreviewType" json:"previewType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_ExtendedTextMessage) Reset() {
	*x = ConsumerApplication_ExtendedTextMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_ExtendedTextMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_ExtendedTextMessage) ProtoMessage() {}

func (x *ConsumerApplication_ExtendedTextMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_ExtendedTextMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_ExtendedTextMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 21}
}

func (x *ConsumerApplication_ExtendedTextMessage) GetText() *waCommonParameterised.MessageText {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *ConsumerApplication_ExtendedTextMessage) GetMatchedText() string {
	if x != nil && x.MatchedText != nil {
		return *x.MatchedText
	}
	return ""
}

func (x *ConsumerApplication_ExtendedTextMessage) GetCanonicalURL() string {
	if x != nil && x.CanonicalURL != nil {
		return *x.CanonicalURL
	}
	return ""
}

func (x *ConsumerApplication_ExtendedTextMessage) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ConsumerApplication_ExtendedTextMessage) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *ConsumerApplication_ExtendedTextMessage) GetThumbnail() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Thumbnail
	}
	return nil
}

func (x *ConsumerApplication_ExtendedTextMessage) GetPreviewType() ConsumerApplication_ExtendedTextMessage_PreviewType {
	if x != nil && x.PreviewType != nil {
		return *x.PreviewType
	}
	return ConsumerApplication_ExtendedTextMessage_NONE
}

type ConsumerApplication_LocationMessage struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Location      *ConsumerApplication_Location `protobuf:"bytes,1,opt,name=location" json:"location,omitempty"`
	Address       *string                       `protobuf:"bytes,2,opt,name=address" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_LocationMessage) Reset() {
	*x = ConsumerApplication_LocationMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_LocationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_LocationMessage) ProtoMessage() {}

func (x *ConsumerApplication_LocationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_LocationMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_LocationMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 22}
}

func (x *ConsumerApplication_LocationMessage) GetLocation() *ConsumerApplication_Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *ConsumerApplication_LocationMessage) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

type ConsumerApplication_StickerMessage struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Sticker       *waCommonParameterised.SubProtocol `protobuf:"bytes,1,opt,name=sticker" json:"sticker,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_StickerMessage) Reset() {
	*x = ConsumerApplication_StickerMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_StickerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_StickerMessage) ProtoMessage() {}

func (x *ConsumerApplication_StickerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_StickerMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_StickerMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 23}
}

func (x *ConsumerApplication_StickerMessage) GetSticker() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Sticker
	}
	return nil
}

type ConsumerApplication_DocumentMessage struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Document      *waCommonParameterised.SubProtocol `protobuf:"bytes,1,opt,name=document" json:"document,omitempty"`
	FileName      *string                            `protobuf:"bytes,2,opt,name=fileName" json:"fileName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_DocumentMessage) Reset() {
	*x = ConsumerApplication_DocumentMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_DocumentMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_DocumentMessage) ProtoMessage() {}

func (x *ConsumerApplication_DocumentMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_DocumentMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_DocumentMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 24}
}

func (x *ConsumerApplication_DocumentMessage) GetDocument() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Document
	}
	return nil
}

func (x *ConsumerApplication_DocumentMessage) GetFileName() string {
	if x != nil && x.FileName != nil {
		return *x.FileName
	}
	return ""
}

type ConsumerApplication_VideoMessage struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Video         *waCommonParameterised.SubProtocol `protobuf:"bytes,1,opt,name=video" json:"video,omitempty"`
	Caption       *waCommonParameterised.MessageText `protobuf:"bytes,2,opt,name=caption" json:"caption,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_VideoMessage) Reset() {
	*x = ConsumerApplication_VideoMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_VideoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_VideoMessage) ProtoMessage() {}

func (x *ConsumerApplication_VideoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_VideoMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_VideoMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 25}
}

func (x *ConsumerApplication_VideoMessage) GetVideo() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *ConsumerApplication_VideoMessage) GetCaption() *waCommonParameterised.MessageText {
	if x != nil {
		return x.Caption
	}
	return nil
}

type ConsumerApplication_AudioMessage struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Audio         *waCommonParameterised.SubProtocol `protobuf:"bytes,1,opt,name=audio" json:"audio,omitempty"`
	PTT           *bool                              `protobuf:"varint,2,opt,name=PTT" json:"PTT,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_AudioMessage) Reset() {
	*x = ConsumerApplication_AudioMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_AudioMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_AudioMessage) ProtoMessage() {}

func (x *ConsumerApplication_AudioMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_AudioMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_AudioMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 26}
}

func (x *ConsumerApplication_AudioMessage) GetAudio() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *ConsumerApplication_AudioMessage) GetPTT() bool {
	if x != nil && x.PTT != nil {
		return *x.PTT
	}
	return false
}

type ConsumerApplication_ImageMessage struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Image         *waCommonParameterised.SubProtocol `protobuf:"bytes,1,opt,name=image" json:"image,omitempty"`
	Caption       *waCommonParameterised.MessageText `protobuf:"bytes,2,opt,name=caption" json:"caption,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_ImageMessage) Reset() {
	*x = ConsumerApplication_ImageMessage{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_ImageMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_ImageMessage) ProtoMessage() {}

func (x *ConsumerApplication_ImageMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_ImageMessage.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_ImageMessage) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 27}
}

func (x *ConsumerApplication_ImageMessage) GetImage() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ConsumerApplication_ImageMessage) GetCaption() *waCommonParameterised.MessageText {
	if x != nil {
		return x.Caption
	}
	return nil
}

type ConsumerApplication_InteractiveAnnotation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Action:
	//
	//	*ConsumerApplication_InteractiveAnnotation_Location
	Action          isConsumerApplication_InteractiveAnnotation_Action `protobuf_oneof:"action"`
	PolygonVertices []*ConsumerApplication_Point                       `protobuf:"bytes,1,rep,name=polygonVertices" json:"polygonVertices,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ConsumerApplication_InteractiveAnnotation) Reset() {
	*x = ConsumerApplication_InteractiveAnnotation{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_InteractiveAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_InteractiveAnnotation) ProtoMessage() {}

func (x *ConsumerApplication_InteractiveAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_InteractiveAnnotation.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_InteractiveAnnotation) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 28}
}

func (x *ConsumerApplication_InteractiveAnnotation) GetAction() isConsumerApplication_InteractiveAnnotation_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *ConsumerApplication_InteractiveAnnotation) GetLocation() *ConsumerApplication_Location {
	if x != nil {
		if x, ok := x.Action.(*ConsumerApplication_InteractiveAnnotation_Location); ok {
			return x.Location
		}
	}
	return nil
}

func (x *ConsumerApplication_InteractiveAnnotation) GetPolygonVertices() []*ConsumerApplication_Point {
	if x != nil {
		return x.PolygonVertices
	}
	return nil
}

type isConsumerApplication_InteractiveAnnotation_Action interface {
	isConsumerApplication_InteractiveAnnotation_Action()
}

type ConsumerApplication_InteractiveAnnotation_Location struct {
	Location *ConsumerApplication_Location `protobuf:"bytes,2,opt,name=location,oneof"`
}

func (*ConsumerApplication_InteractiveAnnotation_Location) isConsumerApplication_InteractiveAnnotation_Action() {
}

type ConsumerApplication_Point struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             *float64               `protobuf:"fixed64,1,opt,name=x" json:"x,omitempty"`
	Y             *float64               `protobuf:"fixed64,2,opt,name=y" json:"y,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_Point) Reset() {
	*x = ConsumerApplication_Point{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_Point) ProtoMessage() {}

func (x *ConsumerApplication_Point) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_Point.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_Point) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 29}
}

func (x *ConsumerApplication_Point) GetX() float64 {
	if x != nil && x.X != nil {
		return *x.X
	}
	return 0
}

func (x *ConsumerApplication_Point) GetY() float64 {
	if x != nil && x.Y != nil {
		return *x.Y
	}
	return 0
}

type ConsumerApplication_Location struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DegreesLatitude  *float64               `protobuf:"fixed64,1,opt,name=degreesLatitude" json:"degreesLatitude,omitempty"`
	DegreesLongitude *float64               `protobuf:"fixed64,2,opt,name=degreesLongitude" json:"degreesLongitude,omitempty"`
	Name             *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ConsumerApplication_Location) Reset() {
	*x = ConsumerApplication_Location{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_Location) ProtoMessage() {}

func (x *ConsumerApplication_Location) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_Location.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_Location) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 30}
}

func (x *ConsumerApplication_Location) GetDegreesLatitude() float64 {
	if x != nil && x.DegreesLatitude != nil {
		return *x.DegreesLatitude
	}
	return 0
}

func (x *ConsumerApplication_Location) GetDegreesLongitude() float64 {
	if x != nil && x.DegreesLongitude != nil {
		return *x.DegreesLongitude
	}
	return 0
}

func (x *ConsumerApplication_Location) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type ConsumerApplication_MediaPayload struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Protocol      *waCommonParameterised.SubProtocol `protobuf:"bytes,1,opt,name=protocol" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumerApplication_MediaPayload) Reset() {
	*x = ConsumerApplication_MediaPayload{}
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumerApplication_MediaPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerApplication_MediaPayload) ProtoMessage() {}

func (x *ConsumerApplication_MediaPayload) ProtoReflect() protoreflect.Message {
	mi := &file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerApplication_MediaPayload.ProtoReflect.Descriptor instead.
func (*ConsumerApplication_MediaPayload) Descriptor() ([]byte, []int) {
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP(), []int{0, 31}
}

func (x *ConsumerApplication_MediaPayload) GetProtocol() *waCommonParameterised.SubProtocol {
	if x != nil {
		return x.Protocol
	}
	return nil
}

var File_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto protoreflect.FileDescriptor

const file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDesc = "" +
	"\n" +
	"KwaConsumerApplicationParameterised/WAConsumerApplicationParameterised.proto\x12\"WAConsumerApplicationParameterised\x1a1waCommonParameterised/WACommonParameterised.proto\"\xa1<\n" +
	"\x13ConsumerApplication\x12Y\n" +
	"\apayload\x18\x01 \x01(\v2?.WAConsumerApplicationParameterised.ConsumerApplication.PayloadR\apayload\x12\\\n" +
	"\bmetadata\x18\x02 \x01(\<EMAIL>\bmetadata\x1a\xb0\x03\n" +
	"\aPayload\x12[\n" +
	"\acontent\x18\x01 \x01(\v2?.WAConsumerApplicationParameterised.ConsumerApplication.ContentH\x00R\acontent\x12s\n" +
	"\x0fapplicationData\x18\x02 \x01(\v2G.WAConsumerApplicationParameterised.ConsumerApplication.ApplicationDataH\x00R\x0fapplicationData\x12X\n" +
	"\x06signal\x18\x03 \x01(\v2>.WAConsumerApplicationParameterised.ConsumerApplication.SignalH\x00R\x06signal\x12n\n" +
	"\vsubProtocol\x18\x04 \x01(\v2J.WAConsumerApplicationParameterised.ConsumerApplication.SubProtocolPayloadH\x00R\vsubProtocolB\t\n" +
	"\apayload\x1ab\n" +
	"\x12SubProtocolPayload\x12L\n" +
	"\vfutureProof\x18\x01 \x01(\x0e2*.WACommonParameterised.FutureProofBehaviorR\vfutureProof\x1a\xbb\x01\n" +
	"\bMetadata\x12z\n" +
	"\x0fspecialTextSize\x18\x01 \x01(\x0e2P.WAConsumerApplicationParameterised.ConsumerApplication.Metadata.SpecialTextSizeR\x0fspecialTextSize\"3\n" +
	"\x0fSpecialTextSize\x12\t\n" +
	"\x05SMALL\x10\x01\x12\n" +
	"\n" +
	"\x06MEDIUM\x10\x02\x12\t\n" +
	"\x05LARGE\x10\x03\x1a\b\n" +
	"\x06Signal\x1a\x88\x01\n" +
	"\x0fApplicationData\x12_\n" +
	"\x06revoke\x18\x01 \x01(\v2E.WAConsumerApplicationParameterised.ConsumerApplication.RevokeMessageH\x00R\x06revokeB\x14\n" +
	"\x12applicationContent\x1a\xbc\x10\n" +
	"\aContent\x12F\n" +
	"\vmessageText\x18\x01 \x01(\v2\".WACommonParameterised.MessageTextH\x00R\vmessageText\x12j\n" +
	"\fimageMessage\x18\x02 \x01(\v2D.WAConsumerApplicationParameterised.ConsumerApplication.ImageMessageH\x00R\fimageMessage\x12p\n" +
	"\x0econtactMessage\x18\x03 \x01(\v2F.WAConsumerApplicationParameterised.ConsumerApplication.ContactMessageH\x00R\x0econtactMessage\x12s\n" +
	"\x0flocationMessage\x18\x04 \x01(\v2G.WAConsumerApplicationParameterised.ConsumerApplication.LocationMessageH\x00R\x0flocationMessage\x12\x7f\n" +
	"\x13extendedTextMessage\x18\x05 \x01(\v2K.WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessageH\x00R\x13extendedTextMessage\x12x\n" +
	"\x11statusTextMessage\x18\x06 \x01(\v2H.WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesageH\x00R\x11statusTextMessage\x12s\n" +
	"\x0fdocumentMessage\x18\a \x01(\v2G.WAConsumerApplicationParameterised.ConsumerApplication.DocumentMessageH\x00R\x0fdocumentMessage\x12j\n" +
	"\faudioMessage\x18\b \x01(\v2D.WAConsumerApplicationParameterised.ConsumerApplication.AudioMessageH\x00R\faudioMessage\x12j\n" +
	"\fvideoMessage\x18\t \x01(\v2D.WAConsumerApplicationParameterised.ConsumerApplication.VideoMessageH\x00R\fvideoMessage\x12\x82\x01\n" +
	"\x14contactsArrayMessage\x18\n" +
	" \x01(\v2L.WAConsumerApplicationParameterised.ConsumerApplication.ContactsArrayMessageH\x00R\x14contactsArrayMessage\x12\x7f\n" +
	"\x13liveLocationMessage\x18\v \x01(\v2K.WAConsumerApplicationParameterised.ConsumerApplication.LiveLocationMessageH\x00R\x13liveLocationMessage\x12p\n" +
	"\x0estickerMessage\x18\f \x01(\v2F.WAConsumerApplicationParameterised.ConsumerApplication.StickerMessageH\x00R\x0estickerMessage\x12|\n" +
	"\x12groupInviteMessage\x18\r \x01(\v2J.WAConsumerApplicationParameterised.ConsumerApplication.GroupInviteMessageH\x00R\x12groupInviteMessage\x12s\n" +
	"\x0fviewOnceMessage\x18\x0e \x01(\v2G.WAConsumerApplicationParameterised.ConsumerApplication.ViewOnceMessageH\x00R\x0fviewOnceMessage\x12s\n" +
	"\x0freactionMessage\x18\x10 \x01(\v2G.WAConsumerApplicationParameterised.ConsumerApplication.ReactionMessageH\x00R\x0freactionMessage\x12\x7f\n" +
	"\x13pollCreationMessage\x18\x11 \x01(\v2K.WAConsumerApplicationParameterised.ConsumerApplication.PollCreationMessageH\x00R\x13pollCreationMessage\x12y\n" +
	"\x11pollUpdateMessage\x18\x12 \x01(\v2I.WAConsumerApplicationParameterised.ConsumerApplication.PollUpdateMessageH\x00R\x11pollUpdateMessage\x12g\n" +
	"\veditMessage\x18\x13 \x01(\v2C.WAConsumerApplicationParameterised.ConsumerApplication.EditMessageH\x00R\veditMessageB\t\n" +
	"\acontent\x1a\xa2\x01\n" +
	"\vEditMessage\x123\n" +
	"\x03key\x18\x01 \x01(\v2!.WACommonParameterised.MessageKeyR\x03key\x12<\n" +
	"\amessage\x18\x02 \x01(\v2\".WACommonParameterised.MessageTextR\amessage\x12 \n" +
	"\vtimestampMS\x18\x03 \x01(\x03R\vtimestampMS\x1av\n" +
	"\x14PollAddOptionMessage\x12^\n" +
	"\n" +
	"pollOption\x18\x01 \x03(\v2>.WAConsumerApplicationParameterised.ConsumerApplication.OptionR\n" +
	"pollOption\x1ai\n" +
	"\x0fPollVoteMessage\x12(\n" +
	"\x0fselectedOptions\x18\x01 \x03(\fR\x0fselectedOptions\x12,\n" +
	"\x11senderTimestampMS\x18\x02 \x01(\x03R\x11senderTimestampMS\x1aD\n" +
	"\fPollEncValue\x12\x1e\n" +
	"\n" +
	"encPayload\x18\x01 \x01(\fR\n" +
	"encPayload\x12\x14\n" +
	"\x05encIV\x18\x02 \x01(\fR\x05encIV\x1a\xac\x02\n" +
	"\x11PollUpdateMessage\x12Y\n" +
	"\x16pollCreationMessageKey\x18\x01 \x01(\v2!.WACommonParameterised.MessageKeyR\x16pollCreationMessageKey\x12X\n" +
	"\x04vote\x18\x02 \x01(\v2D.WAConsumerApplicationParameterised.ConsumerApplication.PollEncValueR\x04vote\x12b\n" +
	"\taddOption\x18\x03 \x01(\v2D.WAConsumerApplicationParameterised.ConsumerApplication.PollEncValueR\taddOption\x1a\xd3\x01\n" +
	"\x13PollCreationMessage\x12\x16\n" +
	"\x06encKey\x18\x01 \x01(\fR\x06encKey\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12X\n" +
	"\aoptions\x18\x03 \x03(\v2>.WAConsumerApplicationParameterised.ConsumerApplication.OptionR\aoptions\x126\n" +
	"\x16selectableOptionsCount\x18\x04 \x01(\rR\x16selectableOptionsCount\x1a(\n" +
	"\x06Option\x12\x1e\n" +
	"\n" +
	"optionName\x18\x01 \x01(\tR\n" +
	"optionName\x1a\x86\x02\n" +
	"\x0fReactionMessage\x123\n" +
	"\x03key\x18\x01 \x01(\v2!.WACommonParameterised.MessageKeyR\x03key\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12 \n" +
	"\vgroupingKey\x18\x03 \x01(\tR\vgroupingKey\x12,\n" +
	"\x11senderTimestampMS\x18\x04 \x01(\x03R\x11senderTimestampMS\x12D\n" +
	"\x1dreactionMetadataDataclassData\x18\x05 \x01(\tR\x1dreactionMetadataDataclassData\x12\x14\n" +
	"\x05style\x18\x06 \x01(\x05R\x05style\x1aD\n" +
	"\rRevokeMessage\x123\n" +
	"\x03key\x18\x01 \x01(\v2!.WACommonParameterised.MessageKeyR\x03key\x1a\xfc\x01\n" +
	"\x0fViewOnceMessage\x12j\n" +
	"\fimageMessage\x18\x01 \x01(\v2D.WAConsumerApplicationParameterised.ConsumerApplication.ImageMessageH\x00R\fimageMessage\x12j\n" +
	"\fvideoMessage\x18\x02 \x01(\v2D.WAConsumerApplicationParameterised.ConsumerApplication.VideoMessageH\x00R\fvideoMessageB\x11\n" +
	"\x0fviewOnceContent\x1a\xfe\x01\n" +
	"\x12GroupInviteMessage\x12\x1a\n" +
	"\bgroupJID\x18\x01 \x01(\tR\bgroupJID\x12\x1e\n" +
	"\n" +
	"inviteCode\x18\x02 \x01(\tR\n" +
	"inviteCode\x12*\n" +
	"\x10inviteExpiration\x18\x03 \x01(\x03R\x10inviteExpiration\x12\x1c\n" +
	"\tgroupName\x18\x04 \x01(\tR\tgroupName\x12$\n" +
	"\rJPEGThumbnail\x18\x05 \x01(\fR\rJPEGThumbnail\x12<\n" +
	"\acaption\x18\x06 \x01(\v2\".WACommonParameterised.MessageTextR\acaption\x1a\x93\x03\n" +
	"\x13LiveLocationMessage\x12\\\n" +
	"\blocation\x18\x01 \x01(\<EMAIL>\blocation\x12*\n" +
	"\x10accuracyInMeters\x18\x02 \x01(\rR\x10accuracyInMeters\x12\x1e\n" +
	"\n" +
	"speedInMps\x18\x03 \x01(\x02R\n" +
	"speedInMps\x12L\n" +
	"!degreesClockwiseFromMagneticNorth\x18\x04 \x01(\rR!degreesClockwiseFromMagneticNorth\x12<\n" +
	"\acaption\x18\x05 \x01(\v2\".WACommonParameterised.MessageTextR\acaption\x12&\n" +
	"\x0esequenceNumber\x18\x06 \x01(\x03R\x0esequenceNumber\x12\x1e\n" +
	"\n" +
	"timeOffset\x18\a \x01(\rR\n" +
	"timeOffset\x1a\x9c\x01\n" +
	"\x14ContactsArrayMessage\x12 \n" +
	"\vdisplayName\x18\x01 \x01(\tR\vdisplayName\x12b\n" +
	"\bcontacts\x18\x02 \x03(\v2F.WAConsumerApplicationParameterised.ConsumerApplication.ContactMessageR\bcontacts\x1aN\n" +
	"\x0eContactMessage\x12<\n" +
	"\acontact\x18\x01 \x01(\v2\".WACommonParameterised.SubProtocolR\acontact\x1a\x96\x03\n" +
	"\x10StatusTextMesage\x12_\n" +
	"\x04text\x18\x01 \x01(\v2K.WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessageR\x04text\x12\x1a\n" +
	"\btextArgb\x18\x06 \x01(\aR\btextArgb\x12&\n" +
	"\x0ebackgroundArgb\x18\a \x01(\aR\x0ebackgroundArgb\x12e\n" +
	"\x04font\x18\b \x01(\x0e2Q.WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesage.FontTypeR\x04font\"v\n" +
	"\bFontType\x12\x0e\n" +
	"\n" +
	"SANS_SERIF\x10\x00\x12\t\n" +
	"\x05SERIF\x10\x01\x12\x13\n" +
	"\x0fNORICAN_REGULAR\x10\x02\x12\x11\n" +
	"\rBRYNDAN_WRITE\x10\x03\x12\x15\n" +
	"\x11BEBASNEUE_REGULAR\x10\x04\x12\x10\n" +
	"\fOSWALD_HEAVY\x10\x05\x1a\xac\x03\n" +
	"\x13ExtendedTextMessage\x126\n" +
	"\x04text\x18\x01 \x01(\v2\".WACommonParameterised.MessageTextR\x04text\x12 \n" +
	"\vmatchedText\x18\x02 \x01(\tR\vmatchedText\x12\"\n" +
	"\fcanonicalURL\x18\x03 \x01(\tR\fcanonicalURL\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12@\n" +
	"\tthumbnail\x18\x06 \x01(\v2\".WACommonParameterised.SubProtocolR\tthumbnail\x12y\n" +
	"\vpreviewType\x18\a \x01(\x0e2W.WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage.PreviewTypeR\vpreviewType\"\"\n" +
	"\vPreviewType\x12\b\n" +
	"\x04NONE\x10\x00\x12\t\n" +
	"\x05VIDEO\x10\x01\x1a\x89\x01\n" +
	"\x0fLocationMessage\x12\\\n" +
	"\blocation\x18\x01 \x01(\<EMAIL>\blocation\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\x1aN\n" +
	"\x0eStickerMessage\x12<\n" +
	"\asticker\x18\x01 \x01(\v2\".WACommonParameterised.SubProtocolR\asticker\x1am\n" +
	"\x0fDocumentMessage\x12>\n" +
	"\bdocument\x18\x01 \x01(\v2\".WACommonParameterised.SubProtocolR\bdocument\x12\x1a\n" +
	"\bfileName\x18\x02 \x01(\tR\bfileName\x1a\x86\x01\n" +
	"\fVideoMessage\x128\n" +
	"\x05video\x18\x01 \x01(\v2\".WACommonParameterised.SubProtocolR\x05video\x12<\n" +
	"\acaption\x18\x02 \x01(\v2\".WACommonParameterised.MessageTextR\acaption\x1aZ\n" +
	"\fAudioMessage\x128\n" +
	"\x05audio\x18\x01 \x01(\v2\".WACommonParameterised.SubProtocolR\x05audio\x12\x10\n" +
	"\x03PTT\x18\x02 \x01(\bR\x03PTT\x1a\x86\x01\n" +
	"\fImageMessage\x128\n" +
	"\x05image\x18\x01 \x01(\v2\".WACommonParameterised.SubProtocolR\x05image\x12<\n" +
	"\acaption\x18\x02 \x01(\v2\".WACommonParameterised.MessageTextR\acaption\x1a\xea\x01\n" +
	"\x15InteractiveAnnotation\x12^\n" +
	"\blocation\x18\x02 \x01(\<EMAIL>\x00R\blocation\x12g\n" +
	"\x0fpolygonVertices\x18\x01 \x03(\v2=.WAConsumerApplicationParameterised.ConsumerApplication.PointR\x0fpolygonVerticesB\b\n" +
	"\x06action\x1a#\n" +
	"\x05Point\x12\f\n" +
	"\x01x\x18\x01 \x01(\x01R\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\x01R\x01y\x1at\n" +
	"\bLocation\x12(\n" +
	"\x0fdegreesLatitude\x18\x01 \x01(\x01R\x0fdegreesLatitude\x12*\n" +
	"\x10degreesLongitude\x18\x02 \x01(\x01R\x10degreesLongitude\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x1aN\n" +
	"\fMediaPayload\x12>\n" +
	"\bprotocol\x18\x01 \x01(\v2\".WACommonParameterised.SubProtocolR\bprotocolB>Z<go.mau.fi/whatsmeow/proto/waConsumerApplicationParameterised"

var (
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescOnce sync.Once
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescData []byte
)

func file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescGZIP() []byte {
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescOnce.Do(func() {
		file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDesc), len(file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDesc)))
	})
	return file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDescData
}

var file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_goTypes = []any{
	(ConsumerApplication_Metadata_SpecialTextSize)(0),        // 0: WAConsumerApplicationParameterised.ConsumerApplication.Metadata.SpecialTextSize
	(ConsumerApplication_StatusTextMesage_FontType)(0),       // 1: WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesage.FontType
	(ConsumerApplication_ExtendedTextMessage_PreviewType)(0), // 2: WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage.PreviewType
	(*ConsumerApplication)(nil),                              // 3: WAConsumerApplicationParameterised.ConsumerApplication
	(*ConsumerApplication_Payload)(nil),                      // 4: WAConsumerApplicationParameterised.ConsumerApplication.Payload
	(*ConsumerApplication_SubProtocolPayload)(nil),           // 5: WAConsumerApplicationParameterised.ConsumerApplication.SubProtocolPayload
	(*ConsumerApplication_Metadata)(nil),                     // 6: WAConsumerApplicationParameterised.ConsumerApplication.Metadata
	(*ConsumerApplication_Signal)(nil),                       // 7: WAConsumerApplicationParameterised.ConsumerApplication.Signal
	(*ConsumerApplication_ApplicationData)(nil),              // 8: WAConsumerApplicationParameterised.ConsumerApplication.ApplicationData
	(*ConsumerApplication_Content)(nil),                      // 9: WAConsumerApplicationParameterised.ConsumerApplication.Content
	(*ConsumerApplication_EditMessage)(nil),                  // 10: WAConsumerApplicationParameterised.ConsumerApplication.EditMessage
	(*ConsumerApplication_PollAddOptionMessage)(nil),         // 11: WAConsumerApplicationParameterised.ConsumerApplication.PollAddOptionMessage
	(*ConsumerApplication_PollVoteMessage)(nil),              // 12: WAConsumerApplicationParameterised.ConsumerApplication.PollVoteMessage
	(*ConsumerApplication_PollEncValue)(nil),                 // 13: WAConsumerApplicationParameterised.ConsumerApplication.PollEncValue
	(*ConsumerApplication_PollUpdateMessage)(nil),            // 14: WAConsumerApplicationParameterised.ConsumerApplication.PollUpdateMessage
	(*ConsumerApplication_PollCreationMessage)(nil),          // 15: WAConsumerApplicationParameterised.ConsumerApplication.PollCreationMessage
	(*ConsumerApplication_Option)(nil),                       // 16: WAConsumerApplicationParameterised.ConsumerApplication.Option
	(*ConsumerApplication_ReactionMessage)(nil),              // 17: WAConsumerApplicationParameterised.ConsumerApplication.ReactionMessage
	(*ConsumerApplication_RevokeMessage)(nil),                // 18: WAConsumerApplicationParameterised.ConsumerApplication.RevokeMessage
	(*ConsumerApplication_ViewOnceMessage)(nil),              // 19: WAConsumerApplicationParameterised.ConsumerApplication.ViewOnceMessage
	(*ConsumerApplication_GroupInviteMessage)(nil),           // 20: WAConsumerApplicationParameterised.ConsumerApplication.GroupInviteMessage
	(*ConsumerApplication_LiveLocationMessage)(nil),          // 21: WAConsumerApplicationParameterised.ConsumerApplication.LiveLocationMessage
	(*ConsumerApplication_ContactsArrayMessage)(nil),         // 22: WAConsumerApplicationParameterised.ConsumerApplication.ContactsArrayMessage
	(*ConsumerApplication_ContactMessage)(nil),               // 23: WAConsumerApplicationParameterised.ConsumerApplication.ContactMessage
	(*ConsumerApplication_StatusTextMesage)(nil),             // 24: WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesage
	(*ConsumerApplication_ExtendedTextMessage)(nil),          // 25: WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage
	(*ConsumerApplication_LocationMessage)(nil),              // 26: WAConsumerApplicationParameterised.ConsumerApplication.LocationMessage
	(*ConsumerApplication_StickerMessage)(nil),               // 27: WAConsumerApplicationParameterised.ConsumerApplication.StickerMessage
	(*ConsumerApplication_DocumentMessage)(nil),              // 28: WAConsumerApplicationParameterised.ConsumerApplication.DocumentMessage
	(*ConsumerApplication_VideoMessage)(nil),                 // 29: WAConsumerApplicationParameterised.ConsumerApplication.VideoMessage
	(*ConsumerApplication_AudioMessage)(nil),                 // 30: WAConsumerApplicationParameterised.ConsumerApplication.AudioMessage
	(*ConsumerApplication_ImageMessage)(nil),                 // 31: WAConsumerApplicationParameterised.ConsumerApplication.ImageMessage
	(*ConsumerApplication_InteractiveAnnotation)(nil),        // 32: WAConsumerApplicationParameterised.ConsumerApplication.InteractiveAnnotation
	(*ConsumerApplication_Point)(nil),                        // 33: WAConsumerApplicationParameterised.ConsumerApplication.Point
	(*ConsumerApplication_Location)(nil),                     // 34: WAConsumerApplicationParameterised.ConsumerApplication.Location
	(*ConsumerApplication_MediaPayload)(nil),                 // 35: WAConsumerApplicationParameterised.ConsumerApplication.MediaPayload
	(waCommonParameterised.FutureProofBehavior)(0),           // 36: WACommonParameterised.FutureProofBehavior
	(*waCommonParameterised.MessageText)(nil),                // 37: WACommonParameterised.MessageText
	(*waCommonParameterised.MessageKey)(nil),                 // 38: WACommonParameterised.MessageKey
	(*waCommonParameterised.SubProtocol)(nil),                // 39: WACommonParameterised.SubProtocol
}
var file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_depIdxs = []int32{
	4,  // 0: WAConsumerApplicationParameterised.ConsumerApplication.payload:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Payload
	6,  // 1: WAConsumerApplicationParameterised.ConsumerApplication.metadata:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Metadata
	9,  // 2: WAConsumerApplicationParameterised.ConsumerApplication.Payload.content:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Content
	8,  // 3: WAConsumerApplicationParameterised.ConsumerApplication.Payload.applicationData:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ApplicationData
	7,  // 4: WAConsumerApplicationParameterised.ConsumerApplication.Payload.signal:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Signal
	5,  // 5: WAConsumerApplicationParameterised.ConsumerApplication.Payload.subProtocol:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.SubProtocolPayload
	36, // 6: WAConsumerApplicationParameterised.ConsumerApplication.SubProtocolPayload.futureProof:type_name -> WACommonParameterised.FutureProofBehavior
	0,  // 7: WAConsumerApplicationParameterised.ConsumerApplication.Metadata.specialTextSize:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Metadata.SpecialTextSize
	18, // 8: WAConsumerApplicationParameterised.ConsumerApplication.ApplicationData.revoke:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.RevokeMessage
	37, // 9: WAConsumerApplicationParameterised.ConsumerApplication.Content.messageText:type_name -> WACommonParameterised.MessageText
	31, // 10: WAConsumerApplicationParameterised.ConsumerApplication.Content.imageMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ImageMessage
	23, // 11: WAConsumerApplicationParameterised.ConsumerApplication.Content.contactMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ContactMessage
	26, // 12: WAConsumerApplicationParameterised.ConsumerApplication.Content.locationMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.LocationMessage
	25, // 13: WAConsumerApplicationParameterised.ConsumerApplication.Content.extendedTextMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage
	24, // 14: WAConsumerApplicationParameterised.ConsumerApplication.Content.statusTextMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesage
	28, // 15: WAConsumerApplicationParameterised.ConsumerApplication.Content.documentMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.DocumentMessage
	30, // 16: WAConsumerApplicationParameterised.ConsumerApplication.Content.audioMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.AudioMessage
	29, // 17: WAConsumerApplicationParameterised.ConsumerApplication.Content.videoMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.VideoMessage
	22, // 18: WAConsumerApplicationParameterised.ConsumerApplication.Content.contactsArrayMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ContactsArrayMessage
	21, // 19: WAConsumerApplicationParameterised.ConsumerApplication.Content.liveLocationMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.LiveLocationMessage
	27, // 20: WAConsumerApplicationParameterised.ConsumerApplication.Content.stickerMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.StickerMessage
	20, // 21: WAConsumerApplicationParameterised.ConsumerApplication.Content.groupInviteMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.GroupInviteMessage
	19, // 22: WAConsumerApplicationParameterised.ConsumerApplication.Content.viewOnceMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ViewOnceMessage
	17, // 23: WAConsumerApplicationParameterised.ConsumerApplication.Content.reactionMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ReactionMessage
	15, // 24: WAConsumerApplicationParameterised.ConsumerApplication.Content.pollCreationMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.PollCreationMessage
	14, // 25: WAConsumerApplicationParameterised.ConsumerApplication.Content.pollUpdateMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.PollUpdateMessage
	10, // 26: WAConsumerApplicationParameterised.ConsumerApplication.Content.editMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.EditMessage
	38, // 27: WAConsumerApplicationParameterised.ConsumerApplication.EditMessage.key:type_name -> WACommonParameterised.MessageKey
	37, // 28: WAConsumerApplicationParameterised.ConsumerApplication.EditMessage.message:type_name -> WACommonParameterised.MessageText
	16, // 29: WAConsumerApplicationParameterised.ConsumerApplication.PollAddOptionMessage.pollOption:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Option
	38, // 30: WAConsumerApplicationParameterised.ConsumerApplication.PollUpdateMessage.pollCreationMessageKey:type_name -> WACommonParameterised.MessageKey
	13, // 31: WAConsumerApplicationParameterised.ConsumerApplication.PollUpdateMessage.vote:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.PollEncValue
	13, // 32: WAConsumerApplicationParameterised.ConsumerApplication.PollUpdateMessage.addOption:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.PollEncValue
	16, // 33: WAConsumerApplicationParameterised.ConsumerApplication.PollCreationMessage.options:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Option
	38, // 34: WAConsumerApplicationParameterised.ConsumerApplication.ReactionMessage.key:type_name -> WACommonParameterised.MessageKey
	38, // 35: WAConsumerApplicationParameterised.ConsumerApplication.RevokeMessage.key:type_name -> WACommonParameterised.MessageKey
	31, // 36: WAConsumerApplicationParameterised.ConsumerApplication.ViewOnceMessage.imageMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ImageMessage
	29, // 37: WAConsumerApplicationParameterised.ConsumerApplication.ViewOnceMessage.videoMessage:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.VideoMessage
	37, // 38: WAConsumerApplicationParameterised.ConsumerApplication.GroupInviteMessage.caption:type_name -> WACommonParameterised.MessageText
	34, // 39: WAConsumerApplicationParameterised.ConsumerApplication.LiveLocationMessage.location:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Location
	37, // 40: WAConsumerApplicationParameterised.ConsumerApplication.LiveLocationMessage.caption:type_name -> WACommonParameterised.MessageText
	23, // 41: WAConsumerApplicationParameterised.ConsumerApplication.ContactsArrayMessage.contacts:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ContactMessage
	39, // 42: WAConsumerApplicationParameterised.ConsumerApplication.ContactMessage.contact:type_name -> WACommonParameterised.SubProtocol
	25, // 43: WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesage.text:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage
	1,  // 44: WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesage.font:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.StatusTextMesage.FontType
	37, // 45: WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage.text:type_name -> WACommonParameterised.MessageText
	39, // 46: WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage.thumbnail:type_name -> WACommonParameterised.SubProtocol
	2,  // 47: WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage.previewType:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.ExtendedTextMessage.PreviewType
	34, // 48: WAConsumerApplicationParameterised.ConsumerApplication.LocationMessage.location:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Location
	39, // 49: WAConsumerApplicationParameterised.ConsumerApplication.StickerMessage.sticker:type_name -> WACommonParameterised.SubProtocol
	39, // 50: WAConsumerApplicationParameterised.ConsumerApplication.DocumentMessage.document:type_name -> WACommonParameterised.SubProtocol
	39, // 51: WAConsumerApplicationParameterised.ConsumerApplication.VideoMessage.video:type_name -> WACommonParameterised.SubProtocol
	37, // 52: WAConsumerApplicationParameterised.ConsumerApplication.VideoMessage.caption:type_name -> WACommonParameterised.MessageText
	39, // 53: WAConsumerApplicationParameterised.ConsumerApplication.AudioMessage.audio:type_name -> WACommonParameterised.SubProtocol
	39, // 54: WAConsumerApplicationParameterised.ConsumerApplication.ImageMessage.image:type_name -> WACommonParameterised.SubProtocol
	37, // 55: WAConsumerApplicationParameterised.ConsumerApplication.ImageMessage.caption:type_name -> WACommonParameterised.MessageText
	34, // 56: WAConsumerApplicationParameterised.ConsumerApplication.InteractiveAnnotation.location:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Location
	33, // 57: WAConsumerApplicationParameterised.ConsumerApplication.InteractiveAnnotation.polygonVertices:type_name -> WAConsumerApplicationParameterised.ConsumerApplication.Point
	39, // 58: WAConsumerApplicationParameterised.ConsumerApplication.MediaPayload.protocol:type_name -> WACommonParameterised.SubProtocol
	59, // [59:59] is the sub-list for method output_type
	59, // [59:59] is the sub-list for method input_type
	59, // [59:59] is the sub-list for extension type_name
	59, // [59:59] is the sub-list for extension extendee
	0,  // [0:59] is the sub-list for field type_name
}

func init() { file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_init() }
func file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_init() {
	if File_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto != nil {
		return
	}
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[1].OneofWrappers = []any{
		(*ConsumerApplication_Payload_Content)(nil),
		(*ConsumerApplication_Payload_ApplicationData)(nil),
		(*ConsumerApplication_Payload_Signal)(nil),
		(*ConsumerApplication_Payload_SubProtocol)(nil),
	}
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[5].OneofWrappers = []any{
		(*ConsumerApplication_ApplicationData_Revoke)(nil),
	}
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[6].OneofWrappers = []any{
		(*ConsumerApplication_Content_MessageText)(nil),
		(*ConsumerApplication_Content_ImageMessage)(nil),
		(*ConsumerApplication_Content_ContactMessage)(nil),
		(*ConsumerApplication_Content_LocationMessage)(nil),
		(*ConsumerApplication_Content_ExtendedTextMessage)(nil),
		(*ConsumerApplication_Content_StatusTextMessage)(nil),
		(*ConsumerApplication_Content_DocumentMessage)(nil),
		(*ConsumerApplication_Content_AudioMessage)(nil),
		(*ConsumerApplication_Content_VideoMessage)(nil),
		(*ConsumerApplication_Content_ContactsArrayMessage)(nil),
		(*ConsumerApplication_Content_LiveLocationMessage)(nil),
		(*ConsumerApplication_Content_StickerMessage)(nil),
		(*ConsumerApplication_Content_GroupInviteMessage)(nil),
		(*ConsumerApplication_Content_ViewOnceMessage)(nil),
		(*ConsumerApplication_Content_ReactionMessage)(nil),
		(*ConsumerApplication_Content_PollCreationMessage)(nil),
		(*ConsumerApplication_Content_PollUpdateMessage)(nil),
		(*ConsumerApplication_Content_EditMessage)(nil),
	}
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[16].OneofWrappers = []any{
		(*ConsumerApplication_ViewOnceMessage_ImageMessage)(nil),
		(*ConsumerApplication_ViewOnceMessage_VideoMessage)(nil),
	}
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes[29].OneofWrappers = []any{
		(*ConsumerApplication_InteractiveAnnotation_Location)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDesc), len(file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_goTypes,
		DependencyIndexes: file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_depIdxs,
		EnumInfos:         file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_enumTypes,
		MessageInfos:      file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_msgTypes,
	}.Build()
	File_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto = out.File
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_goTypes = nil
	file_waConsumerApplicationParameterised_WAConsumerApplicationParameterised_proto_depIdxs = nil
}
