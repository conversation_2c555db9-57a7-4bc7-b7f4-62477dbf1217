// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waE2EGuest/WAWebProtobufsE2EGuest.proto

package waE2EGuest

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Message struct {
	state               protoimpl.MessageState       `protogen:"open.v1"`
	Conversation        *string                      `protobuf:"bytes,1,opt,name=conversation" json:"conversation,omitempty"`
	ExtendedTextMessage *Message_ExtendedTextMessage `protobuf:"bytes,6,opt,name=extendedTextMessage" json:"extendedTextMessage,omitempty"`
	MessageContextInfo  *MessageContextInfo          `protobuf:"bytes,35,opt,name=messageContextInfo" json:"messageContextInfo,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescGZIP(), []int{0}
}

func (x *Message) GetConversation() string {
	if x != nil && x.Conversation != nil {
		return *x.Conversation
	}
	return ""
}

func (x *Message) GetExtendedTextMessage() *Message_ExtendedTextMessage {
	if x != nil {
		return x.ExtendedTextMessage
	}
	return nil
}

func (x *Message) GetMessageContextInfo() *MessageContextInfo {
	if x != nil {
		return x.MessageContextInfo
	}
	return nil
}

type MessageContextInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageSecret []byte                 `protobuf:"bytes,3,opt,name=messageSecret" json:"messageSecret,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageContextInfo) Reset() {
	*x = MessageContextInfo{}
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageContextInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageContextInfo) ProtoMessage() {}

func (x *MessageContextInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageContextInfo.ProtoReflect.Descriptor instead.
func (*MessageContextInfo) Descriptor() ([]byte, []int) {
	return file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescGZIP(), []int{1}
}

func (x *MessageContextInfo) GetMessageSecret() []byte {
	if x != nil {
		return x.MessageSecret
	}
	return nil
}

type Message_ExtendedTextMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          *string                `protobuf:"bytes,1,opt,name=text" json:"text,omitempty"`
	ContextInfo   *Message_ContextInfo   `protobuf:"bytes,17,opt,name=contextInfo" json:"contextInfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message_ExtendedTextMessage) Reset() {
	*x = Message_ExtendedTextMessage{}
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message_ExtendedTextMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message_ExtendedTextMessage) ProtoMessage() {}

func (x *Message_ExtendedTextMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message_ExtendedTextMessage.ProtoReflect.Descriptor instead.
func (*Message_ExtendedTextMessage) Descriptor() ([]byte, []int) {
	return file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Message_ExtendedTextMessage) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

func (x *Message_ExtendedTextMessage) GetContextInfo() *Message_ContextInfo {
	if x != nil {
		return x.ContextInfo
	}
	return nil
}

type Message_ContextInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StanzaID      *string                `protobuf:"bytes,1,opt,name=stanzaID" json:"stanzaID,omitempty"`
	Participant   *string                `protobuf:"bytes,2,opt,name=participant" json:"participant,omitempty"`
	QuotedMessage *Message               `protobuf:"bytes,3,opt,name=quotedMessage" json:"quotedMessage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message_ContextInfo) Reset() {
	*x = Message_ContextInfo{}
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message_ContextInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message_ContextInfo) ProtoMessage() {}

func (x *Message_ContextInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message_ContextInfo.ProtoReflect.Descriptor instead.
func (*Message_ContextInfo) Descriptor() ([]byte, []int) {
	return file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Message_ContextInfo) GetStanzaID() string {
	if x != nil && x.StanzaID != nil {
		return *x.StanzaID
	}
	return ""
}

func (x *Message_ContextInfo) GetParticipant() string {
	if x != nil && x.Participant != nil {
		return *x.Participant
	}
	return ""
}

func (x *Message_ContextInfo) GetQuotedMessage() *Message {
	if x != nil {
		return x.QuotedMessage
	}
	return nil
}

var File_waE2EGuest_WAWebProtobufsE2EGuest_proto protoreflect.FileDescriptor

const file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDesc = "" +
	"\n" +
	"'waE2EGuest/WAWebProtobufsE2EGuest.proto\x12\x16WAWebProtobufsE2EGuest\"\xff\x03\n" +
	"\aMessage\x12\"\n" +
	"\fconversation\x18\x01 \x01(\tR\fconversation\x12e\n" +
	"\x13extendedTextMessage\x18\x06 \x01(\v23.WAWebProtobufsE2EGuest.Message.ExtendedTextMessageR\x13extendedTextMessage\x12Z\n" +
	"\x12messageContextInfo\x18# \x01(\v2*.WAWebProtobufsE2EGuest.MessageContextInfoR\x12messageContextInfo\x1ax\n" +
	"\x13ExtendedTextMessage\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12M\n" +
	"\vcontextInfo\x18\x11 \x01(\v2+.WAWebProtobufsE2EGuest.Message.ContextInfoR\vcontextInfo\x1a\x92\x01\n" +
	"\vContextInfo\x12\x1a\n" +
	"\bstanzaID\x18\x01 \x01(\tR\bstanzaID\x12 \n" +
	"\vparticipant\x18\x02 \x01(\tR\vparticipant\x12E\n" +
	"\rquotedMessage\x18\x03 \x01(\v2\x1f.WAWebProtobufsE2EGuest.MessageR\rquotedMessage\":\n" +
	"\x12MessageContextInfo\x12$\n" +
	"\rmessageSecret\x18\x03 \x01(\fR\rmessageSecretB&Z$go.mau.fi/whatsmeow/proto/waE2EGuest"

var (
	file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescOnce sync.Once
	file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescData []byte
)

func file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescGZIP() []byte {
	file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescOnce.Do(func() {
		file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDesc), len(file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDesc)))
	})
	return file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDescData
}

var file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_waE2EGuest_WAWebProtobufsE2EGuest_proto_goTypes = []any{
	(*Message)(nil),                     // 0: WAWebProtobufsE2EGuest.Message
	(*MessageContextInfo)(nil),          // 1: WAWebProtobufsE2EGuest.MessageContextInfo
	(*Message_ExtendedTextMessage)(nil), // 2: WAWebProtobufsE2EGuest.Message.ExtendedTextMessage
	(*Message_ContextInfo)(nil),         // 3: WAWebProtobufsE2EGuest.Message.ContextInfo
}
var file_waE2EGuest_WAWebProtobufsE2EGuest_proto_depIdxs = []int32{
	2, // 0: WAWebProtobufsE2EGuest.Message.extendedTextMessage:type_name -> WAWebProtobufsE2EGuest.Message.ExtendedTextMessage
	1, // 1: WAWebProtobufsE2EGuest.Message.messageContextInfo:type_name -> WAWebProtobufsE2EGuest.MessageContextInfo
	3, // 2: WAWebProtobufsE2EGuest.Message.ExtendedTextMessage.contextInfo:type_name -> WAWebProtobufsE2EGuest.Message.ContextInfo
	0, // 3: WAWebProtobufsE2EGuest.Message.ContextInfo.quotedMessage:type_name -> WAWebProtobufsE2EGuest.Message
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_waE2EGuest_WAWebProtobufsE2EGuest_proto_init() }
func file_waE2EGuest_WAWebProtobufsE2EGuest_proto_init() {
	if File_waE2EGuest_WAWebProtobufsE2EGuest_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDesc), len(file_waE2EGuest_WAWebProtobufsE2EGuest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waE2EGuest_WAWebProtobufsE2EGuest_proto_goTypes,
		DependencyIndexes: file_waE2EGuest_WAWebProtobufsE2EGuest_proto_depIdxs,
		MessageInfos:      file_waE2EGuest_WAWebProtobufsE2EGuest_proto_msgTypes,
	}.Build()
	File_waE2EGuest_WAWebProtobufsE2EGuest_proto = out.File
	file_waE2EGuest_WAWebProtobufsE2EGuest_proto_goTypes = nil
	file_waE2EGuest_WAWebProtobufsE2EGuest_proto_depIdxs = nil
}
