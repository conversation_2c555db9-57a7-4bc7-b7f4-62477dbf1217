// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waEphemeral/WAWebProtobufsEphemeral.proto

package waEphemeral

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EphemeralSetting struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Duration      *int32                 `protobuf:"fixed32,1,opt,name=duration" json:"duration,omitempty"`
	Timestamp     *int64                 `protobuf:"fixed64,2,opt,name=timestamp" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EphemeralSetting) Reset() {
	*x = EphemeralSetting{}
	mi := &file_waEphemeral_WAWebProtobufsEphemeral_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EphemeralSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EphemeralSetting) ProtoMessage() {}

func (x *EphemeralSetting) ProtoReflect() protoreflect.Message {
	mi := &file_waEphemeral_WAWebProtobufsEphemeral_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EphemeralSetting.ProtoReflect.Descriptor instead.
func (*EphemeralSetting) Descriptor() ([]byte, []int) {
	return file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDescGZIP(), []int{0}
}

func (x *EphemeralSetting) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *EphemeralSetting) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

var File_waEphemeral_WAWebProtobufsEphemeral_proto protoreflect.FileDescriptor

const file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDesc = "" +
	"\n" +
	")waEphemeral/WAWebProtobufsEphemeral.proto\x12\x17WAWebProtobufsEphemeral\"L\n" +
	"\x10EphemeralSetting\x12\x1a\n" +
	"\bduration\x18\x01 \x01(\x0fR\bduration\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x10R\ttimestampB'Z%go.mau.fi/whatsmeow/proto/waEphemeral"

var (
	file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDescOnce sync.Once
	file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDescData []byte
)

func file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDescGZIP() []byte {
	file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDescOnce.Do(func() {
		file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDesc), len(file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDesc)))
	})
	return file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDescData
}

var file_waEphemeral_WAWebProtobufsEphemeral_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_waEphemeral_WAWebProtobufsEphemeral_proto_goTypes = []any{
	(*EphemeralSetting)(nil), // 0: WAWebProtobufsEphemeral.EphemeralSetting
}
var file_waEphemeral_WAWebProtobufsEphemeral_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_waEphemeral_WAWebProtobufsEphemeral_proto_init() }
func file_waEphemeral_WAWebProtobufsEphemeral_proto_init() {
	if File_waEphemeral_WAWebProtobufsEphemeral_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDesc), len(file_waEphemeral_WAWebProtobufsEphemeral_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waEphemeral_WAWebProtobufsEphemeral_proto_goTypes,
		DependencyIndexes: file_waEphemeral_WAWebProtobufsEphemeral_proto_depIdxs,
		MessageInfos:      file_waEphemeral_WAWebProtobufsEphemeral_proto_msgTypes,
	}.Build()
	File_waEphemeral_WAWebProtobufsEphemeral_proto = out.File
	file_waEphemeral_WAWebProtobufsEphemeral_proto_goTypes = nil
	file_waEphemeral_WAWebProtobufsEphemeral_proto_depIdxs = nil
}
