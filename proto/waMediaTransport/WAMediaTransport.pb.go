// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waMediaTransport/WAMediaTransport.proto

package waMediaTransport

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waCommon "go.mau.fi/whatsmeow/proto/waCommon"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ImageTransport_Ancillary_HdType int32

const (
	ImageTransport_Ancillary_NONE  ImageTransport_Ancillary_HdType = 0
	ImageTransport_Ancillary_LQ_4K ImageTransport_Ancillary_HdType = 1
	ImageTransport_Ancillary_HQ_4K ImageTransport_Ancillary_HdType = 2
)

// Enum value maps for ImageTransport_Ancillary_HdType.
var (
	ImageTransport_Ancillary_HdType_name = map[int32]string{
		0: "NONE",
		1: "LQ_4K",
		2: "HQ_4K",
	}
	ImageTransport_Ancillary_HdType_value = map[string]int32{
		"NONE":  0,
		"LQ_4K": 1,
		"HQ_4K": 2,
	}
)

func (x ImageTransport_Ancillary_HdType) Enum() *ImageTransport_Ancillary_HdType {
	p := new(ImageTransport_Ancillary_HdType)
	*p = x
	return p
}

func (x ImageTransport_Ancillary_HdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImageTransport_Ancillary_HdType) Descriptor() protoreflect.EnumDescriptor {
	return file_waMediaTransport_WAMediaTransport_proto_enumTypes[0].Descriptor()
}

func (ImageTransport_Ancillary_HdType) Type() protoreflect.EnumType {
	return &file_waMediaTransport_WAMediaTransport_proto_enumTypes[0]
}

func (x ImageTransport_Ancillary_HdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ImageTransport_Ancillary_HdType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ImageTransport_Ancillary_HdType(num)
	return nil
}

// Deprecated: Use ImageTransport_Ancillary_HdType.Descriptor instead.
func (ImageTransport_Ancillary_HdType) EnumDescriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{1, 0, 0}
}

type VideoTransport_Ancillary_Attribution int32

const (
	VideoTransport_Ancillary_NONE  VideoTransport_Ancillary_Attribution = 0
	VideoTransport_Ancillary_GIPHY VideoTransport_Ancillary_Attribution = 1
	VideoTransport_Ancillary_TENOR VideoTransport_Ancillary_Attribution = 2
)

// Enum value maps for VideoTransport_Ancillary_Attribution.
var (
	VideoTransport_Ancillary_Attribution_name = map[int32]string{
		0: "NONE",
		1: "GIPHY",
		2: "TENOR",
	}
	VideoTransport_Ancillary_Attribution_value = map[string]int32{
		"NONE":  0,
		"GIPHY": 1,
		"TENOR": 2,
	}
)

func (x VideoTransport_Ancillary_Attribution) Enum() *VideoTransport_Ancillary_Attribution {
	p := new(VideoTransport_Ancillary_Attribution)
	*p = x
	return p
}

func (x VideoTransport_Ancillary_Attribution) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VideoTransport_Ancillary_Attribution) Descriptor() protoreflect.EnumDescriptor {
	return file_waMediaTransport_WAMediaTransport_proto_enumTypes[1].Descriptor()
}

func (VideoTransport_Ancillary_Attribution) Type() protoreflect.EnumType {
	return &file_waMediaTransport_WAMediaTransport_proto_enumTypes[1]
}

func (x VideoTransport_Ancillary_Attribution) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *VideoTransport_Ancillary_Attribution) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = VideoTransport_Ancillary_Attribution(num)
	return nil
}

// Deprecated: Use VideoTransport_Ancillary_Attribution.Descriptor instead.
func (VideoTransport_Ancillary_Attribution) EnumDescriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{2, 0, 0}
}

type AudioTransport_Ancillary_AvatarAudio_AnimationsType int32

const (
	AudioTransport_Ancillary_AvatarAudio_TALKING_A  AudioTransport_Ancillary_AvatarAudio_AnimationsType = 0
	AudioTransport_Ancillary_AvatarAudio_IDLE_A     AudioTransport_Ancillary_AvatarAudio_AnimationsType = 1
	AudioTransport_Ancillary_AvatarAudio_TALKING_B  AudioTransport_Ancillary_AvatarAudio_AnimationsType = 2
	AudioTransport_Ancillary_AvatarAudio_IDLE_B     AudioTransport_Ancillary_AvatarAudio_AnimationsType = 3
	AudioTransport_Ancillary_AvatarAudio_BACKGROUND AudioTransport_Ancillary_AvatarAudio_AnimationsType = 4
)

// Enum value maps for AudioTransport_Ancillary_AvatarAudio_AnimationsType.
var (
	AudioTransport_Ancillary_AvatarAudio_AnimationsType_name = map[int32]string{
		0: "TALKING_A",
		1: "IDLE_A",
		2: "TALKING_B",
		3: "IDLE_B",
		4: "BACKGROUND",
	}
	AudioTransport_Ancillary_AvatarAudio_AnimationsType_value = map[string]int32{
		"TALKING_A":  0,
		"IDLE_A":     1,
		"TALKING_B":  2,
		"IDLE_B":     3,
		"BACKGROUND": 4,
	}
)

func (x AudioTransport_Ancillary_AvatarAudio_AnimationsType) Enum() *AudioTransport_Ancillary_AvatarAudio_AnimationsType {
	p := new(AudioTransport_Ancillary_AvatarAudio_AnimationsType)
	*p = x
	return p
}

func (x AudioTransport_Ancillary_AvatarAudio_AnimationsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AudioTransport_Ancillary_AvatarAudio_AnimationsType) Descriptor() protoreflect.EnumDescriptor {
	return file_waMediaTransport_WAMediaTransport_proto_enumTypes[2].Descriptor()
}

func (AudioTransport_Ancillary_AvatarAudio_AnimationsType) Type() protoreflect.EnumType {
	return &file_waMediaTransport_WAMediaTransport_proto_enumTypes[2]
}

func (x AudioTransport_Ancillary_AvatarAudio_AnimationsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AudioTransport_Ancillary_AvatarAudio_AnimationsType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AudioTransport_Ancillary_AvatarAudio_AnimationsType(num)
	return nil
}

// Deprecated: Use AudioTransport_Ancillary_AvatarAudio_AnimationsType.Descriptor instead.
func (AudioTransport_Ancillary_AvatarAudio_AnimationsType) EnumDescriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{3, 0, 0, 0}
}

type AudioTransport_Integral_AudioFormat int32

const (
	AudioTransport_Integral_UNKNOWN AudioTransport_Integral_AudioFormat = 0
	AudioTransport_Integral_OPUS    AudioTransport_Integral_AudioFormat = 1
)

// Enum value maps for AudioTransport_Integral_AudioFormat.
var (
	AudioTransport_Integral_AudioFormat_name = map[int32]string{
		0: "UNKNOWN",
		1: "OPUS",
	}
	AudioTransport_Integral_AudioFormat_value = map[string]int32{
		"UNKNOWN": 0,
		"OPUS":    1,
	}
)

func (x AudioTransport_Integral_AudioFormat) Enum() *AudioTransport_Integral_AudioFormat {
	p := new(AudioTransport_Integral_AudioFormat)
	*p = x
	return p
}

func (x AudioTransport_Integral_AudioFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AudioTransport_Integral_AudioFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_waMediaTransport_WAMediaTransport_proto_enumTypes[3].Descriptor()
}

func (AudioTransport_Integral_AudioFormat) Type() protoreflect.EnumType {
	return &file_waMediaTransport_WAMediaTransport_proto_enumTypes[3]
}

func (x AudioTransport_Integral_AudioFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *AudioTransport_Integral_AudioFormat) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = AudioTransport_Integral_AudioFormat(num)
	return nil
}

// Deprecated: Use AudioTransport_Integral_AudioFormat.Descriptor instead.
func (AudioTransport_Integral_AudioFormat) EnumDescriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{3, 1, 0}
}

type WAMediaTransport struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Integral      *WAMediaTransport_Integral  `protobuf:"bytes,1,opt,name=integral" json:"integral,omitempty"`
	Ancillary     *WAMediaTransport_Ancillary `protobuf:"bytes,2,opt,name=ancillary" json:"ancillary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WAMediaTransport) Reset() {
	*x = WAMediaTransport{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WAMediaTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WAMediaTransport) ProtoMessage() {}

func (x *WAMediaTransport) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WAMediaTransport.ProtoReflect.Descriptor instead.
func (*WAMediaTransport) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{0}
}

func (x *WAMediaTransport) GetIntegral() *WAMediaTransport_Integral {
	if x != nil {
		return x.Integral
	}
	return nil
}

func (x *WAMediaTransport) GetAncillary() *WAMediaTransport_Ancillary {
	if x != nil {
		return x.Ancillary
	}
	return nil
}

type ImageTransport struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Integral      *ImageTransport_Integral  `protobuf:"bytes,1,opt,name=integral" json:"integral,omitempty"`
	Ancillary     *ImageTransport_Ancillary `protobuf:"bytes,2,opt,name=ancillary" json:"ancillary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageTransport) Reset() {
	*x = ImageTransport{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageTransport) ProtoMessage() {}

func (x *ImageTransport) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageTransport.ProtoReflect.Descriptor instead.
func (*ImageTransport) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{1}
}

func (x *ImageTransport) GetIntegral() *ImageTransport_Integral {
	if x != nil {
		return x.Integral
	}
	return nil
}

func (x *ImageTransport) GetAncillary() *ImageTransport_Ancillary {
	if x != nil {
		return x.Ancillary
	}
	return nil
}

type VideoTransport struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Integral      *VideoTransport_Integral  `protobuf:"bytes,1,opt,name=integral" json:"integral,omitempty"`
	Ancillary     *VideoTransport_Ancillary `protobuf:"bytes,2,opt,name=ancillary" json:"ancillary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoTransport) Reset() {
	*x = VideoTransport{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoTransport) ProtoMessage() {}

func (x *VideoTransport) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoTransport.ProtoReflect.Descriptor instead.
func (*VideoTransport) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{2}
}

func (x *VideoTransport) GetIntegral() *VideoTransport_Integral {
	if x != nil {
		return x.Integral
	}
	return nil
}

func (x *VideoTransport) GetAncillary() *VideoTransport_Ancillary {
	if x != nil {
		return x.Ancillary
	}
	return nil
}

type AudioTransport struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Integral      *AudioTransport_Integral  `protobuf:"bytes,1,opt,name=integral" json:"integral,omitempty"`
	Ancillary     *AudioTransport_Ancillary `protobuf:"bytes,2,opt,name=ancillary" json:"ancillary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioTransport) Reset() {
	*x = AudioTransport{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioTransport) ProtoMessage() {}

func (x *AudioTransport) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioTransport.ProtoReflect.Descriptor instead.
func (*AudioTransport) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{3}
}

func (x *AudioTransport) GetIntegral() *AudioTransport_Integral {
	if x != nil {
		return x.Integral
	}
	return nil
}

func (x *AudioTransport) GetAncillary() *AudioTransport_Ancillary {
	if x != nil {
		return x.Ancillary
	}
	return nil
}

type DocumentTransport struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Integral      *DocumentTransport_Integral  `protobuf:"bytes,1,opt,name=integral" json:"integral,omitempty"`
	Ancillary     *DocumentTransport_Ancillary `protobuf:"bytes,2,opt,name=ancillary" json:"ancillary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocumentTransport) Reset() {
	*x = DocumentTransport{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocumentTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentTransport) ProtoMessage() {}

func (x *DocumentTransport) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentTransport.ProtoReflect.Descriptor instead.
func (*DocumentTransport) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{4}
}

func (x *DocumentTransport) GetIntegral() *DocumentTransport_Integral {
	if x != nil {
		return x.Integral
	}
	return nil
}

func (x *DocumentTransport) GetAncillary() *DocumentTransport_Ancillary {
	if x != nil {
		return x.Ancillary
	}
	return nil
}

type StickerTransport struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Integral      *StickerTransport_Integral  `protobuf:"bytes,1,opt,name=integral" json:"integral,omitempty"`
	Ancillary     *StickerTransport_Ancillary `protobuf:"bytes,2,opt,name=ancillary" json:"ancillary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerTransport) Reset() {
	*x = StickerTransport{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerTransport) ProtoMessage() {}

func (x *StickerTransport) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerTransport.ProtoReflect.Descriptor instead.
func (*StickerTransport) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{5}
}

func (x *StickerTransport) GetIntegral() *StickerTransport_Integral {
	if x != nil {
		return x.Integral
	}
	return nil
}

func (x *StickerTransport) GetAncillary() *StickerTransport_Ancillary {
	if x != nil {
		return x.Ancillary
	}
	return nil
}

type ContactTransport struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Integral      *ContactTransport_Integral  `protobuf:"bytes,1,opt,name=integral" json:"integral,omitempty"`
	Ancillary     *ContactTransport_Ancillary `protobuf:"bytes,2,opt,name=ancillary" json:"ancillary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactTransport) Reset() {
	*x = ContactTransport{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactTransport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactTransport) ProtoMessage() {}

func (x *ContactTransport) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactTransport.ProtoReflect.Descriptor instead.
func (*ContactTransport) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{6}
}

func (x *ContactTransport) GetIntegral() *ContactTransport_Integral {
	if x != nil {
		return x.Integral
	}
	return nil
}

func (x *ContactTransport) GetAncillary() *ContactTransport_Ancillary {
	if x != nil {
		return x.Ancillary
	}
	return nil
}

type WAMediaTransport_Ancillary struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	FileLength    *uint64                               `protobuf:"varint,1,opt,name=fileLength" json:"fileLength,omitempty"`
	Mimetype      *string                               `protobuf:"bytes,2,opt,name=mimetype" json:"mimetype,omitempty"`
	Thumbnail     *WAMediaTransport_Ancillary_Thumbnail `protobuf:"bytes,3,opt,name=thumbnail" json:"thumbnail,omitempty"`
	ObjectID      *string                               `protobuf:"bytes,4,opt,name=objectID" json:"objectID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WAMediaTransport_Ancillary) Reset() {
	*x = WAMediaTransport_Ancillary{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WAMediaTransport_Ancillary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WAMediaTransport_Ancillary) ProtoMessage() {}

func (x *WAMediaTransport_Ancillary) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WAMediaTransport_Ancillary.ProtoReflect.Descriptor instead.
func (*WAMediaTransport_Ancillary) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{0, 0}
}

func (x *WAMediaTransport_Ancillary) GetFileLength() uint64 {
	if x != nil && x.FileLength != nil {
		return *x.FileLength
	}
	return 0
}

func (x *WAMediaTransport_Ancillary) GetMimetype() string {
	if x != nil && x.Mimetype != nil {
		return *x.Mimetype
	}
	return ""
}

func (x *WAMediaTransport_Ancillary) GetThumbnail() *WAMediaTransport_Ancillary_Thumbnail {
	if x != nil {
		return x.Thumbnail
	}
	return nil
}

func (x *WAMediaTransport_Ancillary) GetObjectID() string {
	if x != nil && x.ObjectID != nil {
		return *x.ObjectID
	}
	return ""
}

type WAMediaTransport_Integral struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	FileSHA256        []byte                 `protobuf:"bytes,1,opt,name=fileSHA256" json:"fileSHA256,omitempty"`
	MediaKey          []byte                 `protobuf:"bytes,2,opt,name=mediaKey" json:"mediaKey,omitempty"`
	FileEncSHA256     []byte                 `protobuf:"bytes,3,opt,name=fileEncSHA256" json:"fileEncSHA256,omitempty"`
	DirectPath        *string                `protobuf:"bytes,4,opt,name=directPath" json:"directPath,omitempty"`
	MediaKeyTimestamp *int64                 `protobuf:"varint,5,opt,name=mediaKeyTimestamp" json:"mediaKeyTimestamp,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *WAMediaTransport_Integral) Reset() {
	*x = WAMediaTransport_Integral{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WAMediaTransport_Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WAMediaTransport_Integral) ProtoMessage() {}

func (x *WAMediaTransport_Integral) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WAMediaTransport_Integral.ProtoReflect.Descriptor instead.
func (*WAMediaTransport_Integral) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{0, 1}
}

func (x *WAMediaTransport_Integral) GetFileSHA256() []byte {
	if x != nil {
		return x.FileSHA256
	}
	return nil
}

func (x *WAMediaTransport_Integral) GetMediaKey() []byte {
	if x != nil {
		return x.MediaKey
	}
	return nil
}

func (x *WAMediaTransport_Integral) GetFileEncSHA256() []byte {
	if x != nil {
		return x.FileEncSHA256
	}
	return nil
}

func (x *WAMediaTransport_Integral) GetDirectPath() string {
	if x != nil && x.DirectPath != nil {
		return *x.DirectPath
	}
	return ""
}

func (x *WAMediaTransport_Integral) GetMediaKeyTimestamp() int64 {
	if x != nil && x.MediaKeyTimestamp != nil {
		return *x.MediaKeyTimestamp
	}
	return 0
}

type WAMediaTransport_Ancillary_Thumbnail struct {
	state                 protoimpl.MessageState                                      `protogen:"open.v1"`
	JPEGThumbnail         []byte                                                      `protobuf:"bytes,1,opt,name=JPEGThumbnail" json:"JPEGThumbnail,omitempty"`
	DownloadableThumbnail *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail `protobuf:"bytes,2,opt,name=downloadableThumbnail" json:"downloadableThumbnail,omitempty"`
	ThumbnailWidth        *uint32                                                     `protobuf:"varint,3,opt,name=thumbnailWidth" json:"thumbnailWidth,omitempty"`
	ThumbnailHeight       *uint32                                                     `protobuf:"varint,4,opt,name=thumbnailHeight" json:"thumbnailHeight,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *WAMediaTransport_Ancillary_Thumbnail) Reset() {
	*x = WAMediaTransport_Ancillary_Thumbnail{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WAMediaTransport_Ancillary_Thumbnail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WAMediaTransport_Ancillary_Thumbnail) ProtoMessage() {}

func (x *WAMediaTransport_Ancillary_Thumbnail) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WAMediaTransport_Ancillary_Thumbnail.ProtoReflect.Descriptor instead.
func (*WAMediaTransport_Ancillary_Thumbnail) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *WAMediaTransport_Ancillary_Thumbnail) GetJPEGThumbnail() []byte {
	if x != nil {
		return x.JPEGThumbnail
	}
	return nil
}

func (x *WAMediaTransport_Ancillary_Thumbnail) GetDownloadableThumbnail() *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail {
	if x != nil {
		return x.DownloadableThumbnail
	}
	return nil
}

func (x *WAMediaTransport_Ancillary_Thumbnail) GetThumbnailWidth() uint32 {
	if x != nil && x.ThumbnailWidth != nil {
		return *x.ThumbnailWidth
	}
	return 0
}

func (x *WAMediaTransport_Ancillary_Thumbnail) GetThumbnailHeight() uint32 {
	if x != nil && x.ThumbnailHeight != nil {
		return *x.ThumbnailHeight
	}
	return 0
}

type WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	FileSHA256            []byte                 `protobuf:"bytes,1,opt,name=fileSHA256" json:"fileSHA256,omitempty"`
	FileEncSHA256         []byte                 `protobuf:"bytes,2,opt,name=fileEncSHA256" json:"fileEncSHA256,omitempty"`
	DirectPath            *string                `protobuf:"bytes,3,opt,name=directPath" json:"directPath,omitempty"`
	MediaKey              []byte                 `protobuf:"bytes,4,opt,name=mediaKey" json:"mediaKey,omitempty"`
	MediaKeyTimestamp     *int64                 `protobuf:"varint,5,opt,name=mediaKeyTimestamp" json:"mediaKeyTimestamp,omitempty"`
	ObjectID              *string                `protobuf:"bytes,6,opt,name=objectID" json:"objectID,omitempty"`
	ThumbnailScansSidecar []byte                 `protobuf:"bytes,7,opt,name=thumbnailScansSidecar" json:"thumbnailScansSidecar,omitempty"`
	ThumbnailScanLengths  []uint32               `protobuf:"varint,8,rep,name=thumbnailScanLengths" json:"thumbnailScanLengths,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) Reset() {
	*x = WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) ProtoMessage() {}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail.ProtoReflect.Descriptor instead.
func (*WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetFileSHA256() []byte {
	if x != nil {
		return x.FileSHA256
	}
	return nil
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetFileEncSHA256() []byte {
	if x != nil {
		return x.FileEncSHA256
	}
	return nil
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetDirectPath() string {
	if x != nil && x.DirectPath != nil {
		return *x.DirectPath
	}
	return ""
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetMediaKey() []byte {
	if x != nil {
		return x.MediaKey
	}
	return nil
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetMediaKeyTimestamp() int64 {
	if x != nil && x.MediaKeyTimestamp != nil {
		return *x.MediaKeyTimestamp
	}
	return 0
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetObjectID() string {
	if x != nil && x.ObjectID != nil {
		return *x.ObjectID
	}
	return ""
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetThumbnailScansSidecar() []byte {
	if x != nil {
		return x.ThumbnailScansSidecar
	}
	return nil
}

func (x *WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail) GetThumbnailScanLengths() []uint32 {
	if x != nil {
		return x.ThumbnailScanLengths
	}
	return nil
}

type ImageTransport_Ancillary struct {
	state                 protoimpl.MessageState           `protogen:"open.v1"`
	Height                *uint32                          `protobuf:"varint,1,opt,name=height" json:"height,omitempty"`
	Width                 *uint32                          `protobuf:"varint,2,opt,name=width" json:"width,omitempty"`
	ScansSidecar          []byte                           `protobuf:"bytes,3,opt,name=scansSidecar" json:"scansSidecar,omitempty"`
	ScanLengths           []uint32                         `protobuf:"varint,4,rep,name=scanLengths" json:"scanLengths,omitempty"`
	MidQualityFileSHA256  []byte                           `protobuf:"bytes,5,opt,name=midQualityFileSHA256" json:"midQualityFileSHA256,omitempty"`
	HdType                *ImageTransport_Ancillary_HdType `protobuf:"varint,6,opt,name=hdType,enum=WAMediaTransport.ImageTransport_Ancillary_HdType" json:"hdType,omitempty"`
	MemoriesConceptScores []float32                        `protobuf:"fixed32,7,rep,packed,name=memoriesConceptScores" json:"memoriesConceptScores,omitempty"`
	MemoriesConceptIDs    []uint32                         `protobuf:"varint,8,rep,packed,name=memoriesConceptIDs" json:"memoriesConceptIDs,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ImageTransport_Ancillary) Reset() {
	*x = ImageTransport_Ancillary{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageTransport_Ancillary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageTransport_Ancillary) ProtoMessage() {}

func (x *ImageTransport_Ancillary) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageTransport_Ancillary.ProtoReflect.Descriptor instead.
func (*ImageTransport_Ancillary) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ImageTransport_Ancillary) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *ImageTransport_Ancillary) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *ImageTransport_Ancillary) GetScansSidecar() []byte {
	if x != nil {
		return x.ScansSidecar
	}
	return nil
}

func (x *ImageTransport_Ancillary) GetScanLengths() []uint32 {
	if x != nil {
		return x.ScanLengths
	}
	return nil
}

func (x *ImageTransport_Ancillary) GetMidQualityFileSHA256() []byte {
	if x != nil {
		return x.MidQualityFileSHA256
	}
	return nil
}

func (x *ImageTransport_Ancillary) GetHdType() ImageTransport_Ancillary_HdType {
	if x != nil && x.HdType != nil {
		return *x.HdType
	}
	return ImageTransport_Ancillary_NONE
}

func (x *ImageTransport_Ancillary) GetMemoriesConceptScores() []float32 {
	if x != nil {
		return x.MemoriesConceptScores
	}
	return nil
}

func (x *ImageTransport_Ancillary) GetMemoriesConceptIDs() []uint32 {
	if x != nil {
		return x.MemoriesConceptIDs
	}
	return nil
}

type ImageTransport_Integral struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Transport     *WAMediaTransport      `protobuf:"bytes,1,opt,name=transport" json:"transport,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageTransport_Integral) Reset() {
	*x = ImageTransport_Integral{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageTransport_Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageTransport_Integral) ProtoMessage() {}

func (x *ImageTransport_Integral) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageTransport_Integral.ProtoReflect.Descriptor instead.
func (*ImageTransport_Integral) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{1, 1}
}

func (x *ImageTransport_Integral) GetTransport() *WAMediaTransport {
	if x != nil {
		return x.Transport
	}
	return nil
}

type VideoTransport_Ancillary struct {
	state              protoimpl.MessageState                `protogen:"open.v1"`
	Seconds            *uint32                               `protobuf:"varint,1,opt,name=seconds" json:"seconds,omitempty"`
	Caption            *waCommon.MessageText                 `protobuf:"bytes,2,opt,name=caption" json:"caption,omitempty"`
	GifPlayback        *bool                                 `protobuf:"varint,3,opt,name=gifPlayback" json:"gifPlayback,omitempty"`
	Height             *uint32                               `protobuf:"varint,4,opt,name=height" json:"height,omitempty"`
	Width              *uint32                               `protobuf:"varint,5,opt,name=width" json:"width,omitempty"`
	Sidecar            []byte                                `protobuf:"bytes,6,opt,name=sidecar" json:"sidecar,omitempty"`
	GifAttribution     *VideoTransport_Ancillary_Attribution `protobuf:"varint,7,opt,name=gifAttribution,enum=WAMediaTransport.VideoTransport_Ancillary_Attribution" json:"gifAttribution,omitempty"`
	AccessibilityLabel *string                               `protobuf:"bytes,8,opt,name=accessibilityLabel" json:"accessibilityLabel,omitempty"`
	IsHd               *bool                                 `protobuf:"varint,9,opt,name=isHd" json:"isHd,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *VideoTransport_Ancillary) Reset() {
	*x = VideoTransport_Ancillary{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoTransport_Ancillary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoTransport_Ancillary) ProtoMessage() {}

func (x *VideoTransport_Ancillary) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoTransport_Ancillary.ProtoReflect.Descriptor instead.
func (*VideoTransport_Ancillary) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{2, 0}
}

func (x *VideoTransport_Ancillary) GetSeconds() uint32 {
	if x != nil && x.Seconds != nil {
		return *x.Seconds
	}
	return 0
}

func (x *VideoTransport_Ancillary) GetCaption() *waCommon.MessageText {
	if x != nil {
		return x.Caption
	}
	return nil
}

func (x *VideoTransport_Ancillary) GetGifPlayback() bool {
	if x != nil && x.GifPlayback != nil {
		return *x.GifPlayback
	}
	return false
}

func (x *VideoTransport_Ancillary) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *VideoTransport_Ancillary) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *VideoTransport_Ancillary) GetSidecar() []byte {
	if x != nil {
		return x.Sidecar
	}
	return nil
}

func (x *VideoTransport_Ancillary) GetGifAttribution() VideoTransport_Ancillary_Attribution {
	if x != nil && x.GifAttribution != nil {
		return *x.GifAttribution
	}
	return VideoTransport_Ancillary_NONE
}

func (x *VideoTransport_Ancillary) GetAccessibilityLabel() string {
	if x != nil && x.AccessibilityLabel != nil {
		return *x.AccessibilityLabel
	}
	return ""
}

func (x *VideoTransport_Ancillary) GetIsHd() bool {
	if x != nil && x.IsHd != nil {
		return *x.IsHd
	}
	return false
}

type VideoTransport_Integral struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Transport     *WAMediaTransport      `protobuf:"bytes,1,opt,name=transport" json:"transport,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoTransport_Integral) Reset() {
	*x = VideoTransport_Integral{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoTransport_Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoTransport_Integral) ProtoMessage() {}

func (x *VideoTransport_Integral) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoTransport_Integral.ProtoReflect.Descriptor instead.
func (*VideoTransport_Integral) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{2, 1}
}

func (x *VideoTransport_Integral) GetTransport() *WAMediaTransport {
	if x != nil {
		return x.Transport
	}
	return nil
}

type AudioTransport_Ancillary struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Seconds       *uint32                               `protobuf:"varint,1,opt,name=seconds" json:"seconds,omitempty"`
	AvatarAudio   *AudioTransport_Ancillary_AvatarAudio `protobuf:"bytes,2,opt,name=avatarAudio" json:"avatarAudio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioTransport_Ancillary) Reset() {
	*x = AudioTransport_Ancillary{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioTransport_Ancillary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioTransport_Ancillary) ProtoMessage() {}

func (x *AudioTransport_Ancillary) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioTransport_Ancillary.ProtoReflect.Descriptor instead.
func (*AudioTransport_Ancillary) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AudioTransport_Ancillary) GetSeconds() uint32 {
	if x != nil && x.Seconds != nil {
		return *x.Seconds
	}
	return 0
}

func (x *AudioTransport_Ancillary) GetAvatarAudio() *AudioTransport_Ancillary_AvatarAudio {
	if x != nil {
		return x.AvatarAudio
	}
	return nil
}

type AudioTransport_Integral struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Transport     *WAMediaTransport                    `protobuf:"bytes,1,opt,name=transport" json:"transport,omitempty"`
	AudioFormat   *AudioTransport_Integral_AudioFormat `protobuf:"varint,2,opt,name=audioFormat,enum=WAMediaTransport.AudioTransport_Integral_AudioFormat" json:"audioFormat,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioTransport_Integral) Reset() {
	*x = AudioTransport_Integral{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioTransport_Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioTransport_Integral) ProtoMessage() {}

func (x *AudioTransport_Integral) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioTransport_Integral.ProtoReflect.Descriptor instead.
func (*AudioTransport_Integral) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{3, 1}
}

func (x *AudioTransport_Integral) GetTransport() *WAMediaTransport {
	if x != nil {
		return x.Transport
	}
	return nil
}

func (x *AudioTransport_Integral) GetAudioFormat() AudioTransport_Integral_AudioFormat {
	if x != nil && x.AudioFormat != nil {
		return *x.AudioFormat
	}
	return AudioTransport_Integral_UNKNOWN
}

type AudioTransport_Ancillary_AvatarAudio struct {
	state            protoimpl.MessageState                                               `protogen:"open.v1"`
	PoseID           *uint32                                                              `protobuf:"varint,1,opt,name=poseID" json:"poseID,omitempty"`
	AvatarAnimations []*AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations `protobuf:"bytes,2,rep,name=avatarAnimations" json:"avatarAnimations,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AudioTransport_Ancillary_AvatarAudio) Reset() {
	*x = AudioTransport_Ancillary_AvatarAudio{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioTransport_Ancillary_AvatarAudio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioTransport_Ancillary_AvatarAudio) ProtoMessage() {}

func (x *AudioTransport_Ancillary_AvatarAudio) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioTransport_Ancillary_AvatarAudio.ProtoReflect.Descriptor instead.
func (*AudioTransport_Ancillary_AvatarAudio) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *AudioTransport_Ancillary_AvatarAudio) GetPoseID() uint32 {
	if x != nil && x.PoseID != nil {
		return *x.PoseID
	}
	return 0
}

func (x *AudioTransport_Ancillary_AvatarAudio) GetAvatarAnimations() []*AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations {
	if x != nil {
		return x.AvatarAnimations
	}
	return nil
}

type AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations struct {
	state             protoimpl.MessageState                               `protogen:"open.v1"`
	FileSHA256        []byte                                               `protobuf:"bytes,1,opt,name=fileSHA256" json:"fileSHA256,omitempty"`
	FileEncSHA256     []byte                                               `protobuf:"bytes,2,opt,name=fileEncSHA256" json:"fileEncSHA256,omitempty"`
	DirectPath        *string                                              `protobuf:"bytes,3,opt,name=directPath" json:"directPath,omitempty"`
	MediaKey          []byte                                               `protobuf:"bytes,4,opt,name=mediaKey" json:"mediaKey,omitempty"`
	MediaKeyTimestamp *int64                                               `protobuf:"varint,5,opt,name=mediaKeyTimestamp" json:"mediaKeyTimestamp,omitempty"`
	ObjectID          *string                                              `protobuf:"bytes,6,opt,name=objectID" json:"objectID,omitempty"`
	AnimationsType    *AudioTransport_Ancillary_AvatarAudio_AnimationsType `protobuf:"varint,7,opt,name=animationsType,enum=WAMediaTransport.AudioTransport_Ancillary_AvatarAudio_AnimationsType" json:"animationsType,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) Reset() {
	*x = AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) ProtoMessage() {}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations.ProtoReflect.Descriptor instead.
func (*AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{3, 0, 0, 0}
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) GetFileSHA256() []byte {
	if x != nil {
		return x.FileSHA256
	}
	return nil
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) GetFileEncSHA256() []byte {
	if x != nil {
		return x.FileEncSHA256
	}
	return nil
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) GetDirectPath() string {
	if x != nil && x.DirectPath != nil {
		return *x.DirectPath
	}
	return ""
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) GetMediaKey() []byte {
	if x != nil {
		return x.MediaKey
	}
	return nil
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) GetMediaKeyTimestamp() int64 {
	if x != nil && x.MediaKeyTimestamp != nil {
		return *x.MediaKeyTimestamp
	}
	return 0
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) GetObjectID() string {
	if x != nil && x.ObjectID != nil {
		return *x.ObjectID
	}
	return ""
}

func (x *AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations) GetAnimationsType() AudioTransport_Ancillary_AvatarAudio_AnimationsType {
	if x != nil && x.AnimationsType != nil {
		return *x.AnimationsType
	}
	return AudioTransport_Ancillary_AvatarAudio_TALKING_A
}

type DocumentTransport_Ancillary struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageCount     *uint32                `protobuf:"varint,1,opt,name=pageCount" json:"pageCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocumentTransport_Ancillary) Reset() {
	*x = DocumentTransport_Ancillary{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocumentTransport_Ancillary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentTransport_Ancillary) ProtoMessage() {}

func (x *DocumentTransport_Ancillary) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentTransport_Ancillary.ProtoReflect.Descriptor instead.
func (*DocumentTransport_Ancillary) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{4, 0}
}

func (x *DocumentTransport_Ancillary) GetPageCount() uint32 {
	if x != nil && x.PageCount != nil {
		return *x.PageCount
	}
	return 0
}

type DocumentTransport_Integral struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Transport     *WAMediaTransport      `protobuf:"bytes,1,opt,name=transport" json:"transport,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocumentTransport_Integral) Reset() {
	*x = DocumentTransport_Integral{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocumentTransport_Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentTransport_Integral) ProtoMessage() {}

func (x *DocumentTransport_Integral) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentTransport_Integral.ProtoReflect.Descriptor instead.
func (*DocumentTransport_Integral) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{4, 1}
}

func (x *DocumentTransport_Integral) GetTransport() *WAMediaTransport {
	if x != nil {
		return x.Transport
	}
	return nil
}

type StickerTransport_Ancillary struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PageCount          *uint32                `protobuf:"varint,1,opt,name=pageCount" json:"pageCount,omitempty"`
	Height             *uint32                `protobuf:"varint,2,opt,name=height" json:"height,omitempty"`
	Width              *uint32                `protobuf:"varint,3,opt,name=width" json:"width,omitempty"`
	FirstFrameLength   *uint32                `protobuf:"varint,4,opt,name=firstFrameLength" json:"firstFrameLength,omitempty"`
	FirstFrameSidecar  []byte                 `protobuf:"bytes,5,opt,name=firstFrameSidecar" json:"firstFrameSidecar,omitempty"`
	MustacheText       *string                `protobuf:"bytes,6,opt,name=mustacheText" json:"mustacheText,omitempty"`
	IsThirdParty       *bool                  `protobuf:"varint,7,opt,name=isThirdParty" json:"isThirdParty,omitempty"`
	ReceiverFetchID    *string                `protobuf:"bytes,8,opt,name=receiverFetchID" json:"receiverFetchID,omitempty"`
	AccessibilityLabel *string                `protobuf:"bytes,9,opt,name=accessibilityLabel" json:"accessibilityLabel,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *StickerTransport_Ancillary) Reset() {
	*x = StickerTransport_Ancillary{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerTransport_Ancillary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerTransport_Ancillary) ProtoMessage() {}

func (x *StickerTransport_Ancillary) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerTransport_Ancillary.ProtoReflect.Descriptor instead.
func (*StickerTransport_Ancillary) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{5, 0}
}

func (x *StickerTransport_Ancillary) GetPageCount() uint32 {
	if x != nil && x.PageCount != nil {
		return *x.PageCount
	}
	return 0
}

func (x *StickerTransport_Ancillary) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *StickerTransport_Ancillary) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *StickerTransport_Ancillary) GetFirstFrameLength() uint32 {
	if x != nil && x.FirstFrameLength != nil {
		return *x.FirstFrameLength
	}
	return 0
}

func (x *StickerTransport_Ancillary) GetFirstFrameSidecar() []byte {
	if x != nil {
		return x.FirstFrameSidecar
	}
	return nil
}

func (x *StickerTransport_Ancillary) GetMustacheText() string {
	if x != nil && x.MustacheText != nil {
		return *x.MustacheText
	}
	return ""
}

func (x *StickerTransport_Ancillary) GetIsThirdParty() bool {
	if x != nil && x.IsThirdParty != nil {
		return *x.IsThirdParty
	}
	return false
}

func (x *StickerTransport_Ancillary) GetReceiverFetchID() string {
	if x != nil && x.ReceiverFetchID != nil {
		return *x.ReceiverFetchID
	}
	return ""
}

func (x *StickerTransport_Ancillary) GetAccessibilityLabel() string {
	if x != nil && x.AccessibilityLabel != nil {
		return *x.AccessibilityLabel
	}
	return ""
}

type StickerTransport_Integral struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Transport       *WAMediaTransport      `protobuf:"bytes,1,opt,name=transport" json:"transport,omitempty"`
	IsAnimated      *bool                  `protobuf:"varint,2,opt,name=isAnimated" json:"isAnimated,omitempty"`
	ReceiverFetchID *string                `protobuf:"bytes,3,opt,name=receiverFetchID" json:"receiverFetchID,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StickerTransport_Integral) Reset() {
	*x = StickerTransport_Integral{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerTransport_Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerTransport_Integral) ProtoMessage() {}

func (x *StickerTransport_Integral) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerTransport_Integral.ProtoReflect.Descriptor instead.
func (*StickerTransport_Integral) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{5, 1}
}

func (x *StickerTransport_Integral) GetTransport() *WAMediaTransport {
	if x != nil {
		return x.Transport
	}
	return nil
}

func (x *StickerTransport_Integral) GetIsAnimated() bool {
	if x != nil && x.IsAnimated != nil {
		return *x.IsAnimated
	}
	return false
}

func (x *StickerTransport_Integral) GetReceiverFetchID() string {
	if x != nil && x.ReceiverFetchID != nil {
		return *x.ReceiverFetchID
	}
	return ""
}

type ContactTransport_Ancillary struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DisplayName   *string                `protobuf:"bytes,1,opt,name=displayName" json:"displayName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactTransport_Ancillary) Reset() {
	*x = ContactTransport_Ancillary{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactTransport_Ancillary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactTransport_Ancillary) ProtoMessage() {}

func (x *ContactTransport_Ancillary) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactTransport_Ancillary.ProtoReflect.Descriptor instead.
func (*ContactTransport_Ancillary) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ContactTransport_Ancillary) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

type ContactTransport_Integral struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Contact:
	//
	//	*ContactTransport_Integral_Vcard
	//	*ContactTransport_Integral_DownloadableVcard
	Contact       isContactTransport_Integral_Contact `protobuf_oneof:"contact"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactTransport_Integral) Reset() {
	*x = ContactTransport_Integral{}
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactTransport_Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactTransport_Integral) ProtoMessage() {}

func (x *ContactTransport_Integral) ProtoReflect() protoreflect.Message {
	mi := &file_waMediaTransport_WAMediaTransport_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactTransport_Integral.ProtoReflect.Descriptor instead.
func (*ContactTransport_Integral) Descriptor() ([]byte, []int) {
	return file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP(), []int{6, 1}
}

func (x *ContactTransport_Integral) GetContact() isContactTransport_Integral_Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *ContactTransport_Integral) GetVcard() string {
	if x != nil {
		if x, ok := x.Contact.(*ContactTransport_Integral_Vcard); ok {
			return x.Vcard
		}
	}
	return ""
}

func (x *ContactTransport_Integral) GetDownloadableVcard() *WAMediaTransport {
	if x != nil {
		if x, ok := x.Contact.(*ContactTransport_Integral_DownloadableVcard); ok {
			return x.DownloadableVcard
		}
	}
	return nil
}

type isContactTransport_Integral_Contact interface {
	isContactTransport_Integral_Contact()
}

type ContactTransport_Integral_Vcard struct {
	Vcard string `protobuf:"bytes,1,opt,name=vcard,oneof"`
}

type ContactTransport_Integral_DownloadableVcard struct {
	DownloadableVcard *WAMediaTransport `protobuf:"bytes,2,opt,name=downloadableVcard,oneof"`
}

func (*ContactTransport_Integral_Vcard) isContactTransport_Integral_Contact() {}

func (*ContactTransport_Integral_DownloadableVcard) isContactTransport_Integral_Contact() {}

var File_waMediaTransport_WAMediaTransport_proto protoreflect.FileDescriptor

const file_waMediaTransport_WAMediaTransport_proto_rawDesc = "" +
	"\n" +
	"'waMediaTransport/WAMediaTransport.proto\x12\x10WAMediaTransport\x1a\x17waCommon/WACommon.proto\"\xfb\b\n" +
	"\x10WAMediaTransport\x12G\n" +
	"\bintegral\x18\x01 \x01(\v2+.WAMediaTransport.WAMediaTransport.IntegralR\bintegral\x12J\n" +
	"\tancillary\x18\x02 \x01(\v2,.WAMediaTransport.WAMediaTransport.AncillaryR\tancillary\x1a\x94\x06\n" +
	"\tAncillary\x12\x1e\n" +
	"\n" +
	"fileLength\x18\x01 \x01(\x04R\n" +
	"fileLength\x12\x1a\n" +
	"\bmimetype\x18\x02 \x01(\tR\bmimetype\x12T\n" +
	"\tthumbnail\x18\x03 \x01(\v26.WAMediaTransport.WAMediaTransport.Ancillary.ThumbnailR\tthumbnail\x12\x1a\n" +
	"\bobjectID\x18\x04 \x01(\tR\bobjectID\x1a\xd8\x04\n" +
	"\tThumbnail\x12$\n" +
	"\rJPEGThumbnail\x18\x01 \x01(\fR\rJPEGThumbnail\x12\x82\x01\n" +
	"\x15downloadableThumbnail\x18\x02 \x01(\v2L.WAMediaTransport.WAMediaTransport.Ancillary.Thumbnail.DownloadableThumbnailR\x15downloadableThumbnail\x12&\n" +
	"\x0ethumbnailWidth\x18\x03 \x01(\rR\x0ethumbnailWidth\x12(\n" +
	"\x0fthumbnailHeight\x18\x04 \x01(\rR\x0fthumbnailHeight\x1a\xcd\x02\n" +
	"\x15DownloadableThumbnail\x12\x1e\n" +
	"\n" +
	"fileSHA256\x18\x01 \x01(\fR\n" +
	"fileSHA256\x12$\n" +
	"\rfileEncSHA256\x18\x02 \x01(\fR\rfileEncSHA256\x12\x1e\n" +
	"\n" +
	"directPath\x18\x03 \x01(\tR\n" +
	"directPath\x12\x1a\n" +
	"\bmediaKey\x18\x04 \x01(\fR\bmediaKey\x12,\n" +
	"\x11mediaKeyTimestamp\x18\x05 \x01(\x03R\x11mediaKeyTimestamp\x12\x1a\n" +
	"\bobjectID\x18\x06 \x01(\tR\bobjectID\x124\n" +
	"\x15thumbnailScansSidecar\x18\a \x01(\fR\x15thumbnailScansSidecar\x122\n" +
	"\x14thumbnailScanLengths\x18\b \x03(\rR\x14thumbnailScanLengths\x1a\xba\x01\n" +
	"\bIntegral\x12\x1e\n" +
	"\n" +
	"fileSHA256\x18\x01 \x01(\fR\n" +
	"fileSHA256\x12\x1a\n" +
	"\bmediaKey\x18\x02 \x01(\fR\bmediaKey\x12$\n" +
	"\rfileEncSHA256\x18\x03 \x01(\fR\rfileEncSHA256\x12\x1e\n" +
	"\n" +
	"directPath\x18\x04 \x01(\tR\n" +
	"directPath\x12,\n" +
	"\x11mediaKeyTimestamp\x18\x05 \x01(\x03R\x11mediaKeyTimestamp\"\x88\x05\n" +
	"\x0eImageTransport\x12E\n" +
	"\bintegral\x18\x01 \x01(\v2).WAMediaTransport.ImageTransport.IntegralR\bintegral\x12H\n" +
	"\tancillary\x18\x02 \x01(\v2*.WAMediaTransport.ImageTransport.AncillaryR\tancillary\x1a\x96\x03\n" +
	"\tAncillary\x12\x16\n" +
	"\x06height\x18\x01 \x01(\rR\x06height\x12\x14\n" +
	"\x05width\x18\x02 \x01(\rR\x05width\x12\"\n" +
	"\fscansSidecar\x18\x03 \x01(\fR\fscansSidecar\x12 \n" +
	"\vscanLengths\x18\x04 \x03(\rR\vscanLengths\x122\n" +
	"\x14midQualityFileSHA256\x18\x05 \x01(\fR\x14midQualityFileSHA256\x12I\n" +
	"\x06hdType\x18\x06 \x01(\x0e21.WAMediaTransport.ImageTransport.Ancillary.HdTypeR\x06hdType\x128\n" +
	"\x15memoriesConceptScores\x18\a \x03(\x02B\x02\x10\x01R\x15memoriesConceptScores\x122\n" +
	"\x12memoriesConceptIDs\x18\b \x03(\rB\x02\x10\x01R\x12memoriesConceptIDs\"(\n" +
	"\x06HdType\x12\b\n" +
	"\x04NONE\x10\x00\x12\t\n" +
	"\x05LQ_4K\x10\x01\x12\t\n" +
	"\x05HQ_4K\x10\x02\x1aL\n" +
	"\bIntegral\x12@\n" +
	"\ttransport\x18\x01 \x01(\v2\".WAMediaTransport.WAMediaTransportR\ttransport\"\x85\x05\n" +
	"\x0eVideoTransport\x12E\n" +
	"\bintegral\x18\x01 \x01(\v2).WAMediaTransport.VideoTransport.IntegralR\bintegral\x12H\n" +
	"\tancillary\x18\x02 \x01(\v2*.WAMediaTransport.VideoTransport.AncillaryR\tancillary\x1a\x93\x03\n" +
	"\tAncillary\x12\x18\n" +
	"\aseconds\x18\x01 \x01(\rR\aseconds\x12/\n" +
	"\acaption\x18\x02 \x01(\v2\x15.WACommon.MessageTextR\acaption\x12 \n" +
	"\vgifPlayback\x18\x03 \x01(\bR\vgifPlayback\x12\x16\n" +
	"\x06height\x18\x04 \x01(\rR\x06height\x12\x14\n" +
	"\x05width\x18\x05 \x01(\rR\x05width\x12\x18\n" +
	"\asidecar\x18\x06 \x01(\fR\asidecar\x12^\n" +
	"\x0egifAttribution\x18\a \x01(\x0e26.WAMediaTransport.VideoTransport.Ancillary.AttributionR\x0egifAttribution\x12.\n" +
	"\x12accessibilityLabel\x18\b \x01(\tR\x12accessibilityLabel\x12\x12\n" +
	"\x04isHd\x18\t \x01(\bR\x04isHd\"-\n" +
	"\vAttribution\x12\b\n" +
	"\x04NONE\x10\x00\x12\t\n" +
	"\x05GIPHY\x10\x01\x12\t\n" +
	"\x05TENOR\x10\x02\x1aL\n" +
	"\bIntegral\x12@\n" +
	"\ttransport\x18\x01 \x01(\v2\".WAMediaTransport.WAMediaTransportR\ttransport\"\xce\b\n" +
	"\x0eAudioTransport\x12E\n" +
	"\bintegral\x18\x01 \x01(\v2).WAMediaTransport.AudioTransport.IntegralR\bintegral\x12H\n" +
	"\tancillary\x18\x02 \x01(\v2*.WAMediaTransport.AudioTransport.AncillaryR\tancillary\x1a\xdc\x05\n" +
	"\tAncillary\x12\x18\n" +
	"\aseconds\x18\x01 \x01(\rR\aseconds\x12X\n" +
	"\vavatarAudio\x18\x02 \x01(\v26.WAMediaTransport.AudioTransport.Ancillary.AvatarAudioR\vavatarAudio\x1a\xda\x04\n" +
	"\vAvatarAudio\x12\x16\n" +
	"\x06poseID\x18\x01 \x01(\rR\x06poseID\x12\x7f\n" +
	"\x10avatarAnimations\x18\x02 \x03(\v2S.WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.DownloadableAvatarAnimationsR\x10avatarAnimations\x1a\xd9\x02\n" +
	"\x1cDownloadableAvatarAnimations\x12\x1e\n" +
	"\n" +
	"fileSHA256\x18\x01 \x01(\fR\n" +
	"fileSHA256\x12$\n" +
	"\rfileEncSHA256\x18\x02 \x01(\fR\rfileEncSHA256\x12\x1e\n" +
	"\n" +
	"directPath\x18\x03 \x01(\tR\n" +
	"directPath\x12\x1a\n" +
	"\bmediaKey\x18\x04 \x01(\fR\bmediaKey\x12,\n" +
	"\x11mediaKeyTimestamp\x18\x05 \x01(\x03R\x11mediaKeyTimestamp\x12\x1a\n" +
	"\bobjectID\x18\x06 \x01(\tR\bobjectID\x12m\n" +
	"\x0eanimationsType\x18\a \x01(\x0e2E.WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.AnimationsTypeR\x0eanimationsType\"V\n" +
	"\x0eAnimationsType\x12\r\n" +
	"\tTALKING_A\x10\x00\x12\n" +
	"\n" +
	"\x06IDLE_A\x10\x01\x12\r\n" +
	"\tTALKING_B\x10\x02\x12\n" +
	"\n" +
	"\x06IDLE_B\x10\x03\x12\x0e\n" +
	"\n" +
	"BACKGROUND\x10\x04\x1a\xcb\x01\n" +
	"\bIntegral\x12@\n" +
	"\ttransport\x18\x01 \x01(\v2\".WAMediaTransport.WAMediaTransportR\ttransport\x12W\n" +
	"\vaudioFormat\x18\x02 \x01(\x0e25.WAMediaTransport.AudioTransport.Integral.AudioFormatR\vaudioFormat\"$\n" +
	"\vAudioFormat\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\b\n" +
	"\x04OPUS\x10\x01\"\xa3\x02\n" +
	"\x11DocumentTransport\x12H\n" +
	"\bintegral\x18\x01 \x01(\v2,.WAMediaTransport.DocumentTransport.IntegralR\bintegral\x12K\n" +
	"\tancillary\x18\x02 \x01(\v2-.WAMediaTransport.DocumentTransport.AncillaryR\tancillary\x1a)\n" +
	"\tAncillary\x12\x1c\n" +
	"\tpageCount\x18\x01 \x01(\rR\tpageCount\x1aL\n" +
	"\bIntegral\x12@\n" +
	"\ttransport\x18\x01 \x01(\v2\".WAMediaTransport.WAMediaTransportR\ttransport\"\x96\x05\n" +
	"\x10StickerTransport\x12G\n" +
	"\bintegral\x18\x01 \x01(\v2+.WAMediaTransport.StickerTransport.IntegralR\bintegral\x12J\n" +
	"\tancillary\x18\x02 \x01(\v2,.WAMediaTransport.StickerTransport.AncillaryR\tancillary\x1a\xd3\x02\n" +
	"\tAncillary\x12\x1c\n" +
	"\tpageCount\x18\x01 \x01(\rR\tpageCount\x12\x16\n" +
	"\x06height\x18\x02 \x01(\rR\x06height\x12\x14\n" +
	"\x05width\x18\x03 \x01(\rR\x05width\x12*\n" +
	"\x10firstFrameLength\x18\x04 \x01(\rR\x10firstFrameLength\x12,\n" +
	"\x11firstFrameSidecar\x18\x05 \x01(\fR\x11firstFrameSidecar\x12\"\n" +
	"\fmustacheText\x18\x06 \x01(\tR\fmustacheText\x12\"\n" +
	"\fisThirdParty\x18\a \x01(\bR\fisThirdParty\x12(\n" +
	"\x0freceiverFetchID\x18\b \x01(\tR\x0freceiverFetchID\x12.\n" +
	"\x12accessibilityLabel\x18\t \x01(\tR\x12accessibilityLabel\x1a\x96\x01\n" +
	"\bIntegral\x12@\n" +
	"\ttransport\x18\x01 \x01(\v2\".WAMediaTransport.WAMediaTransportR\ttransport\x12\x1e\n" +
	"\n" +
	"isAnimated\x18\x02 \x01(\bR\n" +
	"isAnimated\x12(\n" +
	"\x0freceiverFetchID\x18\x03 \x01(\tR\x0freceiverFetchID\"\xda\x02\n" +
	"\x10ContactTransport\x12G\n" +
	"\bintegral\x18\x01 \x01(\v2+.WAMediaTransport.ContactTransport.IntegralR\bintegral\x12J\n" +
	"\tancillary\x18\x02 \x01(\v2,.WAMediaTransport.ContactTransport.AncillaryR\tancillary\x1a-\n" +
	"\tAncillary\x12 \n" +
	"\vdisplayName\x18\x01 \x01(\tR\vdisplayName\x1a\x81\x01\n" +
	"\bIntegral\x12\x16\n" +
	"\x05vcard\x18\x01 \x01(\tH\x00R\x05vcard\x12R\n" +
	"\x11downloadableVcard\x18\x02 \x01(\v2\".WAMediaTransport.WAMediaTransportH\x00R\x11downloadableVcardB\t\n" +
	"\acontactB,Z*go.mau.fi/whatsmeow/proto/waMediaTransport"

var (
	file_waMediaTransport_WAMediaTransport_proto_rawDescOnce sync.Once
	file_waMediaTransport_WAMediaTransport_proto_rawDescData []byte
)

func file_waMediaTransport_WAMediaTransport_proto_rawDescGZIP() []byte {
	file_waMediaTransport_WAMediaTransport_proto_rawDescOnce.Do(func() {
		file_waMediaTransport_WAMediaTransport_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waMediaTransport_WAMediaTransport_proto_rawDesc), len(file_waMediaTransport_WAMediaTransport_proto_rawDesc)))
	})
	return file_waMediaTransport_WAMediaTransport_proto_rawDescData
}

var file_waMediaTransport_WAMediaTransport_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_waMediaTransport_WAMediaTransport_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_waMediaTransport_WAMediaTransport_proto_goTypes = []any{
	(ImageTransport_Ancillary_HdType)(0),                                      // 0: WAMediaTransport.ImageTransport.Ancillary.HdType
	(VideoTransport_Ancillary_Attribution)(0),                                 // 1: WAMediaTransport.VideoTransport.Ancillary.Attribution
	(AudioTransport_Ancillary_AvatarAudio_AnimationsType)(0),                  // 2: WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.AnimationsType
	(AudioTransport_Integral_AudioFormat)(0),                                  // 3: WAMediaTransport.AudioTransport.Integral.AudioFormat
	(*WAMediaTransport)(nil),                                                  // 4: WAMediaTransport.WAMediaTransport
	(*ImageTransport)(nil),                                                    // 5: WAMediaTransport.ImageTransport
	(*VideoTransport)(nil),                                                    // 6: WAMediaTransport.VideoTransport
	(*AudioTransport)(nil),                                                    // 7: WAMediaTransport.AudioTransport
	(*DocumentTransport)(nil),                                                 // 8: WAMediaTransport.DocumentTransport
	(*StickerTransport)(nil),                                                  // 9: WAMediaTransport.StickerTransport
	(*ContactTransport)(nil),                                                  // 10: WAMediaTransport.ContactTransport
	(*WAMediaTransport_Ancillary)(nil),                                        // 11: WAMediaTransport.WAMediaTransport.Ancillary
	(*WAMediaTransport_Integral)(nil),                                         // 12: WAMediaTransport.WAMediaTransport.Integral
	(*WAMediaTransport_Ancillary_Thumbnail)(nil),                              // 13: WAMediaTransport.WAMediaTransport.Ancillary.Thumbnail
	(*WAMediaTransport_Ancillary_Thumbnail_DownloadableThumbnail)(nil),        // 14: WAMediaTransport.WAMediaTransport.Ancillary.Thumbnail.DownloadableThumbnail
	(*ImageTransport_Ancillary)(nil),                                          // 15: WAMediaTransport.ImageTransport.Ancillary
	(*ImageTransport_Integral)(nil),                                           // 16: WAMediaTransport.ImageTransport.Integral
	(*VideoTransport_Ancillary)(nil),                                          // 17: WAMediaTransport.VideoTransport.Ancillary
	(*VideoTransport_Integral)(nil),                                           // 18: WAMediaTransport.VideoTransport.Integral
	(*AudioTransport_Ancillary)(nil),                                          // 19: WAMediaTransport.AudioTransport.Ancillary
	(*AudioTransport_Integral)(nil),                                           // 20: WAMediaTransport.AudioTransport.Integral
	(*AudioTransport_Ancillary_AvatarAudio)(nil),                              // 21: WAMediaTransport.AudioTransport.Ancillary.AvatarAudio
	(*AudioTransport_Ancillary_AvatarAudio_DownloadableAvatarAnimations)(nil), // 22: WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.DownloadableAvatarAnimations
	(*DocumentTransport_Ancillary)(nil),                                       // 23: WAMediaTransport.DocumentTransport.Ancillary
	(*DocumentTransport_Integral)(nil),                                        // 24: WAMediaTransport.DocumentTransport.Integral
	(*StickerTransport_Ancillary)(nil),                                        // 25: WAMediaTransport.StickerTransport.Ancillary
	(*StickerTransport_Integral)(nil),                                         // 26: WAMediaTransport.StickerTransport.Integral
	(*ContactTransport_Ancillary)(nil),                                        // 27: WAMediaTransport.ContactTransport.Ancillary
	(*ContactTransport_Integral)(nil),                                         // 28: WAMediaTransport.ContactTransport.Integral
	(*waCommon.MessageText)(nil),                                              // 29: WACommon.MessageText
}
var file_waMediaTransport_WAMediaTransport_proto_depIdxs = []int32{
	12, // 0: WAMediaTransport.WAMediaTransport.integral:type_name -> WAMediaTransport.WAMediaTransport.Integral
	11, // 1: WAMediaTransport.WAMediaTransport.ancillary:type_name -> WAMediaTransport.WAMediaTransport.Ancillary
	16, // 2: WAMediaTransport.ImageTransport.integral:type_name -> WAMediaTransport.ImageTransport.Integral
	15, // 3: WAMediaTransport.ImageTransport.ancillary:type_name -> WAMediaTransport.ImageTransport.Ancillary
	18, // 4: WAMediaTransport.VideoTransport.integral:type_name -> WAMediaTransport.VideoTransport.Integral
	17, // 5: WAMediaTransport.VideoTransport.ancillary:type_name -> WAMediaTransport.VideoTransport.Ancillary
	20, // 6: WAMediaTransport.AudioTransport.integral:type_name -> WAMediaTransport.AudioTransport.Integral
	19, // 7: WAMediaTransport.AudioTransport.ancillary:type_name -> WAMediaTransport.AudioTransport.Ancillary
	24, // 8: WAMediaTransport.DocumentTransport.integral:type_name -> WAMediaTransport.DocumentTransport.Integral
	23, // 9: WAMediaTransport.DocumentTransport.ancillary:type_name -> WAMediaTransport.DocumentTransport.Ancillary
	26, // 10: WAMediaTransport.StickerTransport.integral:type_name -> WAMediaTransport.StickerTransport.Integral
	25, // 11: WAMediaTransport.StickerTransport.ancillary:type_name -> WAMediaTransport.StickerTransport.Ancillary
	28, // 12: WAMediaTransport.ContactTransport.integral:type_name -> WAMediaTransport.ContactTransport.Integral
	27, // 13: WAMediaTransport.ContactTransport.ancillary:type_name -> WAMediaTransport.ContactTransport.Ancillary
	13, // 14: WAMediaTransport.WAMediaTransport.Ancillary.thumbnail:type_name -> WAMediaTransport.WAMediaTransport.Ancillary.Thumbnail
	14, // 15: WAMediaTransport.WAMediaTransport.Ancillary.Thumbnail.downloadableThumbnail:type_name -> WAMediaTransport.WAMediaTransport.Ancillary.Thumbnail.DownloadableThumbnail
	0,  // 16: WAMediaTransport.ImageTransport.Ancillary.hdType:type_name -> WAMediaTransport.ImageTransport.Ancillary.HdType
	4,  // 17: WAMediaTransport.ImageTransport.Integral.transport:type_name -> WAMediaTransport.WAMediaTransport
	29, // 18: WAMediaTransport.VideoTransport.Ancillary.caption:type_name -> WACommon.MessageText
	1,  // 19: WAMediaTransport.VideoTransport.Ancillary.gifAttribution:type_name -> WAMediaTransport.VideoTransport.Ancillary.Attribution
	4,  // 20: WAMediaTransport.VideoTransport.Integral.transport:type_name -> WAMediaTransport.WAMediaTransport
	21, // 21: WAMediaTransport.AudioTransport.Ancillary.avatarAudio:type_name -> WAMediaTransport.AudioTransport.Ancillary.AvatarAudio
	4,  // 22: WAMediaTransport.AudioTransport.Integral.transport:type_name -> WAMediaTransport.WAMediaTransport
	3,  // 23: WAMediaTransport.AudioTransport.Integral.audioFormat:type_name -> WAMediaTransport.AudioTransport.Integral.AudioFormat
	22, // 24: WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.avatarAnimations:type_name -> WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.DownloadableAvatarAnimations
	2,  // 25: WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.DownloadableAvatarAnimations.animationsType:type_name -> WAMediaTransport.AudioTransport.Ancillary.AvatarAudio.AnimationsType
	4,  // 26: WAMediaTransport.DocumentTransport.Integral.transport:type_name -> WAMediaTransport.WAMediaTransport
	4,  // 27: WAMediaTransport.StickerTransport.Integral.transport:type_name -> WAMediaTransport.WAMediaTransport
	4,  // 28: WAMediaTransport.ContactTransport.Integral.downloadableVcard:type_name -> WAMediaTransport.WAMediaTransport
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_waMediaTransport_WAMediaTransport_proto_init() }
func file_waMediaTransport_WAMediaTransport_proto_init() {
	if File_waMediaTransport_WAMediaTransport_proto != nil {
		return
	}
	file_waMediaTransport_WAMediaTransport_proto_msgTypes[24].OneofWrappers = []any{
		(*ContactTransport_Integral_Vcard)(nil),
		(*ContactTransport_Integral_DownloadableVcard)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waMediaTransport_WAMediaTransport_proto_rawDesc), len(file_waMediaTransport_WAMediaTransport_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waMediaTransport_WAMediaTransport_proto_goTypes,
		DependencyIndexes: file_waMediaTransport_WAMediaTransport_proto_depIdxs,
		EnumInfos:         file_waMediaTransport_WAMediaTransport_proto_enumTypes,
		MessageInfos:      file_waMediaTransport_WAMediaTransport_proto_msgTypes,
	}.Build()
	File_waMediaTransport_WAMediaTransport_proto = out.File
	file_waMediaTransport_WAMediaTransport_proto_goTypes = nil
	file_waMediaTransport_WAMediaTransport_proto_depIdxs = nil
}
