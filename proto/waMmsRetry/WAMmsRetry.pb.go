// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waMmsRetry/WAMmsRetry.proto

package waMmsRetry

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MediaRetryNotification_ResultType int32

const (
	MediaRetryNotification_GENERAL_ERROR    MediaRetryNotification_ResultType = 0
	MediaRetryNotification_SUCCESS          MediaRetryNotification_ResultType = 1
	MediaRetryNotification_NOT_FOUND        MediaRetryNotification_ResultType = 2
	MediaRetryNotification_DECRYPTION_ERROR MediaRetryNotification_ResultType = 3
)

// Enum value maps for MediaRetryNotification_ResultType.
var (
	MediaRetryNotification_ResultType_name = map[int32]string{
		0: "GENERAL_ERROR",
		1: "SUCCESS",
		2: "NOT_FOUND",
		3: "DECRYPTION_ERROR",
	}
	MediaRetryNotification_ResultType_value = map[string]int32{
		"GENERAL_ERROR":    0,
		"SUCCESS":          1,
		"NOT_FOUND":        2,
		"DECRYPTION_ERROR": 3,
	}
)

func (x MediaRetryNotification_ResultType) Enum() *MediaRetryNotification_ResultType {
	p := new(MediaRetryNotification_ResultType)
	*p = x
	return p
}

func (x MediaRetryNotification_ResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MediaRetryNotification_ResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_waMmsRetry_WAMmsRetry_proto_enumTypes[0].Descriptor()
}

func (MediaRetryNotification_ResultType) Type() protoreflect.EnumType {
	return &file_waMmsRetry_WAMmsRetry_proto_enumTypes[0]
}

func (x MediaRetryNotification_ResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MediaRetryNotification_ResultType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MediaRetryNotification_ResultType(num)
	return nil
}

// Deprecated: Use MediaRetryNotification_ResultType.Descriptor instead.
func (MediaRetryNotification_ResultType) EnumDescriptor() ([]byte, []int) {
	return file_waMmsRetry_WAMmsRetry_proto_rawDescGZIP(), []int{0, 0}
}

type MediaRetryNotification struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	StanzaID      *string                            `protobuf:"bytes,1,opt,name=stanzaID" json:"stanzaID,omitempty"`
	DirectPath    *string                            `protobuf:"bytes,2,opt,name=directPath" json:"directPath,omitempty"`
	Result        *MediaRetryNotification_ResultType `protobuf:"varint,3,opt,name=result,enum=WAMmsRetry.MediaRetryNotification_ResultType" json:"result,omitempty"`
	MessageSecret []byte                             `protobuf:"bytes,4,opt,name=messageSecret" json:"messageSecret,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaRetryNotification) Reset() {
	*x = MediaRetryNotification{}
	mi := &file_waMmsRetry_WAMmsRetry_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaRetryNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaRetryNotification) ProtoMessage() {}

func (x *MediaRetryNotification) ProtoReflect() protoreflect.Message {
	mi := &file_waMmsRetry_WAMmsRetry_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaRetryNotification.ProtoReflect.Descriptor instead.
func (*MediaRetryNotification) Descriptor() ([]byte, []int) {
	return file_waMmsRetry_WAMmsRetry_proto_rawDescGZIP(), []int{0}
}

func (x *MediaRetryNotification) GetStanzaID() string {
	if x != nil && x.StanzaID != nil {
		return *x.StanzaID
	}
	return ""
}

func (x *MediaRetryNotification) GetDirectPath() string {
	if x != nil && x.DirectPath != nil {
		return *x.DirectPath
	}
	return ""
}

func (x *MediaRetryNotification) GetResult() MediaRetryNotification_ResultType {
	if x != nil && x.Result != nil {
		return *x.Result
	}
	return MediaRetryNotification_GENERAL_ERROR
}

func (x *MediaRetryNotification) GetMessageSecret() []byte {
	if x != nil {
		return x.MessageSecret
	}
	return nil
}

type ServerErrorReceipt struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StanzaID      *string                `protobuf:"bytes,1,opt,name=stanzaID" json:"stanzaID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerErrorReceipt) Reset() {
	*x = ServerErrorReceipt{}
	mi := &file_waMmsRetry_WAMmsRetry_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerErrorReceipt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerErrorReceipt) ProtoMessage() {}

func (x *ServerErrorReceipt) ProtoReflect() protoreflect.Message {
	mi := &file_waMmsRetry_WAMmsRetry_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerErrorReceipt.ProtoReflect.Descriptor instead.
func (*ServerErrorReceipt) Descriptor() ([]byte, []int) {
	return file_waMmsRetry_WAMmsRetry_proto_rawDescGZIP(), []int{1}
}

func (x *ServerErrorReceipt) GetStanzaID() string {
	if x != nil && x.StanzaID != nil {
		return *x.StanzaID
	}
	return ""
}

var File_waMmsRetry_WAMmsRetry_proto protoreflect.FileDescriptor

const file_waMmsRetry_WAMmsRetry_proto_rawDesc = "" +
	"\n" +
	"\x1bwaMmsRetry/WAMmsRetry.proto\x12\n" +
	"WAMmsRetry\"\x94\x02\n" +
	"\x16MediaRetryNotification\x12\x1a\n" +
	"\bstanzaID\x18\x01 \x01(\tR\bstanzaID\x12\x1e\n" +
	"\n" +
	"directPath\x18\x02 \x01(\tR\n" +
	"directPath\x12E\n" +
	"\x06result\x18\x03 \x01(\x0e2-.WAMmsRetry.MediaRetryNotification.ResultTypeR\x06result\x12$\n" +
	"\rmessageSecret\x18\x04 \x01(\fR\rmessageSecret\"Q\n" +
	"\n" +
	"ResultType\x12\x11\n" +
	"\rGENERAL_ERROR\x10\x00\x12\v\n" +
	"\aSUCCESS\x10\x01\x12\r\n" +
	"\tNOT_FOUND\x10\x02\x12\x14\n" +
	"\x10DECRYPTION_ERROR\x10\x03\"0\n" +
	"\x12ServerErrorReceipt\x12\x1a\n" +
	"\bstanzaID\x18\x01 \x01(\tR\bstanzaIDB&Z$go.mau.fi/whatsmeow/proto/waMmsRetry"

var (
	file_waMmsRetry_WAMmsRetry_proto_rawDescOnce sync.Once
	file_waMmsRetry_WAMmsRetry_proto_rawDescData []byte
)

func file_waMmsRetry_WAMmsRetry_proto_rawDescGZIP() []byte {
	file_waMmsRetry_WAMmsRetry_proto_rawDescOnce.Do(func() {
		file_waMmsRetry_WAMmsRetry_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waMmsRetry_WAMmsRetry_proto_rawDesc), len(file_waMmsRetry_WAMmsRetry_proto_rawDesc)))
	})
	return file_waMmsRetry_WAMmsRetry_proto_rawDescData
}

var file_waMmsRetry_WAMmsRetry_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_waMmsRetry_WAMmsRetry_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_waMmsRetry_WAMmsRetry_proto_goTypes = []any{
	(MediaRetryNotification_ResultType)(0), // 0: WAMmsRetry.MediaRetryNotification.ResultType
	(*MediaRetryNotification)(nil),         // 1: WAMmsRetry.MediaRetryNotification
	(*ServerErrorReceipt)(nil),             // 2: WAMmsRetry.ServerErrorReceipt
}
var file_waMmsRetry_WAMmsRetry_proto_depIdxs = []int32{
	0, // 0: WAMmsRetry.MediaRetryNotification.result:type_name -> WAMmsRetry.MediaRetryNotification.ResultType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_waMmsRetry_WAMmsRetry_proto_init() }
func file_waMmsRetry_WAMmsRetry_proto_init() {
	if File_waMmsRetry_WAMmsRetry_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waMmsRetry_WAMmsRetry_proto_rawDesc), len(file_waMmsRetry_WAMmsRetry_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waMmsRetry_WAMmsRetry_proto_goTypes,
		DependencyIndexes: file_waMmsRetry_WAMmsRetry_proto_depIdxs,
		EnumInfos:         file_waMmsRetry_WAMmsRetry_proto_enumTypes,
		MessageInfos:      file_waMmsRetry_WAMmsRetry_proto_msgTypes,
	}.Build()
	File_waMmsRetry_WAMmsRetry_proto = out.File
	file_waMmsRetry_WAMmsRetry_proto_goTypes = nil
	file_waMmsRetry_WAMmsRetry_proto_depIdxs = nil
}
