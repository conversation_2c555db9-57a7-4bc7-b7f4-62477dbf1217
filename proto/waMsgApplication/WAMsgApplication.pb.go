// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waMsgApplication/WAMsgApplication.proto

package waMsgApplication

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waCommon "go.mau.fi/whatsmeow/proto/waCommon"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MessageApplication_Metadata_ThreadType int32

const (
	MessageApplication_Metadata_DEFAULT               MessageApplication_Metadata_ThreadType = 0
	MessageApplication_Metadata_VANISH_MODE           MessageApplication_Metadata_ThreadType = 1
	MessageApplication_Metadata_DISAPPEARING_MESSAGES MessageApplication_Metadata_ThreadType = 2
)

// Enum value maps for MessageApplication_Metadata_ThreadType.
var (
	MessageApplication_Metadata_ThreadType_name = map[int32]string{
		0: "DEFAULT",
		1: "VANISH_MODE",
		2: "DISAPPEARING_MESSAGES",
	}
	MessageApplication_Metadata_ThreadType_value = map[string]int32{
		"DEFAULT":               0,
		"VANISH_MODE":           1,
		"DISAPPEARING_MESSAGES": 2,
	}
)

func (x MessageApplication_Metadata_ThreadType) Enum() *MessageApplication_Metadata_ThreadType {
	p := new(MessageApplication_Metadata_ThreadType)
	*p = x
	return p
}

func (x MessageApplication_Metadata_ThreadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageApplication_Metadata_ThreadType) Descriptor() protoreflect.EnumDescriptor {
	return file_waMsgApplication_WAMsgApplication_proto_enumTypes[0].Descriptor()
}

func (MessageApplication_Metadata_ThreadType) Type() protoreflect.EnumType {
	return &file_waMsgApplication_WAMsgApplication_proto_enumTypes[0]
}

func (x MessageApplication_Metadata_ThreadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MessageApplication_Metadata_ThreadType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MessageApplication_Metadata_ThreadType(num)
	return nil
}

// Deprecated: Use MessageApplication_Metadata_ThreadType.Descriptor instead.
func (MessageApplication_Metadata_ThreadType) EnumDescriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 0, 0}
}

type MessageApplication_EphemeralSetting_EphemeralityType int32

const (
	MessageApplication_EphemeralSetting_UNKNOWN               MessageApplication_EphemeralSetting_EphemeralityType = 0
	MessageApplication_EphemeralSetting_SEEN_ONCE             MessageApplication_EphemeralSetting_EphemeralityType = 1
	MessageApplication_EphemeralSetting_SEEN_BASED_WITH_TIMER MessageApplication_EphemeralSetting_EphemeralityType = 2
	MessageApplication_EphemeralSetting_SEND_BASED_WITH_TIMER MessageApplication_EphemeralSetting_EphemeralityType = 3
)

// Enum value maps for MessageApplication_EphemeralSetting_EphemeralityType.
var (
	MessageApplication_EphemeralSetting_EphemeralityType_name = map[int32]string{
		0: "UNKNOWN",
		1: "SEEN_ONCE",
		2: "SEEN_BASED_WITH_TIMER",
		3: "SEND_BASED_WITH_TIMER",
	}
	MessageApplication_EphemeralSetting_EphemeralityType_value = map[string]int32{
		"UNKNOWN":               0,
		"SEEN_ONCE":             1,
		"SEEN_BASED_WITH_TIMER": 2,
		"SEND_BASED_WITH_TIMER": 3,
	}
)

func (x MessageApplication_EphemeralSetting_EphemeralityType) Enum() *MessageApplication_EphemeralSetting_EphemeralityType {
	p := new(MessageApplication_EphemeralSetting_EphemeralityType)
	*p = x
	return p
}

func (x MessageApplication_EphemeralSetting_EphemeralityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageApplication_EphemeralSetting_EphemeralityType) Descriptor() protoreflect.EnumDescriptor {
	return file_waMsgApplication_WAMsgApplication_proto_enumTypes[1].Descriptor()
}

func (MessageApplication_EphemeralSetting_EphemeralityType) Type() protoreflect.EnumType {
	return &file_waMsgApplication_WAMsgApplication_proto_enumTypes[1]
}

func (x MessageApplication_EphemeralSetting_EphemeralityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MessageApplication_EphemeralSetting_EphemeralityType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MessageApplication_EphemeralSetting_EphemeralityType(num)
	return nil
}

// Deprecated: Use MessageApplication_EphemeralSetting_EphemeralityType.Descriptor instead.
func (MessageApplication_EphemeralSetting_EphemeralityType) EnumDescriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 6, 0}
}

type MessageApplication struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Payload       *MessageApplication_Payload  `protobuf:"bytes,1,opt,name=payload" json:"payload,omitempty"`
	Metadata      *MessageApplication_Metadata `protobuf:"bytes,2,opt,name=metadata" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageApplication) Reset() {
	*x = MessageApplication{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication) ProtoMessage() {}

func (x *MessageApplication) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication.ProtoReflect.Descriptor instead.
func (*MessageApplication) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0}
}

func (x *MessageApplication) GetPayload() *MessageApplication_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *MessageApplication) GetMetadata() *MessageApplication_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type MessageApplication_Metadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Ephemeral:
	//
	//	*MessageApplication_Metadata_ChatEphemeralSetting
	//	*MessageApplication_Metadata_EphemeralSettingList
	//	*MessageApplication_Metadata_EphemeralSharedSecret
	Ephemeral                 isMessageApplication_Metadata_Ephemeral    `protobuf_oneof:"ephemeral"`
	ForwardingScore           *uint32                                    `protobuf:"varint,5,opt,name=forwardingScore" json:"forwardingScore,omitempty"`
	IsForwarded               *bool                                      `protobuf:"varint,6,opt,name=isForwarded" json:"isForwarded,omitempty"`
	BusinessMetadata          *waCommon.SubProtocol                      `protobuf:"bytes,7,opt,name=businessMetadata" json:"businessMetadata,omitempty"`
	FrankingKey               []byte                                     `protobuf:"bytes,8,opt,name=frankingKey" json:"frankingKey,omitempty"`
	FrankingVersion           *int32                                     `protobuf:"varint,9,opt,name=frankingVersion" json:"frankingVersion,omitempty"`
	QuotedMessage             *MessageApplication_Metadata_QuotedMessage `protobuf:"bytes,10,opt,name=quotedMessage" json:"quotedMessage,omitempty"`
	ThreadType                *MessageApplication_Metadata_ThreadType    `protobuf:"varint,11,opt,name=threadType,enum=WAMsgApplication.MessageApplication_Metadata_ThreadType" json:"threadType,omitempty"`
	ReadonlyMetadataDataclass *string                                    `protobuf:"bytes,12,opt,name=readonlyMetadataDataclass" json:"readonlyMetadataDataclass,omitempty"`
	GroupID                   *string                                    `protobuf:"bytes,13,opt,name=groupID" json:"groupID,omitempty"`
	GroupSize                 *uint32                                    `protobuf:"varint,14,opt,name=groupSize" json:"groupSize,omitempty"`
	GroupIndex                *uint32                                    `protobuf:"varint,15,opt,name=groupIndex" json:"groupIndex,omitempty"`
	BotResponseID             *string                                    `protobuf:"bytes,16,opt,name=botResponseID" json:"botResponseID,omitempty"`
	CollapsibleID             *string                                    `protobuf:"bytes,17,opt,name=collapsibleID" json:"collapsibleID,omitempty"`
	SecondaryOtid             *string                                    `protobuf:"bytes,18,opt,name=secondaryOtid" json:"secondaryOtid,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *MessageApplication_Metadata) Reset() {
	*x = MessageApplication_Metadata{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_Metadata) ProtoMessage() {}

func (x *MessageApplication_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_Metadata.ProtoReflect.Descriptor instead.
func (*MessageApplication_Metadata) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 0}
}

func (x *MessageApplication_Metadata) GetEphemeral() isMessageApplication_Metadata_Ephemeral {
	if x != nil {
		return x.Ephemeral
	}
	return nil
}

func (x *MessageApplication_Metadata) GetChatEphemeralSetting() *MessageApplication_EphemeralSetting {
	if x != nil {
		if x, ok := x.Ephemeral.(*MessageApplication_Metadata_ChatEphemeralSetting); ok {
			return x.ChatEphemeralSetting
		}
	}
	return nil
}

func (x *MessageApplication_Metadata) GetEphemeralSettingList() *MessageApplication_Metadata_EphemeralSettingMap {
	if x != nil {
		if x, ok := x.Ephemeral.(*MessageApplication_Metadata_EphemeralSettingList); ok {
			return x.EphemeralSettingList
		}
	}
	return nil
}

func (x *MessageApplication_Metadata) GetEphemeralSharedSecret() []byte {
	if x != nil {
		if x, ok := x.Ephemeral.(*MessageApplication_Metadata_EphemeralSharedSecret); ok {
			return x.EphemeralSharedSecret
		}
	}
	return nil
}

func (x *MessageApplication_Metadata) GetForwardingScore() uint32 {
	if x != nil && x.ForwardingScore != nil {
		return *x.ForwardingScore
	}
	return 0
}

func (x *MessageApplication_Metadata) GetIsForwarded() bool {
	if x != nil && x.IsForwarded != nil {
		return *x.IsForwarded
	}
	return false
}

func (x *MessageApplication_Metadata) GetBusinessMetadata() *waCommon.SubProtocol {
	if x != nil {
		return x.BusinessMetadata
	}
	return nil
}

func (x *MessageApplication_Metadata) GetFrankingKey() []byte {
	if x != nil {
		return x.FrankingKey
	}
	return nil
}

func (x *MessageApplication_Metadata) GetFrankingVersion() int32 {
	if x != nil && x.FrankingVersion != nil {
		return *x.FrankingVersion
	}
	return 0
}

func (x *MessageApplication_Metadata) GetQuotedMessage() *MessageApplication_Metadata_QuotedMessage {
	if x != nil {
		return x.QuotedMessage
	}
	return nil
}

func (x *MessageApplication_Metadata) GetThreadType() MessageApplication_Metadata_ThreadType {
	if x != nil && x.ThreadType != nil {
		return *x.ThreadType
	}
	return MessageApplication_Metadata_DEFAULT
}

func (x *MessageApplication_Metadata) GetReadonlyMetadataDataclass() string {
	if x != nil && x.ReadonlyMetadataDataclass != nil {
		return *x.ReadonlyMetadataDataclass
	}
	return ""
}

func (x *MessageApplication_Metadata) GetGroupID() string {
	if x != nil && x.GroupID != nil {
		return *x.GroupID
	}
	return ""
}

func (x *MessageApplication_Metadata) GetGroupSize() uint32 {
	if x != nil && x.GroupSize != nil {
		return *x.GroupSize
	}
	return 0
}

func (x *MessageApplication_Metadata) GetGroupIndex() uint32 {
	if x != nil && x.GroupIndex != nil {
		return *x.GroupIndex
	}
	return 0
}

func (x *MessageApplication_Metadata) GetBotResponseID() string {
	if x != nil && x.BotResponseID != nil {
		return *x.BotResponseID
	}
	return ""
}

func (x *MessageApplication_Metadata) GetCollapsibleID() string {
	if x != nil && x.CollapsibleID != nil {
		return *x.CollapsibleID
	}
	return ""
}

func (x *MessageApplication_Metadata) GetSecondaryOtid() string {
	if x != nil && x.SecondaryOtid != nil {
		return *x.SecondaryOtid
	}
	return ""
}

type isMessageApplication_Metadata_Ephemeral interface {
	isMessageApplication_Metadata_Ephemeral()
}

type MessageApplication_Metadata_ChatEphemeralSetting struct {
	ChatEphemeralSetting *MessageApplication_EphemeralSetting `protobuf:"bytes,1,opt,name=chatEphemeralSetting,oneof"`
}

type MessageApplication_Metadata_EphemeralSettingList struct {
	EphemeralSettingList *MessageApplication_Metadata_EphemeralSettingMap `protobuf:"bytes,2,opt,name=ephemeralSettingList,oneof"`
}

type MessageApplication_Metadata_EphemeralSharedSecret struct {
	EphemeralSharedSecret []byte `protobuf:"bytes,3,opt,name=ephemeralSharedSecret,oneof"`
}

func (*MessageApplication_Metadata_ChatEphemeralSetting) isMessageApplication_Metadata_Ephemeral() {}

func (*MessageApplication_Metadata_EphemeralSettingList) isMessageApplication_Metadata_Ephemeral() {}

func (*MessageApplication_Metadata_EphemeralSharedSecret) isMessageApplication_Metadata_Ephemeral() {}

type MessageApplication_Payload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Content:
	//
	//	*MessageApplication_Payload_CoreContent
	//	*MessageApplication_Payload_Signal
	//	*MessageApplication_Payload_ApplicationData
	//	*MessageApplication_Payload_SubProtocol
	Content       isMessageApplication_Payload_Content `protobuf_oneof:"content"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageApplication_Payload) Reset() {
	*x = MessageApplication_Payload{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_Payload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_Payload) ProtoMessage() {}

func (x *MessageApplication_Payload) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_Payload.ProtoReflect.Descriptor instead.
func (*MessageApplication_Payload) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 1}
}

func (x *MessageApplication_Payload) GetContent() isMessageApplication_Payload_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *MessageApplication_Payload) GetCoreContent() *MessageApplication_Content {
	if x != nil {
		if x, ok := x.Content.(*MessageApplication_Payload_CoreContent); ok {
			return x.CoreContent
		}
	}
	return nil
}

func (x *MessageApplication_Payload) GetSignal() *MessageApplication_Signal {
	if x != nil {
		if x, ok := x.Content.(*MessageApplication_Payload_Signal); ok {
			return x.Signal
		}
	}
	return nil
}

func (x *MessageApplication_Payload) GetApplicationData() *MessageApplication_ApplicationData {
	if x != nil {
		if x, ok := x.Content.(*MessageApplication_Payload_ApplicationData); ok {
			return x.ApplicationData
		}
	}
	return nil
}

func (x *MessageApplication_Payload) GetSubProtocol() *MessageApplication_SubProtocolPayload {
	if x != nil {
		if x, ok := x.Content.(*MessageApplication_Payload_SubProtocol); ok {
			return x.SubProtocol
		}
	}
	return nil
}

type isMessageApplication_Payload_Content interface {
	isMessageApplication_Payload_Content()
}

type MessageApplication_Payload_CoreContent struct {
	CoreContent *MessageApplication_Content `protobuf:"bytes,1,opt,name=coreContent,oneof"`
}

type MessageApplication_Payload_Signal struct {
	Signal *MessageApplication_Signal `protobuf:"bytes,2,opt,name=signal,oneof"`
}

type MessageApplication_Payload_ApplicationData struct {
	ApplicationData *MessageApplication_ApplicationData `protobuf:"bytes,3,opt,name=applicationData,oneof"`
}

type MessageApplication_Payload_SubProtocol struct {
	SubProtocol *MessageApplication_SubProtocolPayload `protobuf:"bytes,4,opt,name=subProtocol,oneof"`
}

func (*MessageApplication_Payload_CoreContent) isMessageApplication_Payload_Content() {}

func (*MessageApplication_Payload_Signal) isMessageApplication_Payload_Content() {}

func (*MessageApplication_Payload_ApplicationData) isMessageApplication_Payload_Content() {}

func (*MessageApplication_Payload_SubProtocol) isMessageApplication_Payload_Content() {}

type MessageApplication_SubProtocolPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to SubProtocol:
	//
	//	*MessageApplication_SubProtocolPayload_ConsumerMessage
	//	*MessageApplication_SubProtocolPayload_BusinessMessage
	//	*MessageApplication_SubProtocolPayload_PaymentMessage
	//	*MessageApplication_SubProtocolPayload_MultiDevice
	//	*MessageApplication_SubProtocolPayload_Voip
	//	*MessageApplication_SubProtocolPayload_Armadillo
	SubProtocol   isMessageApplication_SubProtocolPayload_SubProtocol `protobuf_oneof:"subProtocol"`
	FutureProof   *waCommon.FutureProofBehavior                       `protobuf:"varint,1,opt,name=futureProof,enum=WACommon.FutureProofBehavior" json:"futureProof,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageApplication_SubProtocolPayload) Reset() {
	*x = MessageApplication_SubProtocolPayload{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_SubProtocolPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_SubProtocolPayload) ProtoMessage() {}

func (x *MessageApplication_SubProtocolPayload) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_SubProtocolPayload.ProtoReflect.Descriptor instead.
func (*MessageApplication_SubProtocolPayload) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 2}
}

func (x *MessageApplication_SubProtocolPayload) GetSubProtocol() isMessageApplication_SubProtocolPayload_SubProtocol {
	if x != nil {
		return x.SubProtocol
	}
	return nil
}

func (x *MessageApplication_SubProtocolPayload) GetConsumerMessage() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.SubProtocol.(*MessageApplication_SubProtocolPayload_ConsumerMessage); ok {
			return x.ConsumerMessage
		}
	}
	return nil
}

func (x *MessageApplication_SubProtocolPayload) GetBusinessMessage() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.SubProtocol.(*MessageApplication_SubProtocolPayload_BusinessMessage); ok {
			return x.BusinessMessage
		}
	}
	return nil
}

func (x *MessageApplication_SubProtocolPayload) GetPaymentMessage() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.SubProtocol.(*MessageApplication_SubProtocolPayload_PaymentMessage); ok {
			return x.PaymentMessage
		}
	}
	return nil
}

func (x *MessageApplication_SubProtocolPayload) GetMultiDevice() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.SubProtocol.(*MessageApplication_SubProtocolPayload_MultiDevice); ok {
			return x.MultiDevice
		}
	}
	return nil
}

func (x *MessageApplication_SubProtocolPayload) GetVoip() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.SubProtocol.(*MessageApplication_SubProtocolPayload_Voip); ok {
			return x.Voip
		}
	}
	return nil
}

func (x *MessageApplication_SubProtocolPayload) GetArmadillo() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.SubProtocol.(*MessageApplication_SubProtocolPayload_Armadillo); ok {
			return x.Armadillo
		}
	}
	return nil
}

func (x *MessageApplication_SubProtocolPayload) GetFutureProof() waCommon.FutureProofBehavior {
	if x != nil && x.FutureProof != nil {
		return *x.FutureProof
	}
	return waCommon.FutureProofBehavior(0)
}

type isMessageApplication_SubProtocolPayload_SubProtocol interface {
	isMessageApplication_SubProtocolPayload_SubProtocol()
}

type MessageApplication_SubProtocolPayload_ConsumerMessage struct {
	ConsumerMessage *waCommon.SubProtocol `protobuf:"bytes,2,opt,name=consumerMessage,oneof"`
}

type MessageApplication_SubProtocolPayload_BusinessMessage struct {
	BusinessMessage *waCommon.SubProtocol `protobuf:"bytes,3,opt,name=businessMessage,oneof"`
}

type MessageApplication_SubProtocolPayload_PaymentMessage struct {
	PaymentMessage *waCommon.SubProtocol `protobuf:"bytes,4,opt,name=paymentMessage,oneof"`
}

type MessageApplication_SubProtocolPayload_MultiDevice struct {
	MultiDevice *waCommon.SubProtocol `protobuf:"bytes,5,opt,name=multiDevice,oneof"`
}

type MessageApplication_SubProtocolPayload_Voip struct {
	Voip *waCommon.SubProtocol `protobuf:"bytes,6,opt,name=voip,oneof"`
}

type MessageApplication_SubProtocolPayload_Armadillo struct {
	Armadillo *waCommon.SubProtocol `protobuf:"bytes,7,opt,name=armadillo,oneof"`
}

func (*MessageApplication_SubProtocolPayload_ConsumerMessage) isMessageApplication_SubProtocolPayload_SubProtocol() {
}

func (*MessageApplication_SubProtocolPayload_BusinessMessage) isMessageApplication_SubProtocolPayload_SubProtocol() {
}

func (*MessageApplication_SubProtocolPayload_PaymentMessage) isMessageApplication_SubProtocolPayload_SubProtocol() {
}

func (*MessageApplication_SubProtocolPayload_MultiDevice) isMessageApplication_SubProtocolPayload_SubProtocol() {
}

func (*MessageApplication_SubProtocolPayload_Voip) isMessageApplication_SubProtocolPayload_SubProtocol() {
}

func (*MessageApplication_SubProtocolPayload_Armadillo) isMessageApplication_SubProtocolPayload_SubProtocol() {
}

type MessageApplication_ApplicationData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageApplication_ApplicationData) Reset() {
	*x = MessageApplication_ApplicationData{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_ApplicationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_ApplicationData) ProtoMessage() {}

func (x *MessageApplication_ApplicationData) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_ApplicationData.ProtoReflect.Descriptor instead.
func (*MessageApplication_ApplicationData) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 3}
}

type MessageApplication_Signal struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageApplication_Signal) Reset() {
	*x = MessageApplication_Signal{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_Signal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_Signal) ProtoMessage() {}

func (x *MessageApplication_Signal) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_Signal.ProtoReflect.Descriptor instead.
func (*MessageApplication_Signal) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 4}
}

type MessageApplication_Content struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageApplication_Content) Reset() {
	*x = MessageApplication_Content{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_Content) ProtoMessage() {}

func (x *MessageApplication_Content) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_Content.ProtoReflect.Descriptor instead.
func (*MessageApplication_Content) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 5}
}

type MessageApplication_EphemeralSetting struct {
	state                     protoimpl.MessageState                                `protogen:"open.v1"`
	EphemeralExpiration       *uint32                                               `protobuf:"varint,2,opt,name=ephemeralExpiration" json:"ephemeralExpiration,omitempty"`
	EphemeralSettingTimestamp *int64                                                `protobuf:"varint,3,opt,name=ephemeralSettingTimestamp" json:"ephemeralSettingTimestamp,omitempty"`
	EphemeralityType          *MessageApplication_EphemeralSetting_EphemeralityType `protobuf:"varint,5,opt,name=ephemeralityType,enum=WAMsgApplication.MessageApplication_EphemeralSetting_EphemeralityType" json:"ephemeralityType,omitempty"`
	IsEphemeralSettingReset   *bool                                                 `protobuf:"varint,4,opt,name=isEphemeralSettingReset" json:"isEphemeralSettingReset,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *MessageApplication_EphemeralSetting) Reset() {
	*x = MessageApplication_EphemeralSetting{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_EphemeralSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_EphemeralSetting) ProtoMessage() {}

func (x *MessageApplication_EphemeralSetting) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_EphemeralSetting.ProtoReflect.Descriptor instead.
func (*MessageApplication_EphemeralSetting) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 6}
}

func (x *MessageApplication_EphemeralSetting) GetEphemeralExpiration() uint32 {
	if x != nil && x.EphemeralExpiration != nil {
		return *x.EphemeralExpiration
	}
	return 0
}

func (x *MessageApplication_EphemeralSetting) GetEphemeralSettingTimestamp() int64 {
	if x != nil && x.EphemeralSettingTimestamp != nil {
		return *x.EphemeralSettingTimestamp
	}
	return 0
}

func (x *MessageApplication_EphemeralSetting) GetEphemeralityType() MessageApplication_EphemeralSetting_EphemeralityType {
	if x != nil && x.EphemeralityType != nil {
		return *x.EphemeralityType
	}
	return MessageApplication_EphemeralSetting_UNKNOWN
}

func (x *MessageApplication_EphemeralSetting) GetIsEphemeralSettingReset() bool {
	if x != nil && x.IsEphemeralSettingReset != nil {
		return *x.IsEphemeralSettingReset
	}
	return false
}

type MessageApplication_Metadata_QuotedMessage struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	StanzaID      *string                     `protobuf:"bytes,1,opt,name=stanzaID" json:"stanzaID,omitempty"`
	RemoteJID     *string                     `protobuf:"bytes,2,opt,name=remoteJID" json:"remoteJID,omitempty"`
	Participant   *string                     `protobuf:"bytes,3,opt,name=participant" json:"participant,omitempty"`
	Payload       *MessageApplication_Payload `protobuf:"bytes,4,opt,name=payload" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageApplication_Metadata_QuotedMessage) Reset() {
	*x = MessageApplication_Metadata_QuotedMessage{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_Metadata_QuotedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_Metadata_QuotedMessage) ProtoMessage() {}

func (x *MessageApplication_Metadata_QuotedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_Metadata_QuotedMessage.ProtoReflect.Descriptor instead.
func (*MessageApplication_Metadata_QuotedMessage) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *MessageApplication_Metadata_QuotedMessage) GetStanzaID() string {
	if x != nil && x.StanzaID != nil {
		return *x.StanzaID
	}
	return ""
}

func (x *MessageApplication_Metadata_QuotedMessage) GetRemoteJID() string {
	if x != nil && x.RemoteJID != nil {
		return *x.RemoteJID
	}
	return ""
}

func (x *MessageApplication_Metadata_QuotedMessage) GetParticipant() string {
	if x != nil && x.Participant != nil {
		return *x.Participant
	}
	return ""
}

func (x *MessageApplication_Metadata_QuotedMessage) GetPayload() *MessageApplication_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type MessageApplication_Metadata_EphemeralSettingMap struct {
	state            protoimpl.MessageState               `protogen:"open.v1"`
	ChatJID          *string                              `protobuf:"bytes,1,opt,name=chatJID" json:"chatJID,omitempty"`
	EphemeralSetting *MessageApplication_EphemeralSetting `protobuf:"bytes,2,opt,name=ephemeralSetting" json:"ephemeralSetting,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MessageApplication_Metadata_EphemeralSettingMap) Reset() {
	*x = MessageApplication_Metadata_EphemeralSettingMap{}
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageApplication_Metadata_EphemeralSettingMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageApplication_Metadata_EphemeralSettingMap) ProtoMessage() {}

func (x *MessageApplication_Metadata_EphemeralSettingMap) ProtoReflect() protoreflect.Message {
	mi := &file_waMsgApplication_WAMsgApplication_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageApplication_Metadata_EphemeralSettingMap.ProtoReflect.Descriptor instead.
func (*MessageApplication_Metadata_EphemeralSettingMap) Descriptor() ([]byte, []int) {
	return file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *MessageApplication_Metadata_EphemeralSettingMap) GetChatJID() string {
	if x != nil && x.ChatJID != nil {
		return *x.ChatJID
	}
	return ""
}

func (x *MessageApplication_Metadata_EphemeralSettingMap) GetEphemeralSetting() *MessageApplication_EphemeralSetting {
	if x != nil {
		return x.EphemeralSetting
	}
	return nil
}

var File_waMsgApplication_WAMsgApplication_proto protoreflect.FileDescriptor

const file_waMsgApplication_WAMsgApplication_proto_rawDesc = "" +
	"\n" +
	"'waMsgApplication/WAMsgApplication.proto\x12\x10WAMsgApplication\x1a\x17waCommon/WACommon.proto\"\x8e\x16\n" +
	"\x12MessageApplication\x12F\n" +
	"\apayload\x18\x01 \x01(\v2,.WAMsgApplication.MessageApplication.PayloadR\apayload\x12I\n" +
	"\bmetadata\x18\x02 \x01(\v2-.WAMsgApplication.MessageApplication.MetadataR\bmetadata\x1a\xe7\n" +
	"\n" +
	"\bMetadata\x12k\n" +
	"\x14chatEphemeralSetting\x18\x01 \x01(\v25.WAMsgApplication.MessageApplication.EphemeralSettingH\x00R\x14chatEphemeralSetting\x12w\n" +
	"\x14ephemeralSettingList\x18\x02 \x01(\v2A.WAMsgApplication.MessageApplication.Metadata.EphemeralSettingMapH\x00R\x14ephemeralSettingList\x126\n" +
	"\x15ephemeralSharedSecret\x18\x03 \x01(\fH\x00R\x15ephemeralSharedSecret\x12(\n" +
	"\x0fforwardingScore\x18\x05 \x01(\rR\x0fforwardingScore\x12 \n" +
	"\visForwarded\x18\x06 \x01(\bR\visForwarded\x12A\n" +
	"\x10businessMetadata\x18\a \x01(\v2\x15.WACommon.SubProtocolR\x10businessMetadata\x12 \n" +
	"\vfrankingKey\x18\b \x01(\fR\vfrankingKey\x12(\n" +
	"\x0ffrankingVersion\x18\t \x01(\x05R\x0ffrankingVersion\x12a\n" +
	"\rquotedMessage\x18\n" +
	" \x01(\v2;.WAMsgApplication.MessageApplication.Metadata.QuotedMessageR\rquotedMessage\x12X\n" +
	"\n" +
	"threadType\x18\v \x01(\x0e28.WAMsgApplication.MessageApplication.Metadata.ThreadTypeR\n" +
	"threadType\x12<\n" +
	"\x19readonlyMetadataDataclass\x18\f \x01(\tR\x19readonlyMetadataDataclass\x12\x18\n" +
	"\agroupID\x18\r \x01(\tR\agroupID\x12\x1c\n" +
	"\tgroupSize\x18\x0e \x01(\rR\tgroupSize\x12\x1e\n" +
	"\n" +
	"groupIndex\x18\x0f \x01(\rR\n" +
	"groupIndex\x12$\n" +
	"\rbotResponseID\x18\x10 \x01(\tR\rbotResponseID\x12$\n" +
	"\rcollapsibleID\x18\x11 \x01(\tR\rcollapsibleID\x12$\n" +
	"\rsecondaryOtid\x18\x12 \x01(\tR\rsecondaryOtid\x1a\xb3\x01\n" +
	"\rQuotedMessage\x12\x1a\n" +
	"\bstanzaID\x18\x01 \x01(\tR\bstanzaID\x12\x1c\n" +
	"\tremoteJID\x18\x02 \x01(\tR\tremoteJID\x12 \n" +
	"\vparticipant\x18\x03 \x01(\tR\vparticipant\x12F\n" +
	"\apayload\x18\x04 \x01(\v2,.WAMsgApplication.MessageApplication.PayloadR\apayload\x1a\x92\x01\n" +
	"\x13EphemeralSettingMap\x12\x18\n" +
	"\achatJID\x18\x01 \x01(\tR\achatJID\x12a\n" +
	"\x10ephemeralSetting\x18\x02 \x01(\v25.WAMsgApplication.MessageApplication.EphemeralSettingR\x10ephemeralSetting\"E\n" +
	"\n" +
	"ThreadType\x12\v\n" +
	"\aDEFAULT\x10\x00\x12\x0f\n" +
	"\vVANISH_MODE\x10\x01\x12\x19\n" +
	"\x15DISAPPEARING_MESSAGES\x10\x02B\v\n" +
	"\tephemeral\x1a\xec\x02\n" +
	"\aPayload\x12P\n" +
	"\vcoreContent\x18\x01 \x01(\v2,.WAMsgApplication.MessageApplication.ContentH\x00R\vcoreContent\x12E\n" +
	"\x06signal\x18\x02 \x01(\v2+.WAMsgApplication.MessageApplication.SignalH\x00R\x06signal\x12`\n" +
	"\x0fapplicationData\x18\x03 \x01(\v24.WAMsgApplication.MessageApplication.ApplicationDataH\x00R\x0fapplicationData\x12[\n" +
	"\vsubProtocol\x18\x04 \x01(\v27.WAMsgApplication.MessageApplication.SubProtocolPayloadH\x00R\vsubProtocolB\t\n" +
	"\acontent\x1a\xca\x03\n" +
	"\x12SubProtocolPayload\x12A\n" +
	"\x0fconsumerMessage\x18\x02 \x01(\v2\x15.WACommon.SubProtocolH\x00R\x0fconsumerMessage\x12A\n" +
	"\x0fbusinessMessage\x18\x03 \x01(\v2\x15.WACommon.SubProtocolH\x00R\x0fbusinessMessage\x12?\n" +
	"\x0epaymentMessage\x18\x04 \x01(\v2\x15.WACommon.SubProtocolH\x00R\x0epaymentMessage\x129\n" +
	"\vmultiDevice\x18\x05 \x01(\v2\x15.WACommon.SubProtocolH\x00R\vmultiDevice\x12+\n" +
	"\x04voip\x18\x06 \x01(\v2\x15.WACommon.SubProtocolH\x00R\x04voip\x125\n" +
	"\tarmadillo\x18\a \x01(\v2\x15.WACommon.SubProtocolH\x00R\tarmadillo\x12?\n" +
	"\vfutureProof\x18\x01 \x01(\x0e2\x1d.WACommon.FutureProofBehaviorR\vfutureProofB\r\n" +
	"\vsubProtocol\x1a\x11\n" +
	"\x0fApplicationData\x1a\b\n" +
	"\x06Signal\x1a\t\n" +
	"\aContent\x1a\x96\x03\n" +
	"\x10EphemeralSetting\x120\n" +
	"\x13ephemeralExpiration\x18\x02 \x01(\rR\x13ephemeralExpiration\x12<\n" +
	"\x19ephemeralSettingTimestamp\x18\x03 \x01(\x03R\x19ephemeralSettingTimestamp\x12r\n" +
	"\x10ephemeralityType\x18\x05 \x01(\x0e2F.WAMsgApplication.MessageApplication.EphemeralSetting.EphemeralityTypeR\x10ephemeralityType\x128\n" +
	"\x17isEphemeralSettingReset\x18\x04 \x01(\bR\x17isEphemeralSettingReset\"d\n" +
	"\x10EphemeralityType\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\r\n" +
	"\tSEEN_ONCE\x10\x01\x12\x19\n" +
	"\x15SEEN_BASED_WITH_TIMER\x10\x02\x12\x19\n" +
	"\x15SEND_BASED_WITH_TIMER\x10\x03B,Z*go.mau.fi/whatsmeow/proto/waMsgApplication"

var (
	file_waMsgApplication_WAMsgApplication_proto_rawDescOnce sync.Once
	file_waMsgApplication_WAMsgApplication_proto_rawDescData []byte
)

func file_waMsgApplication_WAMsgApplication_proto_rawDescGZIP() []byte {
	file_waMsgApplication_WAMsgApplication_proto_rawDescOnce.Do(func() {
		file_waMsgApplication_WAMsgApplication_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waMsgApplication_WAMsgApplication_proto_rawDesc), len(file_waMsgApplication_WAMsgApplication_proto_rawDesc)))
	})
	return file_waMsgApplication_WAMsgApplication_proto_rawDescData
}

var file_waMsgApplication_WAMsgApplication_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_waMsgApplication_WAMsgApplication_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_waMsgApplication_WAMsgApplication_proto_goTypes = []any{
	(MessageApplication_Metadata_ThreadType)(0),               // 0: WAMsgApplication.MessageApplication.Metadata.ThreadType
	(MessageApplication_EphemeralSetting_EphemeralityType)(0), // 1: WAMsgApplication.MessageApplication.EphemeralSetting.EphemeralityType
	(*MessageApplication)(nil),                                // 2: WAMsgApplication.MessageApplication
	(*MessageApplication_Metadata)(nil),                       // 3: WAMsgApplication.MessageApplication.Metadata
	(*MessageApplication_Payload)(nil),                        // 4: WAMsgApplication.MessageApplication.Payload
	(*MessageApplication_SubProtocolPayload)(nil),             // 5: WAMsgApplication.MessageApplication.SubProtocolPayload
	(*MessageApplication_ApplicationData)(nil),                // 6: WAMsgApplication.MessageApplication.ApplicationData
	(*MessageApplication_Signal)(nil),                         // 7: WAMsgApplication.MessageApplication.Signal
	(*MessageApplication_Content)(nil),                        // 8: WAMsgApplication.MessageApplication.Content
	(*MessageApplication_EphemeralSetting)(nil),               // 9: WAMsgApplication.MessageApplication.EphemeralSetting
	(*MessageApplication_Metadata_QuotedMessage)(nil),         // 10: WAMsgApplication.MessageApplication.Metadata.QuotedMessage
	(*MessageApplication_Metadata_EphemeralSettingMap)(nil),   // 11: WAMsgApplication.MessageApplication.Metadata.EphemeralSettingMap
	(*waCommon.SubProtocol)(nil),                              // 12: WACommon.SubProtocol
	(waCommon.FutureProofBehavior)(0),                         // 13: WACommon.FutureProofBehavior
}
var file_waMsgApplication_WAMsgApplication_proto_depIdxs = []int32{
	4,  // 0: WAMsgApplication.MessageApplication.payload:type_name -> WAMsgApplication.MessageApplication.Payload
	3,  // 1: WAMsgApplication.MessageApplication.metadata:type_name -> WAMsgApplication.MessageApplication.Metadata
	9,  // 2: WAMsgApplication.MessageApplication.Metadata.chatEphemeralSetting:type_name -> WAMsgApplication.MessageApplication.EphemeralSetting
	11, // 3: WAMsgApplication.MessageApplication.Metadata.ephemeralSettingList:type_name -> WAMsgApplication.MessageApplication.Metadata.EphemeralSettingMap
	12, // 4: WAMsgApplication.MessageApplication.Metadata.businessMetadata:type_name -> WACommon.SubProtocol
	10, // 5: WAMsgApplication.MessageApplication.Metadata.quotedMessage:type_name -> WAMsgApplication.MessageApplication.Metadata.QuotedMessage
	0,  // 6: WAMsgApplication.MessageApplication.Metadata.threadType:type_name -> WAMsgApplication.MessageApplication.Metadata.ThreadType
	8,  // 7: WAMsgApplication.MessageApplication.Payload.coreContent:type_name -> WAMsgApplication.MessageApplication.Content
	7,  // 8: WAMsgApplication.MessageApplication.Payload.signal:type_name -> WAMsgApplication.MessageApplication.Signal
	6,  // 9: WAMsgApplication.MessageApplication.Payload.applicationData:type_name -> WAMsgApplication.MessageApplication.ApplicationData
	5,  // 10: WAMsgApplication.MessageApplication.Payload.subProtocol:type_name -> WAMsgApplication.MessageApplication.SubProtocolPayload
	12, // 11: WAMsgApplication.MessageApplication.SubProtocolPayload.consumerMessage:type_name -> WACommon.SubProtocol
	12, // 12: WAMsgApplication.MessageApplication.SubProtocolPayload.businessMessage:type_name -> WACommon.SubProtocol
	12, // 13: WAMsgApplication.MessageApplication.SubProtocolPayload.paymentMessage:type_name -> WACommon.SubProtocol
	12, // 14: WAMsgApplication.MessageApplication.SubProtocolPayload.multiDevice:type_name -> WACommon.SubProtocol
	12, // 15: WAMsgApplication.MessageApplication.SubProtocolPayload.voip:type_name -> WACommon.SubProtocol
	12, // 16: WAMsgApplication.MessageApplication.SubProtocolPayload.armadillo:type_name -> WACommon.SubProtocol
	13, // 17: WAMsgApplication.MessageApplication.SubProtocolPayload.futureProof:type_name -> WACommon.FutureProofBehavior
	1,  // 18: WAMsgApplication.MessageApplication.EphemeralSetting.ephemeralityType:type_name -> WAMsgApplication.MessageApplication.EphemeralSetting.EphemeralityType
	4,  // 19: WAMsgApplication.MessageApplication.Metadata.QuotedMessage.payload:type_name -> WAMsgApplication.MessageApplication.Payload
	9,  // 20: WAMsgApplication.MessageApplication.Metadata.EphemeralSettingMap.ephemeralSetting:type_name -> WAMsgApplication.MessageApplication.EphemeralSetting
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_waMsgApplication_WAMsgApplication_proto_init() }
func file_waMsgApplication_WAMsgApplication_proto_init() {
	if File_waMsgApplication_WAMsgApplication_proto != nil {
		return
	}
	file_waMsgApplication_WAMsgApplication_proto_msgTypes[1].OneofWrappers = []any{
		(*MessageApplication_Metadata_ChatEphemeralSetting)(nil),
		(*MessageApplication_Metadata_EphemeralSettingList)(nil),
		(*MessageApplication_Metadata_EphemeralSharedSecret)(nil),
	}
	file_waMsgApplication_WAMsgApplication_proto_msgTypes[2].OneofWrappers = []any{
		(*MessageApplication_Payload_CoreContent)(nil),
		(*MessageApplication_Payload_Signal)(nil),
		(*MessageApplication_Payload_ApplicationData)(nil),
		(*MessageApplication_Payload_SubProtocol)(nil),
	}
	file_waMsgApplication_WAMsgApplication_proto_msgTypes[3].OneofWrappers = []any{
		(*MessageApplication_SubProtocolPayload_ConsumerMessage)(nil),
		(*MessageApplication_SubProtocolPayload_BusinessMessage)(nil),
		(*MessageApplication_SubProtocolPayload_PaymentMessage)(nil),
		(*MessageApplication_SubProtocolPayload_MultiDevice)(nil),
		(*MessageApplication_SubProtocolPayload_Voip)(nil),
		(*MessageApplication_SubProtocolPayload_Armadillo)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waMsgApplication_WAMsgApplication_proto_rawDesc), len(file_waMsgApplication_WAMsgApplication_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waMsgApplication_WAMsgApplication_proto_goTypes,
		DependencyIndexes: file_waMsgApplication_WAMsgApplication_proto_depIdxs,
		EnumInfos:         file_waMsgApplication_WAMsgApplication_proto_enumTypes,
		MessageInfos:      file_waMsgApplication_WAMsgApplication_proto_msgTypes,
	}.Build()
	File_waMsgApplication_WAMsgApplication_proto = out.File
	file_waMsgApplication_WAMsgApplication_proto_goTypes = nil
	file_waMsgApplication_WAMsgApplication_proto_depIdxs = nil
}
