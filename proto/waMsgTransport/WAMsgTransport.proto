syntax = "proto2";
package WAMsgTransport;
option go_package = "go.mau.fi/whatsmeow/proto/waMsgTransport";

import "waCommon/WACommon.proto";

message MessageTransport {
	message Payload {
		optional WACommon.SubProtocol applicationPayload = 1;
		optional WACommon.FutureProofBehavior futureProof = 3;
	}

	message Protocol {
		message Ancillary {
			message BackupDirective {
				enum ActionType {
					NOOP = 0;
					UPSERT = 1;
					DELETE = 2;
					UPSERT_AND_DELETE = 3;
				}

				optional string messageID = 1;
				optional ActionType actionType = 2;
				optional string supplementalKey = 3;
			}

			message ICDCParticipantDevices {
				message ICDCIdentityListDescription {
					optional int32 seq = 1;
					optional bytes signingDevice = 2;
					repeated bytes unknownDevices = 3;
					repeated int32 unknownDeviceIDs = 4;
				}

				optional ICDCIdentityListDescription senderIdentity = 1;
				repeated ICDCIdentityListDescription recipientIdentities = 2;
				repeated string recipientUserJIDs = 3;
			}

			message SenderKeyDistributionMessage {
				optional string groupID = 1;
				optional bytes axolotlSenderKeyDistributionMessage = 2;
			}

			optional SenderKeyDistributionMessage skdm = 2;
			optional DeviceListMetadata deviceListMetadata = 3;
			optional ICDCParticipantDevices icdc = 4;
			optional BackupDirective backupDirective = 5;
		}

		message Integral {
			message DeviceSentMessage {
				optional string destinationJID = 1;
				optional string phash = 2;
			}

			optional bytes padding = 1;
			optional DeviceSentMessage DSM = 2;
		}

		optional Integral integral = 1;
		optional Ancillary ancillary = 2;
	}

	optional Payload payload = 1;
	optional Protocol protocol = 2;
}

message DeviceListMetadata {
	optional bytes senderKeyHash = 1;
	optional uint64 senderTimestamp = 2;
	optional bytes recipientKeyHash = 8;
	optional uint64 recipientTimestamp = 9;
}
