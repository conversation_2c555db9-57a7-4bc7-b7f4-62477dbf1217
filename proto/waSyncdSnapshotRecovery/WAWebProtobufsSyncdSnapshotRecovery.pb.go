// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waSyncdSnapshotRecovery/WAWebProtobufsSyncdSnapshotRecovery.proto

package waSyncdSnapshotRecovery

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waSyncAction "go.mau.fi/whatsmeow/proto/waSyncAction"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SyncdSnapshotRecovery struct {
	state            protoimpl.MessageState  `protogen:"open.v1"`
	Version          *SyncdVersion           `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	CollectionName   *string                 `protobuf:"bytes,2,opt,name=collectionName" json:"collectionName,omitempty"`
	MutationRecords  []*SyncdPlainTextRecord `protobuf:"bytes,3,rep,name=mutationRecords" json:"mutationRecords,omitempty"`
	CollectionLthash []byte                  `protobuf:"bytes,4,opt,name=collectionLthash" json:"collectionLthash,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SyncdSnapshotRecovery) Reset() {
	*x = SyncdSnapshotRecovery{}
	mi := &file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncdSnapshotRecovery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncdSnapshotRecovery) ProtoMessage() {}

func (x *SyncdSnapshotRecovery) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncdSnapshotRecovery.ProtoReflect.Descriptor instead.
func (*SyncdSnapshotRecovery) Descriptor() ([]byte, []int) {
	return file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescGZIP(), []int{0}
}

func (x *SyncdSnapshotRecovery) GetVersion() *SyncdVersion {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *SyncdSnapshotRecovery) GetCollectionName() string {
	if x != nil && x.CollectionName != nil {
		return *x.CollectionName
	}
	return ""
}

func (x *SyncdSnapshotRecovery) GetMutationRecords() []*SyncdPlainTextRecord {
	if x != nil {
		return x.MutationRecords
	}
	return nil
}

func (x *SyncdSnapshotRecovery) GetCollectionLthash() []byte {
	if x != nil {
		return x.CollectionLthash
	}
	return nil
}

type SyncdPlainTextRecord struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Value         *waSyncAction.SyncActionData `protobuf:"bytes,1,opt,name=value" json:"value,omitempty"`
	KeyID         []byte                       `protobuf:"bytes,2,opt,name=keyID" json:"keyID,omitempty"`
	Mac           []byte                       `protobuf:"bytes,3,opt,name=mac" json:"mac,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncdPlainTextRecord) Reset() {
	*x = SyncdPlainTextRecord{}
	mi := &file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncdPlainTextRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncdPlainTextRecord) ProtoMessage() {}

func (x *SyncdPlainTextRecord) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncdPlainTextRecord.ProtoReflect.Descriptor instead.
func (*SyncdPlainTextRecord) Descriptor() ([]byte, []int) {
	return file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescGZIP(), []int{1}
}

func (x *SyncdPlainTextRecord) GetValue() *waSyncAction.SyncActionData {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *SyncdPlainTextRecord) GetKeyID() []byte {
	if x != nil {
		return x.KeyID
	}
	return nil
}

func (x *SyncdPlainTextRecord) GetMac() []byte {
	if x != nil {
		return x.Mac
	}
	return nil
}

type SyncdVersion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       *uint64                `protobuf:"varint,1,opt,name=version" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncdVersion) Reset() {
	*x = SyncdVersion{}
	mi := &file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncdVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncdVersion) ProtoMessage() {}

func (x *SyncdVersion) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncdVersion.ProtoReflect.Descriptor instead.
func (*SyncdVersion) Descriptor() ([]byte, []int) {
	return file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescGZIP(), []int{2}
}

func (x *SyncdVersion) GetVersion() uint64 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

var File_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto protoreflect.FileDescriptor

const file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDesc = "" +
	"\n" +
	"AwaSyncdSnapshotRecovery/WAWebProtobufsSyncdSnapshotRecovery.proto\x12#WAWebProtobufsSyncdSnapshotRecovery\x1a\x1fwaSyncAction/WASyncAction.proto\"\x9d\x02\n" +
	"\x15SyncdSnapshotRecovery\x12K\n" +
	"\aversion\x18\x01 \x01(\v21.WAWebProtobufsSyncdSnapshotRecovery.SyncdVersionR\aversion\x12&\n" +
	"\x0ecollectionName\x18\x02 \x01(\tR\x0ecollectionName\x12c\n" +
	"\x0fmutationRecords\x18\x03 \x03(\v29.WAWebProtobufsSyncdSnapshotRecovery.SyncdPlainTextRecordR\x0fmutationRecords\x12*\n" +
	"\x10collectionLthash\x18\x04 \x01(\fR\x10collectionLthash\"r\n" +
	"\x14SyncdPlainTextRecord\x122\n" +
	"\x05value\x18\x01 \x01(\v2\x1c.WASyncAction.SyncActionDataR\x05value\x12\x14\n" +
	"\x05keyID\x18\x02 \x01(\fR\x05keyID\x12\x10\n" +
	"\x03mac\x18\x03 \x01(\fR\x03mac\"(\n" +
	"\fSyncdVersion\x12\x18\n" +
	"\aversion\x18\x01 \x01(\x04R\aversionB3Z1go.mau.fi/whatsmeow/proto/waSyncdSnapshotRecovery"

var (
	file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescOnce sync.Once
	file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescData []byte
)

func file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescGZIP() []byte {
	file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescOnce.Do(func() {
		file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDesc), len(file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDesc)))
	})
	return file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDescData
}

var file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_goTypes = []any{
	(*SyncdSnapshotRecovery)(nil),       // 0: WAWebProtobufsSyncdSnapshotRecovery.SyncdSnapshotRecovery
	(*SyncdPlainTextRecord)(nil),        // 1: WAWebProtobufsSyncdSnapshotRecovery.SyncdPlainTextRecord
	(*SyncdVersion)(nil),                // 2: WAWebProtobufsSyncdSnapshotRecovery.SyncdVersion
	(*waSyncAction.SyncActionData)(nil), // 3: WASyncAction.SyncActionData
}
var file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_depIdxs = []int32{
	2, // 0: WAWebProtobufsSyncdSnapshotRecovery.SyncdSnapshotRecovery.version:type_name -> WAWebProtobufsSyncdSnapshotRecovery.SyncdVersion
	1, // 1: WAWebProtobufsSyncdSnapshotRecovery.SyncdSnapshotRecovery.mutationRecords:type_name -> WAWebProtobufsSyncdSnapshotRecovery.SyncdPlainTextRecord
	3, // 2: WAWebProtobufsSyncdSnapshotRecovery.SyncdPlainTextRecord.value:type_name -> WASyncAction.SyncActionData
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_init() }
func file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_init() {
	if File_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDesc), len(file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_goTypes,
		DependencyIndexes: file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_depIdxs,
		MessageInfos:      file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_msgTypes,
	}.Build()
	File_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto = out.File
	file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_goTypes = nil
	file_waSyncdSnapshotRecovery_WAWebProtobufsSyncdSnapshotRecovery_proto_depIdxs = nil
}
