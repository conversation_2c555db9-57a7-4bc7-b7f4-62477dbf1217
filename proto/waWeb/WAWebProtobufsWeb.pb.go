// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waWeb/WAWebProtobufsWeb.proto

package waWeb

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waCommon "go.mau.fi/whatsmeow/proto/waCommon"
	waE2E "go.mau.fi/whatsmeow/proto/waE2E"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WebMessageInfo_BizPrivacyStatus int32

const (
	WebMessageInfo_E2EE       WebMessageInfo_BizPrivacyStatus = 0
	WebMessageInfo_FB         WebMessageInfo_BizPrivacyStatus = 2
	WebMessageInfo_BSP        WebMessageInfo_BizPrivacyStatus = 1
	WebMessageInfo_BSP_AND_FB WebMessageInfo_BizPrivacyStatus = 3
)

// Enum value maps for WebMessageInfo_BizPrivacyStatus.
var (
	WebMessageInfo_BizPrivacyStatus_name = map[int32]string{
		0: "E2EE",
		2: "FB",
		1: "BSP",
		3: "BSP_AND_FB",
	}
	WebMessageInfo_BizPrivacyStatus_value = map[string]int32{
		"E2EE":       0,
		"FB":         2,
		"BSP":        1,
		"BSP_AND_FB": 3,
	}
)

func (x WebMessageInfo_BizPrivacyStatus) Enum() *WebMessageInfo_BizPrivacyStatus {
	p := new(WebMessageInfo_BizPrivacyStatus)
	*p = x
	return p
}

func (x WebMessageInfo_BizPrivacyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WebMessageInfo_BizPrivacyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[0].Descriptor()
}

func (WebMessageInfo_BizPrivacyStatus) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[0]
}

func (x WebMessageInfo_BizPrivacyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *WebMessageInfo_BizPrivacyStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = WebMessageInfo_BizPrivacyStatus(num)
	return nil
}

// Deprecated: Use WebMessageInfo_BizPrivacyStatus.Descriptor instead.
func (WebMessageInfo_BizPrivacyStatus) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{0, 0}
}

type WebMessageInfo_StubType int32

const (
	WebMessageInfo_UNKNOWN                                                  WebMessageInfo_StubType = 0
	WebMessageInfo_REVOKE                                                   WebMessageInfo_StubType = 1
	WebMessageInfo_CIPHERTEXT                                               WebMessageInfo_StubType = 2
	WebMessageInfo_FUTUREPROOF                                              WebMessageInfo_StubType = 3
	WebMessageInfo_NON_VERIFIED_TRANSITION                                  WebMessageInfo_StubType = 4
	WebMessageInfo_UNVERIFIED_TRANSITION                                    WebMessageInfo_StubType = 5
	WebMessageInfo_VERIFIED_TRANSITION                                      WebMessageInfo_StubType = 6
	WebMessageInfo_VERIFIED_LOW_UNKNOWN                                     WebMessageInfo_StubType = 7
	WebMessageInfo_VERIFIED_HIGH                                            WebMessageInfo_StubType = 8
	WebMessageInfo_VERIFIED_INITIAL_UNKNOWN                                 WebMessageInfo_StubType = 9
	WebMessageInfo_VERIFIED_INITIAL_LOW                                     WebMessageInfo_StubType = 10
	WebMessageInfo_VERIFIED_INITIAL_HIGH                                    WebMessageInfo_StubType = 11
	WebMessageInfo_VERIFIED_TRANSITION_ANY_TO_NONE                          WebMessageInfo_StubType = 12
	WebMessageInfo_VERIFIED_TRANSITION_ANY_TO_HIGH                          WebMessageInfo_StubType = 13
	WebMessageInfo_VERIFIED_TRANSITION_HIGH_TO_LOW                          WebMessageInfo_StubType = 14
	WebMessageInfo_VERIFIED_TRANSITION_HIGH_TO_UNKNOWN                      WebMessageInfo_StubType = 15
	WebMessageInfo_VERIFIED_TRANSITION_UNKNOWN_TO_LOW                       WebMessageInfo_StubType = 16
	WebMessageInfo_VERIFIED_TRANSITION_LOW_TO_UNKNOWN                       WebMessageInfo_StubType = 17
	WebMessageInfo_VERIFIED_TRANSITION_NONE_TO_LOW                          WebMessageInfo_StubType = 18
	WebMessageInfo_VERIFIED_TRANSITION_NONE_TO_UNKNOWN                      WebMessageInfo_StubType = 19
	WebMessageInfo_GROUP_CREATE                                             WebMessageInfo_StubType = 20
	WebMessageInfo_GROUP_CHANGE_SUBJECT                                     WebMessageInfo_StubType = 21
	WebMessageInfo_GROUP_CHANGE_ICON                                        WebMessageInfo_StubType = 22
	WebMessageInfo_GROUP_CHANGE_INVITE_LINK                                 WebMessageInfo_StubType = 23
	WebMessageInfo_GROUP_CHANGE_DESCRIPTION                                 WebMessageInfo_StubType = 24
	WebMessageInfo_GROUP_CHANGE_RESTRICT                                    WebMessageInfo_StubType = 25
	WebMessageInfo_GROUP_CHANGE_ANNOUNCE                                    WebMessageInfo_StubType = 26
	WebMessageInfo_GROUP_PARTICIPANT_ADD                                    WebMessageInfo_StubType = 27
	WebMessageInfo_GROUP_PARTICIPANT_REMOVE                                 WebMessageInfo_StubType = 28
	WebMessageInfo_GROUP_PARTICIPANT_PROMOTE                                WebMessageInfo_StubType = 29
	WebMessageInfo_GROUP_PARTICIPANT_DEMOTE                                 WebMessageInfo_StubType = 30
	WebMessageInfo_GROUP_PARTICIPANT_INVITE                                 WebMessageInfo_StubType = 31
	WebMessageInfo_GROUP_PARTICIPANT_LEAVE                                  WebMessageInfo_StubType = 32
	WebMessageInfo_GROUP_PARTICIPANT_CHANGE_NUMBER                          WebMessageInfo_StubType = 33
	WebMessageInfo_BROADCAST_CREATE                                         WebMessageInfo_StubType = 34
	WebMessageInfo_BROADCAST_ADD                                            WebMessageInfo_StubType = 35
	WebMessageInfo_BROADCAST_REMOVE                                         WebMessageInfo_StubType = 36
	WebMessageInfo_GENERIC_NOTIFICATION                                     WebMessageInfo_StubType = 37
	WebMessageInfo_E2E_IDENTITY_CHANGED                                     WebMessageInfo_StubType = 38
	WebMessageInfo_E2E_ENCRYPTED                                            WebMessageInfo_StubType = 39
	WebMessageInfo_CALL_MISSED_VOICE                                        WebMessageInfo_StubType = 40
	WebMessageInfo_CALL_MISSED_VIDEO                                        WebMessageInfo_StubType = 41
	WebMessageInfo_INDIVIDUAL_CHANGE_NUMBER                                 WebMessageInfo_StubType = 42
	WebMessageInfo_GROUP_DELETE                                             WebMessageInfo_StubType = 43
	WebMessageInfo_GROUP_ANNOUNCE_MODE_MESSAGE_BOUNCE                       WebMessageInfo_StubType = 44
	WebMessageInfo_CALL_MISSED_GROUP_VOICE                                  WebMessageInfo_StubType = 45
	WebMessageInfo_CALL_MISSED_GROUP_VIDEO                                  WebMessageInfo_StubType = 46
	WebMessageInfo_PAYMENT_CIPHERTEXT                                       WebMessageInfo_StubType = 47
	WebMessageInfo_PAYMENT_FUTUREPROOF                                      WebMessageInfo_StubType = 48
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_FAILED                 WebMessageInfo_StubType = 49
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_REFUNDED               WebMessageInfo_StubType = 50
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_REFUND_FAILED          WebMessageInfo_StubType = 51
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_RECEIVER_PENDING_SETUP        WebMessageInfo_StubType = 52
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_RECEIVER_SUCCESS_AFTER_HICCUP WebMessageInfo_StubType = 53
	WebMessageInfo_PAYMENT_ACTION_ACCOUNT_SETUP_REMINDER                    WebMessageInfo_StubType = 54
	WebMessageInfo_PAYMENT_ACTION_SEND_PAYMENT_REMINDER                     WebMessageInfo_StubType = 55
	WebMessageInfo_PAYMENT_ACTION_SEND_PAYMENT_INVITATION                   WebMessageInfo_StubType = 56
	WebMessageInfo_PAYMENT_ACTION_REQUEST_DECLINED                          WebMessageInfo_StubType = 57
	WebMessageInfo_PAYMENT_ACTION_REQUEST_EXPIRED                           WebMessageInfo_StubType = 58
	WebMessageInfo_PAYMENT_ACTION_REQUEST_CANCELLED                         WebMessageInfo_StubType = 59
	WebMessageInfo_BIZ_VERIFIED_TRANSITION_TOP_TO_BOTTOM                    WebMessageInfo_StubType = 60
	WebMessageInfo_BIZ_VERIFIED_TRANSITION_BOTTOM_TO_TOP                    WebMessageInfo_StubType = 61
	WebMessageInfo_BIZ_INTRO_TOP                                            WebMessageInfo_StubType = 62
	WebMessageInfo_BIZ_INTRO_BOTTOM                                         WebMessageInfo_StubType = 63
	WebMessageInfo_BIZ_NAME_CHANGE                                          WebMessageInfo_StubType = 64
	WebMessageInfo_BIZ_MOVE_TO_CONSUMER_APP                                 WebMessageInfo_StubType = 65
	WebMessageInfo_BIZ_TWO_TIER_MIGRATION_TOP                               WebMessageInfo_StubType = 66
	WebMessageInfo_BIZ_TWO_TIER_MIGRATION_BOTTOM                            WebMessageInfo_StubType = 67
	WebMessageInfo_OVERSIZED                                                WebMessageInfo_StubType = 68
	WebMessageInfo_GROUP_CHANGE_NO_FREQUENTLY_FORWARDED                     WebMessageInfo_StubType = 69
	WebMessageInfo_GROUP_V4_ADD_INVITE_SENT                                 WebMessageInfo_StubType = 70
	WebMessageInfo_GROUP_PARTICIPANT_ADD_REQUEST_JOIN                       WebMessageInfo_StubType = 71
	WebMessageInfo_CHANGE_EPHEMERAL_SETTING                                 WebMessageInfo_StubType = 72
	WebMessageInfo_E2E_DEVICE_CHANGED                                       WebMessageInfo_StubType = 73
	WebMessageInfo_VIEWED_ONCE                                              WebMessageInfo_StubType = 74
	WebMessageInfo_E2E_ENCRYPTED_NOW                                        WebMessageInfo_StubType = 75
	WebMessageInfo_BLUE_MSG_BSP_FB_TO_BSP_PREMISE                           WebMessageInfo_StubType = 76
	WebMessageInfo_BLUE_MSG_BSP_FB_TO_SELF_FB                               WebMessageInfo_StubType = 77
	WebMessageInfo_BLUE_MSG_BSP_FB_TO_SELF_PREMISE                          WebMessageInfo_StubType = 78
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED                               WebMessageInfo_StubType = 79
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED      WebMessageInfo_StubType = 80
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED                                 WebMessageInfo_StubType = 81
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED      WebMessageInfo_StubType = 82
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_TO_SELF_PREMISE                     WebMessageInfo_StubType = 83
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_UNVERIFIED                          WebMessageInfo_StubType = 84
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_UNVERIFIED_TO_SELF_PREMISE_VERIFIED WebMessageInfo_StubType = 85
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_VERIFIED                            WebMessageInfo_StubType = 86
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_VERIFIED_TO_SELF_PREMISE_UNVERIFIED WebMessageInfo_StubType = 87
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_BSP_FB_UNVERIFIED                   WebMessageInfo_StubType = 88
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_BSP_PREMISE_UNVERIFIED              WebMessageInfo_StubType = 89
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_SELF_FB_UNVERIFIED                  WebMessageInfo_StubType = 90
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_SELF_PREMISE_UNVERIFIED             WebMessageInfo_StubType = 91
	WebMessageInfo_BLUE_MSG_SELF_FB_TO_BSP_PREMISE                          WebMessageInfo_StubType = 92
	WebMessageInfo_BLUE_MSG_SELF_FB_TO_SELF_PREMISE                         WebMessageInfo_StubType = 93
	WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED                              WebMessageInfo_StubType = 94
	WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED     WebMessageInfo_StubType = 95
	WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED                                WebMessageInfo_StubType = 96
	WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED     WebMessageInfo_StubType = 97
	WebMessageInfo_BLUE_MSG_SELF_PREMISE_TO_BSP_PREMISE                     WebMessageInfo_StubType = 98
	WebMessageInfo_BLUE_MSG_SELF_PREMISE_UNVERIFIED                         WebMessageInfo_StubType = 99
	WebMessageInfo_BLUE_MSG_SELF_PREMISE_VERIFIED                           WebMessageInfo_StubType = 100
	WebMessageInfo_BLUE_MSG_TO_BSP_FB                                       WebMessageInfo_StubType = 101
	WebMessageInfo_BLUE_MSG_TO_CONSUMER                                     WebMessageInfo_StubType = 102
	WebMessageInfo_BLUE_MSG_TO_SELF_FB                                      WebMessageInfo_StubType = 103
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_BSP_FB_VERIFIED                   WebMessageInfo_StubType = 104
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_BSP_PREMISE_VERIFIED              WebMessageInfo_StubType = 105
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_SELF_FB_VERIFIED                  WebMessageInfo_StubType = 106
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_VERIFIED                          WebMessageInfo_StubType = 107
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_BSP_FB_UNVERIFIED                   WebMessageInfo_StubType = 108
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_BSP_PREMISE_UNVERIFIED              WebMessageInfo_StubType = 109
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_SELF_FB_UNVERIFIED                  WebMessageInfo_StubType = 110
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_UNVERIFIED                          WebMessageInfo_StubType = 111
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED       WebMessageInfo_StubType = 112
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_FB_VERIFIED           WebMessageInfo_StubType = 113
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED       WebMessageInfo_StubType = 114
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_FB_UNVERIFIED           WebMessageInfo_StubType = 115
	WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED      WebMessageInfo_StubType = 116
	WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED      WebMessageInfo_StubType = 117
	WebMessageInfo_E2E_IDENTITY_UNAVAILABLE                                 WebMessageInfo_StubType = 118
	WebMessageInfo_GROUP_CREATING                                           WebMessageInfo_StubType = 119
	WebMessageInfo_GROUP_CREATE_FAILED                                      WebMessageInfo_StubType = 120
	WebMessageInfo_GROUP_BOUNCED                                            WebMessageInfo_StubType = 121
	WebMessageInfo_BLOCK_CONTACT                                            WebMessageInfo_StubType = 122
	WebMessageInfo_EPHEMERAL_SETTING_NOT_APPLIED                            WebMessageInfo_StubType = 123
	WebMessageInfo_SYNC_FAILED                                              WebMessageInfo_StubType = 124
	WebMessageInfo_SYNCING                                                  WebMessageInfo_StubType = 125
	WebMessageInfo_BIZ_PRIVACY_MODE_INIT_FB                                 WebMessageInfo_StubType = 126
	WebMessageInfo_BIZ_PRIVACY_MODE_INIT_BSP                                WebMessageInfo_StubType = 127
	WebMessageInfo_BIZ_PRIVACY_MODE_TO_FB                                   WebMessageInfo_StubType = 128
	WebMessageInfo_BIZ_PRIVACY_MODE_TO_BSP                                  WebMessageInfo_StubType = 129
	WebMessageInfo_DISAPPEARING_MODE                                        WebMessageInfo_StubType = 130
	WebMessageInfo_E2E_DEVICE_FETCH_FAILED                                  WebMessageInfo_StubType = 131
	WebMessageInfo_ADMIN_REVOKE                                             WebMessageInfo_StubType = 132
	WebMessageInfo_GROUP_INVITE_LINK_GROWTH_LOCKED                          WebMessageInfo_StubType = 133
	WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP                              WebMessageInfo_StubType = 134
	WebMessageInfo_COMMUNITY_LINK_SIBLING_GROUP                             WebMessageInfo_StubType = 135
	WebMessageInfo_COMMUNITY_LINK_SUB_GROUP                                 WebMessageInfo_StubType = 136
	WebMessageInfo_COMMUNITY_UNLINK_PARENT_GROUP                            WebMessageInfo_StubType = 137
	WebMessageInfo_COMMUNITY_UNLINK_SIBLING_GROUP                           WebMessageInfo_StubType = 138
	WebMessageInfo_COMMUNITY_UNLINK_SUB_GROUP                               WebMessageInfo_StubType = 139
	WebMessageInfo_GROUP_PARTICIPANT_ACCEPT                                 WebMessageInfo_StubType = 140
	WebMessageInfo_GROUP_PARTICIPANT_LINKED_GROUP_JOIN                      WebMessageInfo_StubType = 141
	WebMessageInfo_COMMUNITY_CREATE                                         WebMessageInfo_StubType = 142
	WebMessageInfo_EPHEMERAL_KEEP_IN_CHAT                                   WebMessageInfo_StubType = 143
	WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST                   WebMessageInfo_StubType = 144
	WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_MODE                      WebMessageInfo_StubType = 145
	WebMessageInfo_INTEGRITY_UNLINK_PARENT_GROUP                            WebMessageInfo_StubType = 146
	WebMessageInfo_COMMUNITY_PARTICIPANT_PROMOTE                            WebMessageInfo_StubType = 147
	WebMessageInfo_COMMUNITY_PARTICIPANT_DEMOTE                             WebMessageInfo_StubType = 148
	WebMessageInfo_COMMUNITY_PARENT_GROUP_DELETED                           WebMessageInfo_StubType = 149
	WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP_MEMBERSHIP_APPROVAL          WebMessageInfo_StubType = 150
	WebMessageInfo_GROUP_PARTICIPANT_JOINED_GROUP_AND_PARENT_GROUP          WebMessageInfo_StubType = 151
	WebMessageInfo_MASKED_THREAD_CREATED                                    WebMessageInfo_StubType = 152
	WebMessageInfo_MASKED_THREAD_UNMASKED                                   WebMessageInfo_StubType = 153
	WebMessageInfo_BIZ_CHAT_ASSIGNMENT                                      WebMessageInfo_StubType = 154
	WebMessageInfo_CHAT_PSA                                                 WebMessageInfo_StubType = 155
	WebMessageInfo_CHAT_POLL_CREATION_MESSAGE                               WebMessageInfo_StubType = 156
	WebMessageInfo_CAG_MASKED_THREAD_CREATED                                WebMessageInfo_StubType = 157
	WebMessageInfo_COMMUNITY_PARENT_GROUP_SUBJECT_CHANGED                   WebMessageInfo_StubType = 158
	WebMessageInfo_CAG_INVITE_AUTO_ADD                                      WebMessageInfo_StubType = 159
	WebMessageInfo_BIZ_CHAT_ASSIGNMENT_UNASSIGN                             WebMessageInfo_StubType = 160
	WebMessageInfo_CAG_INVITE_AUTO_JOINED                                   WebMessageInfo_StubType = 161
	WebMessageInfo_SCHEDULED_CALL_START_MESSAGE                             WebMessageInfo_StubType = 162
	WebMessageInfo_COMMUNITY_INVITE_RICH                                    WebMessageInfo_StubType = 163
	WebMessageInfo_COMMUNITY_INVITE_AUTO_ADD_RICH                           WebMessageInfo_StubType = 164
	WebMessageInfo_SUB_GROUP_INVITE_RICH                                    WebMessageInfo_StubType = 165
	WebMessageInfo_SUB_GROUP_PARTICIPANT_ADD_RICH                           WebMessageInfo_StubType = 166
	WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP_RICH                         WebMessageInfo_StubType = 167
	WebMessageInfo_COMMUNITY_PARTICIPANT_ADD_RICH                           WebMessageInfo_StubType = 168
	WebMessageInfo_SILENCED_UNKNOWN_CALLER_AUDIO                            WebMessageInfo_StubType = 169
	WebMessageInfo_SILENCED_UNKNOWN_CALLER_VIDEO                            WebMessageInfo_StubType = 170
	WebMessageInfo_GROUP_MEMBER_ADD_MODE                                    WebMessageInfo_StubType = 171
	WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST_NON_ADMIN_ADD     WebMessageInfo_StubType = 172
	WebMessageInfo_COMMUNITY_CHANGE_DESCRIPTION                             WebMessageInfo_StubType = 173
	WebMessageInfo_SENDER_INVITE                                            WebMessageInfo_StubType = 174
	WebMessageInfo_RECEIVER_INVITE                                          WebMessageInfo_StubType = 175
	WebMessageInfo_COMMUNITY_ALLOW_MEMBER_ADDED_GROUPS                      WebMessageInfo_StubType = 176
	WebMessageInfo_PINNED_MESSAGE_IN_CHAT                                   WebMessageInfo_StubType = 177
	WebMessageInfo_PAYMENT_INVITE_SETUP_INVITER                             WebMessageInfo_StubType = 178
	WebMessageInfo_PAYMENT_INVITE_SETUP_INVITEE_RECEIVE_ONLY                WebMessageInfo_StubType = 179
	WebMessageInfo_PAYMENT_INVITE_SETUP_INVITEE_SEND_AND_RECEIVE            WebMessageInfo_StubType = 180
	WebMessageInfo_LINKED_GROUP_CALL_START                                  WebMessageInfo_StubType = 181
	WebMessageInfo_REPORT_TO_ADMIN_ENABLED_STATUS                           WebMessageInfo_StubType = 182
	WebMessageInfo_EMPTY_SUBGROUP_CREATE                                    WebMessageInfo_StubType = 183
	WebMessageInfo_SCHEDULED_CALL_CANCEL                                    WebMessageInfo_StubType = 184
	WebMessageInfo_SUBGROUP_ADMIN_TRIGGERED_AUTO_ADD_RICH                   WebMessageInfo_StubType = 185
	WebMessageInfo_GROUP_CHANGE_RECENT_HISTORY_SHARING                      WebMessageInfo_StubType = 186
	WebMessageInfo_PAID_MESSAGE_SERVER_CAMPAIGN_ID                          WebMessageInfo_StubType = 187
	WebMessageInfo_GENERAL_CHAT_CREATE                                      WebMessageInfo_StubType = 188
	WebMessageInfo_GENERAL_CHAT_ADD                                         WebMessageInfo_StubType = 189
	WebMessageInfo_GENERAL_CHAT_AUTO_ADD_DISABLED                           WebMessageInfo_StubType = 190
	WebMessageInfo_SUGGESTED_SUBGROUP_ANNOUNCE                              WebMessageInfo_StubType = 191
	WebMessageInfo_BIZ_BOT_1P_MESSAGING_ENABLED                             WebMessageInfo_StubType = 192
	WebMessageInfo_CHANGE_USERNAME                                          WebMessageInfo_StubType = 193
	WebMessageInfo_BIZ_COEX_PRIVACY_INIT_SELF                               WebMessageInfo_StubType = 194
	WebMessageInfo_BIZ_COEX_PRIVACY_TRANSITION_SELF                         WebMessageInfo_StubType = 195
	WebMessageInfo_SUPPORT_AI_EDUCATION                                     WebMessageInfo_StubType = 196
	WebMessageInfo_BIZ_BOT_3P_MESSAGING_ENABLED                             WebMessageInfo_StubType = 197
	WebMessageInfo_REMINDER_SETUP_MESSAGE                                   WebMessageInfo_StubType = 198
	WebMessageInfo_REMINDER_SENT_MESSAGE                                    WebMessageInfo_StubType = 199
	WebMessageInfo_REMINDER_CANCEL_MESSAGE                                  WebMessageInfo_StubType = 200
	WebMessageInfo_BIZ_COEX_PRIVACY_INIT                                    WebMessageInfo_StubType = 201
	WebMessageInfo_BIZ_COEX_PRIVACY_TRANSITION                              WebMessageInfo_StubType = 202
	WebMessageInfo_GROUP_DEACTIVATED                                        WebMessageInfo_StubType = 203
	WebMessageInfo_COMMUNITY_DEACTIVATE_SIBLING_GROUP                       WebMessageInfo_StubType = 204
	WebMessageInfo_EVENT_UPDATED                                            WebMessageInfo_StubType = 205
	WebMessageInfo_EVENT_CANCELED                                           WebMessageInfo_StubType = 206
	WebMessageInfo_COMMUNITY_OWNER_UPDATED                                  WebMessageInfo_StubType = 207
	WebMessageInfo_COMMUNITY_SUB_GROUP_VISIBILITY_HIDDEN                    WebMessageInfo_StubType = 208
	WebMessageInfo_CAPI_GROUP_NE2EE_SYSTEM_MESSAGE                          WebMessageInfo_StubType = 209
	WebMessageInfo_STATUS_MENTION                                           WebMessageInfo_StubType = 210
	WebMessageInfo_USER_CONTROLS_SYSTEM_MESSAGE                             WebMessageInfo_StubType = 211
	WebMessageInfo_SUPPORT_SYSTEM_MESSAGE                                   WebMessageInfo_StubType = 212
	WebMessageInfo_CHANGE_LID                                               WebMessageInfo_StubType = 213
	WebMessageInfo_BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_IN_MESSAGE             WebMessageInfo_StubType = 214
	WebMessageInfo_BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_OUT_MESSAGE            WebMessageInfo_StubType = 215
	WebMessageInfo_CHANGE_LIMIT_SHARING                                     WebMessageInfo_StubType = 216
	WebMessageInfo_GROUP_MEMBER_LINK_MODE                                   WebMessageInfo_StubType = 217
	WebMessageInfo_BIZ_AUTOMATICALLY_LABELED_CHAT_SYSTEM_MESSAGE            WebMessageInfo_StubType = 218
	WebMessageInfo_PHONE_NUMBER_HIDING_CHAT_DEPRECATED_MESSAGE              WebMessageInfo_StubType = 219
)

// Enum value maps for WebMessageInfo_StubType.
var (
	WebMessageInfo_StubType_name = map[int32]string{
		0:   "UNKNOWN",
		1:   "REVOKE",
		2:   "CIPHERTEXT",
		3:   "FUTUREPROOF",
		4:   "NON_VERIFIED_TRANSITION",
		5:   "UNVERIFIED_TRANSITION",
		6:   "VERIFIED_TRANSITION",
		7:   "VERIFIED_LOW_UNKNOWN",
		8:   "VERIFIED_HIGH",
		9:   "VERIFIED_INITIAL_UNKNOWN",
		10:  "VERIFIED_INITIAL_LOW",
		11:  "VERIFIED_INITIAL_HIGH",
		12:  "VERIFIED_TRANSITION_ANY_TO_NONE",
		13:  "VERIFIED_TRANSITION_ANY_TO_HIGH",
		14:  "VERIFIED_TRANSITION_HIGH_TO_LOW",
		15:  "VERIFIED_TRANSITION_HIGH_TO_UNKNOWN",
		16:  "VERIFIED_TRANSITION_UNKNOWN_TO_LOW",
		17:  "VERIFIED_TRANSITION_LOW_TO_UNKNOWN",
		18:  "VERIFIED_TRANSITION_NONE_TO_LOW",
		19:  "VERIFIED_TRANSITION_NONE_TO_UNKNOWN",
		20:  "GROUP_CREATE",
		21:  "GROUP_CHANGE_SUBJECT",
		22:  "GROUP_CHANGE_ICON",
		23:  "GROUP_CHANGE_INVITE_LINK",
		24:  "GROUP_CHANGE_DESCRIPTION",
		25:  "GROUP_CHANGE_RESTRICT",
		26:  "GROUP_CHANGE_ANNOUNCE",
		27:  "GROUP_PARTICIPANT_ADD",
		28:  "GROUP_PARTICIPANT_REMOVE",
		29:  "GROUP_PARTICIPANT_PROMOTE",
		30:  "GROUP_PARTICIPANT_DEMOTE",
		31:  "GROUP_PARTICIPANT_INVITE",
		32:  "GROUP_PARTICIPANT_LEAVE",
		33:  "GROUP_PARTICIPANT_CHANGE_NUMBER",
		34:  "BROADCAST_CREATE",
		35:  "BROADCAST_ADD",
		36:  "BROADCAST_REMOVE",
		37:  "GENERIC_NOTIFICATION",
		38:  "E2E_IDENTITY_CHANGED",
		39:  "E2E_ENCRYPTED",
		40:  "CALL_MISSED_VOICE",
		41:  "CALL_MISSED_VIDEO",
		42:  "INDIVIDUAL_CHANGE_NUMBER",
		43:  "GROUP_DELETE",
		44:  "GROUP_ANNOUNCE_MODE_MESSAGE_BOUNCE",
		45:  "CALL_MISSED_GROUP_VOICE",
		46:  "CALL_MISSED_GROUP_VIDEO",
		47:  "PAYMENT_CIPHERTEXT",
		48:  "PAYMENT_FUTUREPROOF",
		49:  "PAYMENT_TRANSACTION_STATUS_UPDATE_FAILED",
		50:  "PAYMENT_TRANSACTION_STATUS_UPDATE_REFUNDED",
		51:  "PAYMENT_TRANSACTION_STATUS_UPDATE_REFUND_FAILED",
		52:  "PAYMENT_TRANSACTION_STATUS_RECEIVER_PENDING_SETUP",
		53:  "PAYMENT_TRANSACTION_STATUS_RECEIVER_SUCCESS_AFTER_HICCUP",
		54:  "PAYMENT_ACTION_ACCOUNT_SETUP_REMINDER",
		55:  "PAYMENT_ACTION_SEND_PAYMENT_REMINDER",
		56:  "PAYMENT_ACTION_SEND_PAYMENT_INVITATION",
		57:  "PAYMENT_ACTION_REQUEST_DECLINED",
		58:  "PAYMENT_ACTION_REQUEST_EXPIRED",
		59:  "PAYMENT_ACTION_REQUEST_CANCELLED",
		60:  "BIZ_VERIFIED_TRANSITION_TOP_TO_BOTTOM",
		61:  "BIZ_VERIFIED_TRANSITION_BOTTOM_TO_TOP",
		62:  "BIZ_INTRO_TOP",
		63:  "BIZ_INTRO_BOTTOM",
		64:  "BIZ_NAME_CHANGE",
		65:  "BIZ_MOVE_TO_CONSUMER_APP",
		66:  "BIZ_TWO_TIER_MIGRATION_TOP",
		67:  "BIZ_TWO_TIER_MIGRATION_BOTTOM",
		68:  "OVERSIZED",
		69:  "GROUP_CHANGE_NO_FREQUENTLY_FORWARDED",
		70:  "GROUP_V4_ADD_INVITE_SENT",
		71:  "GROUP_PARTICIPANT_ADD_REQUEST_JOIN",
		72:  "CHANGE_EPHEMERAL_SETTING",
		73:  "E2E_DEVICE_CHANGED",
		74:  "VIEWED_ONCE",
		75:  "E2E_ENCRYPTED_NOW",
		76:  "BLUE_MSG_BSP_FB_TO_BSP_PREMISE",
		77:  "BLUE_MSG_BSP_FB_TO_SELF_FB",
		78:  "BLUE_MSG_BSP_FB_TO_SELF_PREMISE",
		79:  "BLUE_MSG_BSP_FB_UNVERIFIED",
		80:  "BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED",
		81:  "BLUE_MSG_BSP_FB_VERIFIED",
		82:  "BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED",
		83:  "BLUE_MSG_BSP_PREMISE_TO_SELF_PREMISE",
		84:  "BLUE_MSG_BSP_PREMISE_UNVERIFIED",
		85:  "BLUE_MSG_BSP_PREMISE_UNVERIFIED_TO_SELF_PREMISE_VERIFIED",
		86:  "BLUE_MSG_BSP_PREMISE_VERIFIED",
		87:  "BLUE_MSG_BSP_PREMISE_VERIFIED_TO_SELF_PREMISE_UNVERIFIED",
		88:  "BLUE_MSG_CONSUMER_TO_BSP_FB_UNVERIFIED",
		89:  "BLUE_MSG_CONSUMER_TO_BSP_PREMISE_UNVERIFIED",
		90:  "BLUE_MSG_CONSUMER_TO_SELF_FB_UNVERIFIED",
		91:  "BLUE_MSG_CONSUMER_TO_SELF_PREMISE_UNVERIFIED",
		92:  "BLUE_MSG_SELF_FB_TO_BSP_PREMISE",
		93:  "BLUE_MSG_SELF_FB_TO_SELF_PREMISE",
		94:  "BLUE_MSG_SELF_FB_UNVERIFIED",
		95:  "BLUE_MSG_SELF_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED",
		96:  "BLUE_MSG_SELF_FB_VERIFIED",
		97:  "BLUE_MSG_SELF_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED",
		98:  "BLUE_MSG_SELF_PREMISE_TO_BSP_PREMISE",
		99:  "BLUE_MSG_SELF_PREMISE_UNVERIFIED",
		100: "BLUE_MSG_SELF_PREMISE_VERIFIED",
		101: "BLUE_MSG_TO_BSP_FB",
		102: "BLUE_MSG_TO_CONSUMER",
		103: "BLUE_MSG_TO_SELF_FB",
		104: "BLUE_MSG_UNVERIFIED_TO_BSP_FB_VERIFIED",
		105: "BLUE_MSG_UNVERIFIED_TO_BSP_PREMISE_VERIFIED",
		106: "BLUE_MSG_UNVERIFIED_TO_SELF_FB_VERIFIED",
		107: "BLUE_MSG_UNVERIFIED_TO_VERIFIED",
		108: "BLUE_MSG_VERIFIED_TO_BSP_FB_UNVERIFIED",
		109: "BLUE_MSG_VERIFIED_TO_BSP_PREMISE_UNVERIFIED",
		110: "BLUE_MSG_VERIFIED_TO_SELF_FB_UNVERIFIED",
		111: "BLUE_MSG_VERIFIED_TO_UNVERIFIED",
		112: "BLUE_MSG_BSP_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED",
		113: "BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_FB_VERIFIED",
		114: "BLUE_MSG_BSP_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED",
		115: "BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_FB_UNVERIFIED",
		116: "BLUE_MSG_SELF_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED",
		117: "BLUE_MSG_SELF_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED",
		118: "E2E_IDENTITY_UNAVAILABLE",
		119: "GROUP_CREATING",
		120: "GROUP_CREATE_FAILED",
		121: "GROUP_BOUNCED",
		122: "BLOCK_CONTACT",
		123: "EPHEMERAL_SETTING_NOT_APPLIED",
		124: "SYNC_FAILED",
		125: "SYNCING",
		126: "BIZ_PRIVACY_MODE_INIT_FB",
		127: "BIZ_PRIVACY_MODE_INIT_BSP",
		128: "BIZ_PRIVACY_MODE_TO_FB",
		129: "BIZ_PRIVACY_MODE_TO_BSP",
		130: "DISAPPEARING_MODE",
		131: "E2E_DEVICE_FETCH_FAILED",
		132: "ADMIN_REVOKE",
		133: "GROUP_INVITE_LINK_GROWTH_LOCKED",
		134: "COMMUNITY_LINK_PARENT_GROUP",
		135: "COMMUNITY_LINK_SIBLING_GROUP",
		136: "COMMUNITY_LINK_SUB_GROUP",
		137: "COMMUNITY_UNLINK_PARENT_GROUP",
		138: "COMMUNITY_UNLINK_SIBLING_GROUP",
		139: "COMMUNITY_UNLINK_SUB_GROUP",
		140: "GROUP_PARTICIPANT_ACCEPT",
		141: "GROUP_PARTICIPANT_LINKED_GROUP_JOIN",
		142: "COMMUNITY_CREATE",
		143: "EPHEMERAL_KEEP_IN_CHAT",
		144: "GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST",
		145: "GROUP_MEMBERSHIP_JOIN_APPROVAL_MODE",
		146: "INTEGRITY_UNLINK_PARENT_GROUP",
		147: "COMMUNITY_PARTICIPANT_PROMOTE",
		148: "COMMUNITY_PARTICIPANT_DEMOTE",
		149: "COMMUNITY_PARENT_GROUP_DELETED",
		150: "COMMUNITY_LINK_PARENT_GROUP_MEMBERSHIP_APPROVAL",
		151: "GROUP_PARTICIPANT_JOINED_GROUP_AND_PARENT_GROUP",
		152: "MASKED_THREAD_CREATED",
		153: "MASKED_THREAD_UNMASKED",
		154: "BIZ_CHAT_ASSIGNMENT",
		155: "CHAT_PSA",
		156: "CHAT_POLL_CREATION_MESSAGE",
		157: "CAG_MASKED_THREAD_CREATED",
		158: "COMMUNITY_PARENT_GROUP_SUBJECT_CHANGED",
		159: "CAG_INVITE_AUTO_ADD",
		160: "BIZ_CHAT_ASSIGNMENT_UNASSIGN",
		161: "CAG_INVITE_AUTO_JOINED",
		162: "SCHEDULED_CALL_START_MESSAGE",
		163: "COMMUNITY_INVITE_RICH",
		164: "COMMUNITY_INVITE_AUTO_ADD_RICH",
		165: "SUB_GROUP_INVITE_RICH",
		166: "SUB_GROUP_PARTICIPANT_ADD_RICH",
		167: "COMMUNITY_LINK_PARENT_GROUP_RICH",
		168: "COMMUNITY_PARTICIPANT_ADD_RICH",
		169: "SILENCED_UNKNOWN_CALLER_AUDIO",
		170: "SILENCED_UNKNOWN_CALLER_VIDEO",
		171: "GROUP_MEMBER_ADD_MODE",
		172: "GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST_NON_ADMIN_ADD",
		173: "COMMUNITY_CHANGE_DESCRIPTION",
		174: "SENDER_INVITE",
		175: "RECEIVER_INVITE",
		176: "COMMUNITY_ALLOW_MEMBER_ADDED_GROUPS",
		177: "PINNED_MESSAGE_IN_CHAT",
		178: "PAYMENT_INVITE_SETUP_INVITER",
		179: "PAYMENT_INVITE_SETUP_INVITEE_RECEIVE_ONLY",
		180: "PAYMENT_INVITE_SETUP_INVITEE_SEND_AND_RECEIVE",
		181: "LINKED_GROUP_CALL_START",
		182: "REPORT_TO_ADMIN_ENABLED_STATUS",
		183: "EMPTY_SUBGROUP_CREATE",
		184: "SCHEDULED_CALL_CANCEL",
		185: "SUBGROUP_ADMIN_TRIGGERED_AUTO_ADD_RICH",
		186: "GROUP_CHANGE_RECENT_HISTORY_SHARING",
		187: "PAID_MESSAGE_SERVER_CAMPAIGN_ID",
		188: "GENERAL_CHAT_CREATE",
		189: "GENERAL_CHAT_ADD",
		190: "GENERAL_CHAT_AUTO_ADD_DISABLED",
		191: "SUGGESTED_SUBGROUP_ANNOUNCE",
		192: "BIZ_BOT_1P_MESSAGING_ENABLED",
		193: "CHANGE_USERNAME",
		194: "BIZ_COEX_PRIVACY_INIT_SELF",
		195: "BIZ_COEX_PRIVACY_TRANSITION_SELF",
		196: "SUPPORT_AI_EDUCATION",
		197: "BIZ_BOT_3P_MESSAGING_ENABLED",
		198: "REMINDER_SETUP_MESSAGE",
		199: "REMINDER_SENT_MESSAGE",
		200: "REMINDER_CANCEL_MESSAGE",
		201: "BIZ_COEX_PRIVACY_INIT",
		202: "BIZ_COEX_PRIVACY_TRANSITION",
		203: "GROUP_DEACTIVATED",
		204: "COMMUNITY_DEACTIVATE_SIBLING_GROUP",
		205: "EVENT_UPDATED",
		206: "EVENT_CANCELED",
		207: "COMMUNITY_OWNER_UPDATED",
		208: "COMMUNITY_SUB_GROUP_VISIBILITY_HIDDEN",
		209: "CAPI_GROUP_NE2EE_SYSTEM_MESSAGE",
		210: "STATUS_MENTION",
		211: "USER_CONTROLS_SYSTEM_MESSAGE",
		212: "SUPPORT_SYSTEM_MESSAGE",
		213: "CHANGE_LID",
		214: "BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_IN_MESSAGE",
		215: "BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_OUT_MESSAGE",
		216: "CHANGE_LIMIT_SHARING",
		217: "GROUP_MEMBER_LINK_MODE",
		218: "BIZ_AUTOMATICALLY_LABELED_CHAT_SYSTEM_MESSAGE",
		219: "PHONE_NUMBER_HIDING_CHAT_DEPRECATED_MESSAGE",
	}
	WebMessageInfo_StubType_value = map[string]int32{
		"UNKNOWN":                                                  0,
		"REVOKE":                                                   1,
		"CIPHERTEXT":                                               2,
		"FUTUREPROOF":                                              3,
		"NON_VERIFIED_TRANSITION":                                  4,
		"UNVERIFIED_TRANSITION":                                    5,
		"VERIFIED_TRANSITION":                                      6,
		"VERIFIED_LOW_UNKNOWN":                                     7,
		"VERIFIED_HIGH":                                            8,
		"VERIFIED_INITIAL_UNKNOWN":                                 9,
		"VERIFIED_INITIAL_LOW":                                     10,
		"VERIFIED_INITIAL_HIGH":                                    11,
		"VERIFIED_TRANSITION_ANY_TO_NONE":                          12,
		"VERIFIED_TRANSITION_ANY_TO_HIGH":                          13,
		"VERIFIED_TRANSITION_HIGH_TO_LOW":                          14,
		"VERIFIED_TRANSITION_HIGH_TO_UNKNOWN":                      15,
		"VERIFIED_TRANSITION_UNKNOWN_TO_LOW":                       16,
		"VERIFIED_TRANSITION_LOW_TO_UNKNOWN":                       17,
		"VERIFIED_TRANSITION_NONE_TO_LOW":                          18,
		"VERIFIED_TRANSITION_NONE_TO_UNKNOWN":                      19,
		"GROUP_CREATE":                                             20,
		"GROUP_CHANGE_SUBJECT":                                     21,
		"GROUP_CHANGE_ICON":                                        22,
		"GROUP_CHANGE_INVITE_LINK":                                 23,
		"GROUP_CHANGE_DESCRIPTION":                                 24,
		"GROUP_CHANGE_RESTRICT":                                    25,
		"GROUP_CHANGE_ANNOUNCE":                                    26,
		"GROUP_PARTICIPANT_ADD":                                    27,
		"GROUP_PARTICIPANT_REMOVE":                                 28,
		"GROUP_PARTICIPANT_PROMOTE":                                29,
		"GROUP_PARTICIPANT_DEMOTE":                                 30,
		"GROUP_PARTICIPANT_INVITE":                                 31,
		"GROUP_PARTICIPANT_LEAVE":                                  32,
		"GROUP_PARTICIPANT_CHANGE_NUMBER":                          33,
		"BROADCAST_CREATE":                                         34,
		"BROADCAST_ADD":                                            35,
		"BROADCAST_REMOVE":                                         36,
		"GENERIC_NOTIFICATION":                                     37,
		"E2E_IDENTITY_CHANGED":                                     38,
		"E2E_ENCRYPTED":                                            39,
		"CALL_MISSED_VOICE":                                        40,
		"CALL_MISSED_VIDEO":                                        41,
		"INDIVIDUAL_CHANGE_NUMBER":                                 42,
		"GROUP_DELETE":                                             43,
		"GROUP_ANNOUNCE_MODE_MESSAGE_BOUNCE":                       44,
		"CALL_MISSED_GROUP_VOICE":                                  45,
		"CALL_MISSED_GROUP_VIDEO":                                  46,
		"PAYMENT_CIPHERTEXT":                                       47,
		"PAYMENT_FUTUREPROOF":                                      48,
		"PAYMENT_TRANSACTION_STATUS_UPDATE_FAILED":                 49,
		"PAYMENT_TRANSACTION_STATUS_UPDATE_REFUNDED":               50,
		"PAYMENT_TRANSACTION_STATUS_UPDATE_REFUND_FAILED":          51,
		"PAYMENT_TRANSACTION_STATUS_RECEIVER_PENDING_SETUP":        52,
		"PAYMENT_TRANSACTION_STATUS_RECEIVER_SUCCESS_AFTER_HICCUP": 53,
		"PAYMENT_ACTION_ACCOUNT_SETUP_REMINDER":                    54,
		"PAYMENT_ACTION_SEND_PAYMENT_REMINDER":                     55,
		"PAYMENT_ACTION_SEND_PAYMENT_INVITATION":                   56,
		"PAYMENT_ACTION_REQUEST_DECLINED":                          57,
		"PAYMENT_ACTION_REQUEST_EXPIRED":                           58,
		"PAYMENT_ACTION_REQUEST_CANCELLED":                         59,
		"BIZ_VERIFIED_TRANSITION_TOP_TO_BOTTOM":                    60,
		"BIZ_VERIFIED_TRANSITION_BOTTOM_TO_TOP":                    61,
		"BIZ_INTRO_TOP":                                            62,
		"BIZ_INTRO_BOTTOM":                                         63,
		"BIZ_NAME_CHANGE":                                          64,
		"BIZ_MOVE_TO_CONSUMER_APP":                                 65,
		"BIZ_TWO_TIER_MIGRATION_TOP":                               66,
		"BIZ_TWO_TIER_MIGRATION_BOTTOM":                            67,
		"OVERSIZED":                                                68,
		"GROUP_CHANGE_NO_FREQUENTLY_FORWARDED":                     69,
		"GROUP_V4_ADD_INVITE_SENT":                                 70,
		"GROUP_PARTICIPANT_ADD_REQUEST_JOIN":                       71,
		"CHANGE_EPHEMERAL_SETTING":                                 72,
		"E2E_DEVICE_CHANGED":                                       73,
		"VIEWED_ONCE":                                              74,
		"E2E_ENCRYPTED_NOW":                                        75,
		"BLUE_MSG_BSP_FB_TO_BSP_PREMISE":                           76,
		"BLUE_MSG_BSP_FB_TO_SELF_FB":                               77,
		"BLUE_MSG_BSP_FB_TO_SELF_PREMISE":                          78,
		"BLUE_MSG_BSP_FB_UNVERIFIED":                               79,
		"BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED":      80,
		"BLUE_MSG_BSP_FB_VERIFIED":                                 81,
		"BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED":      82,
		"BLUE_MSG_BSP_PREMISE_TO_SELF_PREMISE":                     83,
		"BLUE_MSG_BSP_PREMISE_UNVERIFIED":                          84,
		"BLUE_MSG_BSP_PREMISE_UNVERIFIED_TO_SELF_PREMISE_VERIFIED": 85,
		"BLUE_MSG_BSP_PREMISE_VERIFIED":                            86,
		"BLUE_MSG_BSP_PREMISE_VERIFIED_TO_SELF_PREMISE_UNVERIFIED": 87,
		"BLUE_MSG_CONSUMER_TO_BSP_FB_UNVERIFIED":                   88,
		"BLUE_MSG_CONSUMER_TO_BSP_PREMISE_UNVERIFIED":              89,
		"BLUE_MSG_CONSUMER_TO_SELF_FB_UNVERIFIED":                  90,
		"BLUE_MSG_CONSUMER_TO_SELF_PREMISE_UNVERIFIED":             91,
		"BLUE_MSG_SELF_FB_TO_BSP_PREMISE":                          92,
		"BLUE_MSG_SELF_FB_TO_SELF_PREMISE":                         93,
		"BLUE_MSG_SELF_FB_UNVERIFIED":                              94,
		"BLUE_MSG_SELF_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED":     95,
		"BLUE_MSG_SELF_FB_VERIFIED":                                96,
		"BLUE_MSG_SELF_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED":     97,
		"BLUE_MSG_SELF_PREMISE_TO_BSP_PREMISE":                     98,
		"BLUE_MSG_SELF_PREMISE_UNVERIFIED":                         99,
		"BLUE_MSG_SELF_PREMISE_VERIFIED":                           100,
		"BLUE_MSG_TO_BSP_FB":                                       101,
		"BLUE_MSG_TO_CONSUMER":                                     102,
		"BLUE_MSG_TO_SELF_FB":                                      103,
		"BLUE_MSG_UNVERIFIED_TO_BSP_FB_VERIFIED":                   104,
		"BLUE_MSG_UNVERIFIED_TO_BSP_PREMISE_VERIFIED":              105,
		"BLUE_MSG_UNVERIFIED_TO_SELF_FB_VERIFIED":                  106,
		"BLUE_MSG_UNVERIFIED_TO_VERIFIED":                          107,
		"BLUE_MSG_VERIFIED_TO_BSP_FB_UNVERIFIED":                   108,
		"BLUE_MSG_VERIFIED_TO_BSP_PREMISE_UNVERIFIED":              109,
		"BLUE_MSG_VERIFIED_TO_SELF_FB_UNVERIFIED":                  110,
		"BLUE_MSG_VERIFIED_TO_UNVERIFIED":                          111,
		"BLUE_MSG_BSP_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED":       112,
		"BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_FB_VERIFIED":           113,
		"BLUE_MSG_BSP_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED":       114,
		"BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_FB_UNVERIFIED":           115,
		"BLUE_MSG_SELF_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED":      116,
		"BLUE_MSG_SELF_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED":      117,
		"E2E_IDENTITY_UNAVAILABLE":                                 118,
		"GROUP_CREATING":                                           119,
		"GROUP_CREATE_FAILED":                                      120,
		"GROUP_BOUNCED":                                            121,
		"BLOCK_CONTACT":                                            122,
		"EPHEMERAL_SETTING_NOT_APPLIED":                            123,
		"SYNC_FAILED":                                              124,
		"SYNCING":                                                  125,
		"BIZ_PRIVACY_MODE_INIT_FB":                                 126,
		"BIZ_PRIVACY_MODE_INIT_BSP":                                127,
		"BIZ_PRIVACY_MODE_TO_FB":                                   128,
		"BIZ_PRIVACY_MODE_TO_BSP":                                  129,
		"DISAPPEARING_MODE":                                        130,
		"E2E_DEVICE_FETCH_FAILED":                                  131,
		"ADMIN_REVOKE":                                             132,
		"GROUP_INVITE_LINK_GROWTH_LOCKED":                          133,
		"COMMUNITY_LINK_PARENT_GROUP":                              134,
		"COMMUNITY_LINK_SIBLING_GROUP":                             135,
		"COMMUNITY_LINK_SUB_GROUP":                                 136,
		"COMMUNITY_UNLINK_PARENT_GROUP":                            137,
		"COMMUNITY_UNLINK_SIBLING_GROUP":                           138,
		"COMMUNITY_UNLINK_SUB_GROUP":                               139,
		"GROUP_PARTICIPANT_ACCEPT":                                 140,
		"GROUP_PARTICIPANT_LINKED_GROUP_JOIN":                      141,
		"COMMUNITY_CREATE":                                         142,
		"EPHEMERAL_KEEP_IN_CHAT":                                   143,
		"GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST":                   144,
		"GROUP_MEMBERSHIP_JOIN_APPROVAL_MODE":                      145,
		"INTEGRITY_UNLINK_PARENT_GROUP":                            146,
		"COMMUNITY_PARTICIPANT_PROMOTE":                            147,
		"COMMUNITY_PARTICIPANT_DEMOTE":                             148,
		"COMMUNITY_PARENT_GROUP_DELETED":                           149,
		"COMMUNITY_LINK_PARENT_GROUP_MEMBERSHIP_APPROVAL":          150,
		"GROUP_PARTICIPANT_JOINED_GROUP_AND_PARENT_GROUP":          151,
		"MASKED_THREAD_CREATED":                                    152,
		"MASKED_THREAD_UNMASKED":                                   153,
		"BIZ_CHAT_ASSIGNMENT":                                      154,
		"CHAT_PSA":                                                 155,
		"CHAT_POLL_CREATION_MESSAGE":                               156,
		"CAG_MASKED_THREAD_CREATED":                                157,
		"COMMUNITY_PARENT_GROUP_SUBJECT_CHANGED":                   158,
		"CAG_INVITE_AUTO_ADD":                                      159,
		"BIZ_CHAT_ASSIGNMENT_UNASSIGN":                             160,
		"CAG_INVITE_AUTO_JOINED":                                   161,
		"SCHEDULED_CALL_START_MESSAGE":                             162,
		"COMMUNITY_INVITE_RICH":                                    163,
		"COMMUNITY_INVITE_AUTO_ADD_RICH":                           164,
		"SUB_GROUP_INVITE_RICH":                                    165,
		"SUB_GROUP_PARTICIPANT_ADD_RICH":                           166,
		"COMMUNITY_LINK_PARENT_GROUP_RICH":                         167,
		"COMMUNITY_PARTICIPANT_ADD_RICH":                           168,
		"SILENCED_UNKNOWN_CALLER_AUDIO":                            169,
		"SILENCED_UNKNOWN_CALLER_VIDEO":                            170,
		"GROUP_MEMBER_ADD_MODE":                                    171,
		"GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST_NON_ADMIN_ADD":     172,
		"COMMUNITY_CHANGE_DESCRIPTION":                             173,
		"SENDER_INVITE":                                            174,
		"RECEIVER_INVITE":                                          175,
		"COMMUNITY_ALLOW_MEMBER_ADDED_GROUPS":                      176,
		"PINNED_MESSAGE_IN_CHAT":                                   177,
		"PAYMENT_INVITE_SETUP_INVITER":                             178,
		"PAYMENT_INVITE_SETUP_INVITEE_RECEIVE_ONLY":                179,
		"PAYMENT_INVITE_SETUP_INVITEE_SEND_AND_RECEIVE":            180,
		"LINKED_GROUP_CALL_START":                                  181,
		"REPORT_TO_ADMIN_ENABLED_STATUS":                           182,
		"EMPTY_SUBGROUP_CREATE":                                    183,
		"SCHEDULED_CALL_CANCEL":                                    184,
		"SUBGROUP_ADMIN_TRIGGERED_AUTO_ADD_RICH":                   185,
		"GROUP_CHANGE_RECENT_HISTORY_SHARING":                      186,
		"PAID_MESSAGE_SERVER_CAMPAIGN_ID":                          187,
		"GENERAL_CHAT_CREATE":                                      188,
		"GENERAL_CHAT_ADD":                                         189,
		"GENERAL_CHAT_AUTO_ADD_DISABLED":                           190,
		"SUGGESTED_SUBGROUP_ANNOUNCE":                              191,
		"BIZ_BOT_1P_MESSAGING_ENABLED":                             192,
		"CHANGE_USERNAME":                                          193,
		"BIZ_COEX_PRIVACY_INIT_SELF":                               194,
		"BIZ_COEX_PRIVACY_TRANSITION_SELF":                         195,
		"SUPPORT_AI_EDUCATION":                                     196,
		"BIZ_BOT_3P_MESSAGING_ENABLED":                             197,
		"REMINDER_SETUP_MESSAGE":                                   198,
		"REMINDER_SENT_MESSAGE":                                    199,
		"REMINDER_CANCEL_MESSAGE":                                  200,
		"BIZ_COEX_PRIVACY_INIT":                                    201,
		"BIZ_COEX_PRIVACY_TRANSITION":                              202,
		"GROUP_DEACTIVATED":                                        203,
		"COMMUNITY_DEACTIVATE_SIBLING_GROUP":                       204,
		"EVENT_UPDATED":                                            205,
		"EVENT_CANCELED":                                           206,
		"COMMUNITY_OWNER_UPDATED":                                  207,
		"COMMUNITY_SUB_GROUP_VISIBILITY_HIDDEN":                    208,
		"CAPI_GROUP_NE2EE_SYSTEM_MESSAGE":                          209,
		"STATUS_MENTION":                                           210,
		"USER_CONTROLS_SYSTEM_MESSAGE":                             211,
		"SUPPORT_SYSTEM_MESSAGE":                                   212,
		"CHANGE_LID":                                               213,
		"BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_IN_MESSAGE":             214,
		"BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_OUT_MESSAGE":            215,
		"CHANGE_LIMIT_SHARING":                                     216,
		"GROUP_MEMBER_LINK_MODE":                                   217,
		"BIZ_AUTOMATICALLY_LABELED_CHAT_SYSTEM_MESSAGE":            218,
		"PHONE_NUMBER_HIDING_CHAT_DEPRECATED_MESSAGE":              219,
	}
)

func (x WebMessageInfo_StubType) Enum() *WebMessageInfo_StubType {
	p := new(WebMessageInfo_StubType)
	*p = x
	return p
}

func (x WebMessageInfo_StubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WebMessageInfo_StubType) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[1].Descriptor()
}

func (WebMessageInfo_StubType) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[1]
}

func (x WebMessageInfo_StubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *WebMessageInfo_StubType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = WebMessageInfo_StubType(num)
	return nil
}

// Deprecated: Use WebMessageInfo_StubType.Descriptor instead.
func (WebMessageInfo_StubType) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{0, 1}
}

type WebMessageInfo_Status int32

const (
	WebMessageInfo_ERROR        WebMessageInfo_Status = 0
	WebMessageInfo_PENDING      WebMessageInfo_Status = 1
	WebMessageInfo_SERVER_ACK   WebMessageInfo_Status = 2
	WebMessageInfo_DELIVERY_ACK WebMessageInfo_Status = 3
	WebMessageInfo_READ         WebMessageInfo_Status = 4
	WebMessageInfo_PLAYED       WebMessageInfo_Status = 5
)

// Enum value maps for WebMessageInfo_Status.
var (
	WebMessageInfo_Status_name = map[int32]string{
		0: "ERROR",
		1: "PENDING",
		2: "SERVER_ACK",
		3: "DELIVERY_ACK",
		4: "READ",
		5: "PLAYED",
	}
	WebMessageInfo_Status_value = map[string]int32{
		"ERROR":        0,
		"PENDING":      1,
		"SERVER_ACK":   2,
		"DELIVERY_ACK": 3,
		"READ":         4,
		"PLAYED":       5,
	}
)

func (x WebMessageInfo_Status) Enum() *WebMessageInfo_Status {
	p := new(WebMessageInfo_Status)
	*p = x
	return p
}

func (x WebMessageInfo_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WebMessageInfo_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[2].Descriptor()
}

func (WebMessageInfo_Status) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[2]
}

func (x WebMessageInfo_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *WebMessageInfo_Status) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = WebMessageInfo_Status(num)
	return nil
}

// Deprecated: Use WebMessageInfo_Status.Descriptor instead.
func (WebMessageInfo_Status) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{0, 2}
}

type PaymentInfo_TxnStatus int32

const (
	PaymentInfo_UNKNOWN                       PaymentInfo_TxnStatus = 0
	PaymentInfo_PENDING_SETUP                 PaymentInfo_TxnStatus = 1
	PaymentInfo_PENDING_RECEIVER_SETUP        PaymentInfo_TxnStatus = 2
	PaymentInfo_INIT                          PaymentInfo_TxnStatus = 3
	PaymentInfo_SUCCESS                       PaymentInfo_TxnStatus = 4
	PaymentInfo_COMPLETED                     PaymentInfo_TxnStatus = 5
	PaymentInfo_FAILED                        PaymentInfo_TxnStatus = 6
	PaymentInfo_FAILED_RISK                   PaymentInfo_TxnStatus = 7
	PaymentInfo_FAILED_PROCESSING             PaymentInfo_TxnStatus = 8
	PaymentInfo_FAILED_RECEIVER_PROCESSING    PaymentInfo_TxnStatus = 9
	PaymentInfo_FAILED_DA                     PaymentInfo_TxnStatus = 10
	PaymentInfo_FAILED_DA_FINAL               PaymentInfo_TxnStatus = 11
	PaymentInfo_REFUNDED_TXN                  PaymentInfo_TxnStatus = 12
	PaymentInfo_REFUND_FAILED                 PaymentInfo_TxnStatus = 13
	PaymentInfo_REFUND_FAILED_PROCESSING      PaymentInfo_TxnStatus = 14
	PaymentInfo_REFUND_FAILED_DA              PaymentInfo_TxnStatus = 15
	PaymentInfo_EXPIRED_TXN                   PaymentInfo_TxnStatus = 16
	PaymentInfo_AUTH_CANCELED                 PaymentInfo_TxnStatus = 17
	PaymentInfo_AUTH_CANCEL_FAILED_PROCESSING PaymentInfo_TxnStatus = 18
	PaymentInfo_AUTH_CANCEL_FAILED            PaymentInfo_TxnStatus = 19
	PaymentInfo_COLLECT_INIT                  PaymentInfo_TxnStatus = 20
	PaymentInfo_COLLECT_SUCCESS               PaymentInfo_TxnStatus = 21
	PaymentInfo_COLLECT_FAILED                PaymentInfo_TxnStatus = 22
	PaymentInfo_COLLECT_FAILED_RISK           PaymentInfo_TxnStatus = 23
	PaymentInfo_COLLECT_REJECTED              PaymentInfo_TxnStatus = 24
	PaymentInfo_COLLECT_EXPIRED               PaymentInfo_TxnStatus = 25
	PaymentInfo_COLLECT_CANCELED              PaymentInfo_TxnStatus = 26
	PaymentInfo_COLLECT_CANCELLING            PaymentInfo_TxnStatus = 27
	PaymentInfo_IN_REVIEW                     PaymentInfo_TxnStatus = 28
	PaymentInfo_REVERSAL_SUCCESS              PaymentInfo_TxnStatus = 29
	PaymentInfo_REVERSAL_PENDING              PaymentInfo_TxnStatus = 30
	PaymentInfo_REFUND_PENDING                PaymentInfo_TxnStatus = 31
)

// Enum value maps for PaymentInfo_TxnStatus.
var (
	PaymentInfo_TxnStatus_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "PENDING_SETUP",
		2:  "PENDING_RECEIVER_SETUP",
		3:  "INIT",
		4:  "SUCCESS",
		5:  "COMPLETED",
		6:  "FAILED",
		7:  "FAILED_RISK",
		8:  "FAILED_PROCESSING",
		9:  "FAILED_RECEIVER_PROCESSING",
		10: "FAILED_DA",
		11: "FAILED_DA_FINAL",
		12: "REFUNDED_TXN",
		13: "REFUND_FAILED",
		14: "REFUND_FAILED_PROCESSING",
		15: "REFUND_FAILED_DA",
		16: "EXPIRED_TXN",
		17: "AUTH_CANCELED",
		18: "AUTH_CANCEL_FAILED_PROCESSING",
		19: "AUTH_CANCEL_FAILED",
		20: "COLLECT_INIT",
		21: "COLLECT_SUCCESS",
		22: "COLLECT_FAILED",
		23: "COLLECT_FAILED_RISK",
		24: "COLLECT_REJECTED",
		25: "COLLECT_EXPIRED",
		26: "COLLECT_CANCELED",
		27: "COLLECT_CANCELLING",
		28: "IN_REVIEW",
		29: "REVERSAL_SUCCESS",
		30: "REVERSAL_PENDING",
		31: "REFUND_PENDING",
	}
	PaymentInfo_TxnStatus_value = map[string]int32{
		"UNKNOWN":                       0,
		"PENDING_SETUP":                 1,
		"PENDING_RECEIVER_SETUP":        2,
		"INIT":                          3,
		"SUCCESS":                       4,
		"COMPLETED":                     5,
		"FAILED":                        6,
		"FAILED_RISK":                   7,
		"FAILED_PROCESSING":             8,
		"FAILED_RECEIVER_PROCESSING":    9,
		"FAILED_DA":                     10,
		"FAILED_DA_FINAL":               11,
		"REFUNDED_TXN":                  12,
		"REFUND_FAILED":                 13,
		"REFUND_FAILED_PROCESSING":      14,
		"REFUND_FAILED_DA":              15,
		"EXPIRED_TXN":                   16,
		"AUTH_CANCELED":                 17,
		"AUTH_CANCEL_FAILED_PROCESSING": 18,
		"AUTH_CANCEL_FAILED":            19,
		"COLLECT_INIT":                  20,
		"COLLECT_SUCCESS":               21,
		"COLLECT_FAILED":                22,
		"COLLECT_FAILED_RISK":           23,
		"COLLECT_REJECTED":              24,
		"COLLECT_EXPIRED":               25,
		"COLLECT_CANCELED":              26,
		"COLLECT_CANCELLING":            27,
		"IN_REVIEW":                     28,
		"REVERSAL_SUCCESS":              29,
		"REVERSAL_PENDING":              30,
		"REFUND_PENDING":                31,
	}
)

func (x PaymentInfo_TxnStatus) Enum() *PaymentInfo_TxnStatus {
	p := new(PaymentInfo_TxnStatus)
	*p = x
	return p
}

func (x PaymentInfo_TxnStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentInfo_TxnStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[3].Descriptor()
}

func (PaymentInfo_TxnStatus) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[3]
}

func (x PaymentInfo_TxnStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PaymentInfo_TxnStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PaymentInfo_TxnStatus(num)
	return nil
}

// Deprecated: Use PaymentInfo_TxnStatus.Descriptor instead.
func (PaymentInfo_TxnStatus) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{1, 0}
}

type PaymentInfo_Status int32

const (
	PaymentInfo_UNKNOWN_STATUS     PaymentInfo_Status = 0
	PaymentInfo_PROCESSING         PaymentInfo_Status = 1
	PaymentInfo_SENT               PaymentInfo_Status = 2
	PaymentInfo_NEED_TO_ACCEPT     PaymentInfo_Status = 3
	PaymentInfo_COMPLETE           PaymentInfo_Status = 4
	PaymentInfo_COULD_NOT_COMPLETE PaymentInfo_Status = 5
	PaymentInfo_REFUNDED           PaymentInfo_Status = 6
	PaymentInfo_EXPIRED            PaymentInfo_Status = 7
	PaymentInfo_REJECTED           PaymentInfo_Status = 8
	PaymentInfo_CANCELLED          PaymentInfo_Status = 9
	PaymentInfo_WAITING_FOR_PAYER  PaymentInfo_Status = 10
	PaymentInfo_WAITING            PaymentInfo_Status = 11
)

// Enum value maps for PaymentInfo_Status.
var (
	PaymentInfo_Status_name = map[int32]string{
		0:  "UNKNOWN_STATUS",
		1:  "PROCESSING",
		2:  "SENT",
		3:  "NEED_TO_ACCEPT",
		4:  "COMPLETE",
		5:  "COULD_NOT_COMPLETE",
		6:  "REFUNDED",
		7:  "EXPIRED",
		8:  "REJECTED",
		9:  "CANCELLED",
		10: "WAITING_FOR_PAYER",
		11: "WAITING",
	}
	PaymentInfo_Status_value = map[string]int32{
		"UNKNOWN_STATUS":     0,
		"PROCESSING":         1,
		"SENT":               2,
		"NEED_TO_ACCEPT":     3,
		"COMPLETE":           4,
		"COULD_NOT_COMPLETE": 5,
		"REFUNDED":           6,
		"EXPIRED":            7,
		"REJECTED":           8,
		"CANCELLED":          9,
		"WAITING_FOR_PAYER":  10,
		"WAITING":            11,
	}
)

func (x PaymentInfo_Status) Enum() *PaymentInfo_Status {
	p := new(PaymentInfo_Status)
	*p = x
	return p
}

func (x PaymentInfo_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentInfo_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[4].Descriptor()
}

func (PaymentInfo_Status) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[4]
}

func (x PaymentInfo_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PaymentInfo_Status) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PaymentInfo_Status(num)
	return nil
}

// Deprecated: Use PaymentInfo_Status.Descriptor instead.
func (PaymentInfo_Status) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{1, 1}
}

type PaymentInfo_Currency int32

const (
	PaymentInfo_UNKNOWN_CURRENCY PaymentInfo_Currency = 0
	PaymentInfo_INR              PaymentInfo_Currency = 1
)

// Enum value maps for PaymentInfo_Currency.
var (
	PaymentInfo_Currency_name = map[int32]string{
		0: "UNKNOWN_CURRENCY",
		1: "INR",
	}
	PaymentInfo_Currency_value = map[string]int32{
		"UNKNOWN_CURRENCY": 0,
		"INR":              1,
	}
)

func (x PaymentInfo_Currency) Enum() *PaymentInfo_Currency {
	p := new(PaymentInfo_Currency)
	*p = x
	return p
}

func (x PaymentInfo_Currency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentInfo_Currency) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[5].Descriptor()
}

func (PaymentInfo_Currency) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[5]
}

func (x PaymentInfo_Currency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PaymentInfo_Currency) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PaymentInfo_Currency(num)
	return nil
}

// Deprecated: Use PaymentInfo_Currency.Descriptor instead.
func (PaymentInfo_Currency) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{1, 2}
}

type WebFeatures_Flag int32

const (
	WebFeatures_NOT_STARTED   WebFeatures_Flag = 0
	WebFeatures_FORCE_UPGRADE WebFeatures_Flag = 1
	WebFeatures_DEVELOPMENT   WebFeatures_Flag = 2
	WebFeatures_PRODUCTION    WebFeatures_Flag = 3
)

// Enum value maps for WebFeatures_Flag.
var (
	WebFeatures_Flag_name = map[int32]string{
		0: "NOT_STARTED",
		1: "FORCE_UPGRADE",
		2: "DEVELOPMENT",
		3: "PRODUCTION",
	}
	WebFeatures_Flag_value = map[string]int32{
		"NOT_STARTED":   0,
		"FORCE_UPGRADE": 1,
		"DEVELOPMENT":   2,
		"PRODUCTION":    3,
	}
)

func (x WebFeatures_Flag) Enum() *WebFeatures_Flag {
	p := new(WebFeatures_Flag)
	*p = x
	return p
}

func (x WebFeatures_Flag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WebFeatures_Flag) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[6].Descriptor()
}

func (WebFeatures_Flag) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[6]
}

func (x WebFeatures_Flag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *WebFeatures_Flag) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = WebFeatures_Flag(num)
	return nil
}

// Deprecated: Use WebFeatures_Flag.Descriptor instead.
func (WebFeatures_Flag) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{2, 0}
}

type PinInChat_Type int32

const (
	PinInChat_UNKNOWN_TYPE  PinInChat_Type = 0
	PinInChat_PIN_FOR_ALL   PinInChat_Type = 1
	PinInChat_UNPIN_FOR_ALL PinInChat_Type = 2
)

// Enum value maps for PinInChat_Type.
var (
	PinInChat_Type_name = map[int32]string{
		0: "UNKNOWN_TYPE",
		1: "PIN_FOR_ALL",
		2: "UNPIN_FOR_ALL",
	}
	PinInChat_Type_value = map[string]int32{
		"UNKNOWN_TYPE":  0,
		"PIN_FOR_ALL":   1,
		"UNPIN_FOR_ALL": 2,
	}
)

func (x PinInChat_Type) Enum() *PinInChat_Type {
	p := new(PinInChat_Type)
	*p = x
	return p
}

func (x PinInChat_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PinInChat_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[7].Descriptor()
}

func (PinInChat_Type) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[7]
}

func (x PinInChat_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PinInChat_Type) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PinInChat_Type(num)
	return nil
}

// Deprecated: Use PinInChat_Type.Descriptor instead.
func (PinInChat_Type) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{3, 0}
}

type MessageAddOn_MessageAddOnType int32

const (
	MessageAddOn_UNDEFINED      MessageAddOn_MessageAddOnType = 0
	MessageAddOn_REACTION       MessageAddOn_MessageAddOnType = 1
	MessageAddOn_EVENT_RESPONSE MessageAddOn_MessageAddOnType = 2
	MessageAddOn_POLL_UPDATE    MessageAddOn_MessageAddOnType = 3
	MessageAddOn_PIN_IN_CHAT    MessageAddOn_MessageAddOnType = 4
)

// Enum value maps for MessageAddOn_MessageAddOnType.
var (
	MessageAddOn_MessageAddOnType_name = map[int32]string{
		0: "UNDEFINED",
		1: "REACTION",
		2: "EVENT_RESPONSE",
		3: "POLL_UPDATE",
		4: "PIN_IN_CHAT",
	}
	MessageAddOn_MessageAddOnType_value = map[string]int32{
		"UNDEFINED":      0,
		"REACTION":       1,
		"EVENT_RESPONSE": 2,
		"POLL_UPDATE":    3,
		"PIN_IN_CHAT":    4,
	}
)

func (x MessageAddOn_MessageAddOnType) Enum() *MessageAddOn_MessageAddOnType {
	p := new(MessageAddOn_MessageAddOnType)
	*p = x
	return p
}

func (x MessageAddOn_MessageAddOnType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageAddOn_MessageAddOnType) Descriptor() protoreflect.EnumDescriptor {
	return file_waWeb_WAWebProtobufsWeb_proto_enumTypes[8].Descriptor()
}

func (MessageAddOn_MessageAddOnType) Type() protoreflect.EnumType {
	return &file_waWeb_WAWebProtobufsWeb_proto_enumTypes[8]
}

func (x MessageAddOn_MessageAddOnType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MessageAddOn_MessageAddOnType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MessageAddOn_MessageAddOnType(num)
	return nil
}

// Deprecated: Use MessageAddOn_MessageAddOnType.Descriptor instead.
func (MessageAddOn_MessageAddOnType) EnumDescriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{4, 0}
}

type WebMessageInfo struct {
	state                           protoimpl.MessageState           `protogen:"open.v1"`
	Key                             *waCommon.MessageKey             `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Message                         *waE2E.Message                   `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	MessageTimestamp                *uint64                          `protobuf:"varint,3,opt,name=messageTimestamp" json:"messageTimestamp,omitempty"`
	Status                          *WebMessageInfo_Status           `protobuf:"varint,4,opt,name=status,enum=WAWebProtobufsWeb.WebMessageInfo_Status" json:"status,omitempty"`
	Participant                     *string                          `protobuf:"bytes,5,opt,name=participant" json:"participant,omitempty"`
	MessageC2STimestamp             *uint64                          `protobuf:"varint,6,opt,name=messageC2STimestamp" json:"messageC2STimestamp,omitempty"`
	Ignore                          *bool                            `protobuf:"varint,16,opt,name=ignore" json:"ignore,omitempty"`
	Starred                         *bool                            `protobuf:"varint,17,opt,name=starred" json:"starred,omitempty"`
	Broadcast                       *bool                            `protobuf:"varint,18,opt,name=broadcast" json:"broadcast,omitempty"`
	PushName                        *string                          `protobuf:"bytes,19,opt,name=pushName" json:"pushName,omitempty"`
	MediaCiphertextSHA256           []byte                           `protobuf:"bytes,20,opt,name=mediaCiphertextSHA256" json:"mediaCiphertextSHA256,omitempty"`
	Multicast                       *bool                            `protobuf:"varint,21,opt,name=multicast" json:"multicast,omitempty"`
	UrlText                         *bool                            `protobuf:"varint,22,opt,name=urlText" json:"urlText,omitempty"`
	UrlNumber                       *bool                            `protobuf:"varint,23,opt,name=urlNumber" json:"urlNumber,omitempty"`
	MessageStubType                 *WebMessageInfo_StubType         `protobuf:"varint,24,opt,name=messageStubType,enum=WAWebProtobufsWeb.WebMessageInfo_StubType" json:"messageStubType,omitempty"`
	ClearMedia                      *bool                            `protobuf:"varint,25,opt,name=clearMedia" json:"clearMedia,omitempty"`
	MessageStubParameters           []string                         `protobuf:"bytes,26,rep,name=messageStubParameters" json:"messageStubParameters,omitempty"`
	Duration                        *uint32                          `protobuf:"varint,27,opt,name=duration" json:"duration,omitempty"`
	Labels                          []string                         `protobuf:"bytes,28,rep,name=labels" json:"labels,omitempty"`
	PaymentInfo                     *PaymentInfo                     `protobuf:"bytes,29,opt,name=paymentInfo" json:"paymentInfo,omitempty"`
	FinalLiveLocation               *waE2E.LiveLocationMessage       `protobuf:"bytes,30,opt,name=finalLiveLocation" json:"finalLiveLocation,omitempty"`
	QuotedPaymentInfo               *PaymentInfo                     `protobuf:"bytes,31,opt,name=quotedPaymentInfo" json:"quotedPaymentInfo,omitempty"`
	EphemeralStartTimestamp         *uint64                          `protobuf:"varint,32,opt,name=ephemeralStartTimestamp" json:"ephemeralStartTimestamp,omitempty"`
	EphemeralDuration               *uint32                          `protobuf:"varint,33,opt,name=ephemeralDuration" json:"ephemeralDuration,omitempty"`
	EphemeralOffToOn                *bool                            `protobuf:"varint,34,opt,name=ephemeralOffToOn" json:"ephemeralOffToOn,omitempty"`
	EphemeralOutOfSync              *bool                            `protobuf:"varint,35,opt,name=ephemeralOutOfSync" json:"ephemeralOutOfSync,omitempty"`
	BizPrivacyStatus                *WebMessageInfo_BizPrivacyStatus `protobuf:"varint,36,opt,name=bizPrivacyStatus,enum=WAWebProtobufsWeb.WebMessageInfo_BizPrivacyStatus" json:"bizPrivacyStatus,omitempty"`
	VerifiedBizName                 *string                          `protobuf:"bytes,37,opt,name=verifiedBizName" json:"verifiedBizName,omitempty"`
	MediaData                       *MediaData                       `protobuf:"bytes,38,opt,name=mediaData" json:"mediaData,omitempty"`
	PhotoChange                     *PhotoChange                     `protobuf:"bytes,39,opt,name=photoChange" json:"photoChange,omitempty"`
	UserReceipt                     []*UserReceipt                   `protobuf:"bytes,40,rep,name=userReceipt" json:"userReceipt,omitempty"`
	Reactions                       []*Reaction                      `protobuf:"bytes,41,rep,name=reactions" json:"reactions,omitempty"`
	QuotedStickerData               *MediaData                       `protobuf:"bytes,42,opt,name=quotedStickerData" json:"quotedStickerData,omitempty"`
	FutureproofData                 []byte                           `protobuf:"bytes,43,opt,name=futureproofData" json:"futureproofData,omitempty"`
	StatusPsa                       *StatusPSA                       `protobuf:"bytes,44,opt,name=statusPsa" json:"statusPsa,omitempty"`
	PollUpdates                     []*PollUpdate                    `protobuf:"bytes,45,rep,name=pollUpdates" json:"pollUpdates,omitempty"`
	PollAdditionalMetadata          *PollAdditionalMetadata          `protobuf:"bytes,46,opt,name=pollAdditionalMetadata" json:"pollAdditionalMetadata,omitempty"`
	AgentID                         *string                          `protobuf:"bytes,47,opt,name=agentID" json:"agentID,omitempty"`
	StatusAlreadyViewed             *bool                            `protobuf:"varint,48,opt,name=statusAlreadyViewed" json:"statusAlreadyViewed,omitempty"`
	MessageSecret                   []byte                           `protobuf:"bytes,49,opt,name=messageSecret" json:"messageSecret,omitempty"`
	KeepInChat                      *KeepInChat                      `protobuf:"bytes,50,opt,name=keepInChat" json:"keepInChat,omitempty"`
	OriginalSelfAuthorUserJIDString *string                          `protobuf:"bytes,51,opt,name=originalSelfAuthorUserJIDString" json:"originalSelfAuthorUserJIDString,omitempty"`
	RevokeMessageTimestamp          *uint64                          `protobuf:"varint,52,opt,name=revokeMessageTimestamp" json:"revokeMessageTimestamp,omitempty"`
	PinInChat                       *PinInChat                       `protobuf:"bytes,54,opt,name=pinInChat" json:"pinInChat,omitempty"`
	PremiumMessageInfo              *PremiumMessageInfo              `protobuf:"bytes,55,opt,name=premiumMessageInfo" json:"premiumMessageInfo,omitempty"`
	Is1PBizBotMessage               *bool                            `protobuf:"varint,56,opt,name=is1PBizBotMessage" json:"is1PBizBotMessage,omitempty"`
	IsGroupHistoryMessage           *bool                            `protobuf:"varint,57,opt,name=isGroupHistoryMessage" json:"isGroupHistoryMessage,omitempty"`
	BotMessageInvokerJID            *string                          `protobuf:"bytes,58,opt,name=botMessageInvokerJID" json:"botMessageInvokerJID,omitempty"`
	CommentMetadata                 *CommentMetadata                 `protobuf:"bytes,59,opt,name=commentMetadata" json:"commentMetadata,omitempty"`
	EventResponses                  []*EventResponse                 `protobuf:"bytes,61,rep,name=eventResponses" json:"eventResponses,omitempty"`
	ReportingTokenInfo              *ReportingTokenInfo              `protobuf:"bytes,62,opt,name=reportingTokenInfo" json:"reportingTokenInfo,omitempty"`
	NewsletterServerID              *uint64                          `protobuf:"varint,63,opt,name=newsletterServerID" json:"newsletterServerID,omitempty"`
	EventAdditionalMetadata         *EventAdditionalMetadata         `protobuf:"bytes,64,opt,name=eventAdditionalMetadata" json:"eventAdditionalMetadata,omitempty"`
	IsMentionedInStatus             *bool                            `protobuf:"varint,65,opt,name=isMentionedInStatus" json:"isMentionedInStatus,omitempty"`
	StatusMentions                  []string                         `protobuf:"bytes,66,rep,name=statusMentions" json:"statusMentions,omitempty"`
	TargetMessageID                 *waCommon.MessageKey             `protobuf:"bytes,67,opt,name=targetMessageID" json:"targetMessageID,omitempty"`
	MessageAddOns                   []*MessageAddOn                  `protobuf:"bytes,68,rep,name=messageAddOns" json:"messageAddOns,omitempty"`
	StatusMentionMessageInfo        *StatusMentionMessage            `protobuf:"bytes,69,opt,name=statusMentionMessageInfo" json:"statusMentionMessageInfo,omitempty"`
	IsSupportAiMessage              *bool                            `protobuf:"varint,70,opt,name=isSupportAiMessage" json:"isSupportAiMessage,omitempty"`
	StatusMentionSources            []string                         `protobuf:"bytes,71,rep,name=statusMentionSources" json:"statusMentionSources,omitempty"`
	SupportAiCitations              []*Citation                      `protobuf:"bytes,72,rep,name=supportAiCitations" json:"supportAiCitations,omitempty"`
	BotTargetID                     *string                          `protobuf:"bytes,73,opt,name=botTargetID" json:"botTargetID,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *WebMessageInfo) Reset() {
	*x = WebMessageInfo{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebMessageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebMessageInfo) ProtoMessage() {}

func (x *WebMessageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebMessageInfo.ProtoReflect.Descriptor instead.
func (*WebMessageInfo) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{0}
}

func (x *WebMessageInfo) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *WebMessageInfo) GetMessage() *waE2E.Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *WebMessageInfo) GetMessageTimestamp() uint64 {
	if x != nil && x.MessageTimestamp != nil {
		return *x.MessageTimestamp
	}
	return 0
}

func (x *WebMessageInfo) GetStatus() WebMessageInfo_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return WebMessageInfo_ERROR
}

func (x *WebMessageInfo) GetParticipant() string {
	if x != nil && x.Participant != nil {
		return *x.Participant
	}
	return ""
}

func (x *WebMessageInfo) GetMessageC2STimestamp() uint64 {
	if x != nil && x.MessageC2STimestamp != nil {
		return *x.MessageC2STimestamp
	}
	return 0
}

func (x *WebMessageInfo) GetIgnore() bool {
	if x != nil && x.Ignore != nil {
		return *x.Ignore
	}
	return false
}

func (x *WebMessageInfo) GetStarred() bool {
	if x != nil && x.Starred != nil {
		return *x.Starred
	}
	return false
}

func (x *WebMessageInfo) GetBroadcast() bool {
	if x != nil && x.Broadcast != nil {
		return *x.Broadcast
	}
	return false
}

func (x *WebMessageInfo) GetPushName() string {
	if x != nil && x.PushName != nil {
		return *x.PushName
	}
	return ""
}

func (x *WebMessageInfo) GetMediaCiphertextSHA256() []byte {
	if x != nil {
		return x.MediaCiphertextSHA256
	}
	return nil
}

func (x *WebMessageInfo) GetMulticast() bool {
	if x != nil && x.Multicast != nil {
		return *x.Multicast
	}
	return false
}

func (x *WebMessageInfo) GetUrlText() bool {
	if x != nil && x.UrlText != nil {
		return *x.UrlText
	}
	return false
}

func (x *WebMessageInfo) GetUrlNumber() bool {
	if x != nil && x.UrlNumber != nil {
		return *x.UrlNumber
	}
	return false
}

func (x *WebMessageInfo) GetMessageStubType() WebMessageInfo_StubType {
	if x != nil && x.MessageStubType != nil {
		return *x.MessageStubType
	}
	return WebMessageInfo_UNKNOWN
}

func (x *WebMessageInfo) GetClearMedia() bool {
	if x != nil && x.ClearMedia != nil {
		return *x.ClearMedia
	}
	return false
}

func (x *WebMessageInfo) GetMessageStubParameters() []string {
	if x != nil {
		return x.MessageStubParameters
	}
	return nil
}

func (x *WebMessageInfo) GetDuration() uint32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *WebMessageInfo) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *WebMessageInfo) GetPaymentInfo() *PaymentInfo {
	if x != nil {
		return x.PaymentInfo
	}
	return nil
}

func (x *WebMessageInfo) GetFinalLiveLocation() *waE2E.LiveLocationMessage {
	if x != nil {
		return x.FinalLiveLocation
	}
	return nil
}

func (x *WebMessageInfo) GetQuotedPaymentInfo() *PaymentInfo {
	if x != nil {
		return x.QuotedPaymentInfo
	}
	return nil
}

func (x *WebMessageInfo) GetEphemeralStartTimestamp() uint64 {
	if x != nil && x.EphemeralStartTimestamp != nil {
		return *x.EphemeralStartTimestamp
	}
	return 0
}

func (x *WebMessageInfo) GetEphemeralDuration() uint32 {
	if x != nil && x.EphemeralDuration != nil {
		return *x.EphemeralDuration
	}
	return 0
}

func (x *WebMessageInfo) GetEphemeralOffToOn() bool {
	if x != nil && x.EphemeralOffToOn != nil {
		return *x.EphemeralOffToOn
	}
	return false
}

func (x *WebMessageInfo) GetEphemeralOutOfSync() bool {
	if x != nil && x.EphemeralOutOfSync != nil {
		return *x.EphemeralOutOfSync
	}
	return false
}

func (x *WebMessageInfo) GetBizPrivacyStatus() WebMessageInfo_BizPrivacyStatus {
	if x != nil && x.BizPrivacyStatus != nil {
		return *x.BizPrivacyStatus
	}
	return WebMessageInfo_E2EE
}

func (x *WebMessageInfo) GetVerifiedBizName() string {
	if x != nil && x.VerifiedBizName != nil {
		return *x.VerifiedBizName
	}
	return ""
}

func (x *WebMessageInfo) GetMediaData() *MediaData {
	if x != nil {
		return x.MediaData
	}
	return nil
}

func (x *WebMessageInfo) GetPhotoChange() *PhotoChange {
	if x != nil {
		return x.PhotoChange
	}
	return nil
}

func (x *WebMessageInfo) GetUserReceipt() []*UserReceipt {
	if x != nil {
		return x.UserReceipt
	}
	return nil
}

func (x *WebMessageInfo) GetReactions() []*Reaction {
	if x != nil {
		return x.Reactions
	}
	return nil
}

func (x *WebMessageInfo) GetQuotedStickerData() *MediaData {
	if x != nil {
		return x.QuotedStickerData
	}
	return nil
}

func (x *WebMessageInfo) GetFutureproofData() []byte {
	if x != nil {
		return x.FutureproofData
	}
	return nil
}

func (x *WebMessageInfo) GetStatusPsa() *StatusPSA {
	if x != nil {
		return x.StatusPsa
	}
	return nil
}

func (x *WebMessageInfo) GetPollUpdates() []*PollUpdate {
	if x != nil {
		return x.PollUpdates
	}
	return nil
}

func (x *WebMessageInfo) GetPollAdditionalMetadata() *PollAdditionalMetadata {
	if x != nil {
		return x.PollAdditionalMetadata
	}
	return nil
}

func (x *WebMessageInfo) GetAgentID() string {
	if x != nil && x.AgentID != nil {
		return *x.AgentID
	}
	return ""
}

func (x *WebMessageInfo) GetStatusAlreadyViewed() bool {
	if x != nil && x.StatusAlreadyViewed != nil {
		return *x.StatusAlreadyViewed
	}
	return false
}

func (x *WebMessageInfo) GetMessageSecret() []byte {
	if x != nil {
		return x.MessageSecret
	}
	return nil
}

func (x *WebMessageInfo) GetKeepInChat() *KeepInChat {
	if x != nil {
		return x.KeepInChat
	}
	return nil
}

func (x *WebMessageInfo) GetOriginalSelfAuthorUserJIDString() string {
	if x != nil && x.OriginalSelfAuthorUserJIDString != nil {
		return *x.OriginalSelfAuthorUserJIDString
	}
	return ""
}

func (x *WebMessageInfo) GetRevokeMessageTimestamp() uint64 {
	if x != nil && x.RevokeMessageTimestamp != nil {
		return *x.RevokeMessageTimestamp
	}
	return 0
}

func (x *WebMessageInfo) GetPinInChat() *PinInChat {
	if x != nil {
		return x.PinInChat
	}
	return nil
}

func (x *WebMessageInfo) GetPremiumMessageInfo() *PremiumMessageInfo {
	if x != nil {
		return x.PremiumMessageInfo
	}
	return nil
}

func (x *WebMessageInfo) GetIs1PBizBotMessage() bool {
	if x != nil && x.Is1PBizBotMessage != nil {
		return *x.Is1PBizBotMessage
	}
	return false
}

func (x *WebMessageInfo) GetIsGroupHistoryMessage() bool {
	if x != nil && x.IsGroupHistoryMessage != nil {
		return *x.IsGroupHistoryMessage
	}
	return false
}

func (x *WebMessageInfo) GetBotMessageInvokerJID() string {
	if x != nil && x.BotMessageInvokerJID != nil {
		return *x.BotMessageInvokerJID
	}
	return ""
}

func (x *WebMessageInfo) GetCommentMetadata() *CommentMetadata {
	if x != nil {
		return x.CommentMetadata
	}
	return nil
}

func (x *WebMessageInfo) GetEventResponses() []*EventResponse {
	if x != nil {
		return x.EventResponses
	}
	return nil
}

func (x *WebMessageInfo) GetReportingTokenInfo() *ReportingTokenInfo {
	if x != nil {
		return x.ReportingTokenInfo
	}
	return nil
}

func (x *WebMessageInfo) GetNewsletterServerID() uint64 {
	if x != nil && x.NewsletterServerID != nil {
		return *x.NewsletterServerID
	}
	return 0
}

func (x *WebMessageInfo) GetEventAdditionalMetadata() *EventAdditionalMetadata {
	if x != nil {
		return x.EventAdditionalMetadata
	}
	return nil
}

func (x *WebMessageInfo) GetIsMentionedInStatus() bool {
	if x != nil && x.IsMentionedInStatus != nil {
		return *x.IsMentionedInStatus
	}
	return false
}

func (x *WebMessageInfo) GetStatusMentions() []string {
	if x != nil {
		return x.StatusMentions
	}
	return nil
}

func (x *WebMessageInfo) GetTargetMessageID() *waCommon.MessageKey {
	if x != nil {
		return x.TargetMessageID
	}
	return nil
}

func (x *WebMessageInfo) GetMessageAddOns() []*MessageAddOn {
	if x != nil {
		return x.MessageAddOns
	}
	return nil
}

func (x *WebMessageInfo) GetStatusMentionMessageInfo() *StatusMentionMessage {
	if x != nil {
		return x.StatusMentionMessageInfo
	}
	return nil
}

func (x *WebMessageInfo) GetIsSupportAiMessage() bool {
	if x != nil && x.IsSupportAiMessage != nil {
		return *x.IsSupportAiMessage
	}
	return false
}

func (x *WebMessageInfo) GetStatusMentionSources() []string {
	if x != nil {
		return x.StatusMentionSources
	}
	return nil
}

func (x *WebMessageInfo) GetSupportAiCitations() []*Citation {
	if x != nil {
		return x.SupportAiCitations
	}
	return nil
}

func (x *WebMessageInfo) GetBotTargetID() string {
	if x != nil && x.BotTargetID != nil {
		return *x.BotTargetID
	}
	return ""
}

type PaymentInfo struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	CurrencyDeprecated   *PaymentInfo_Currency  `protobuf:"varint,1,opt,name=currencyDeprecated,enum=WAWebProtobufsWeb.PaymentInfo_Currency" json:"currencyDeprecated,omitempty"`
	Amount1000           *uint64                `protobuf:"varint,2,opt,name=amount1000" json:"amount1000,omitempty"`
	ReceiverJID          *string                `protobuf:"bytes,3,opt,name=receiverJID" json:"receiverJID,omitempty"`
	Status               *PaymentInfo_Status    `protobuf:"varint,4,opt,name=status,enum=WAWebProtobufsWeb.PaymentInfo_Status" json:"status,omitempty"`
	TransactionTimestamp *uint64                `protobuf:"varint,5,opt,name=transactionTimestamp" json:"transactionTimestamp,omitempty"`
	RequestMessageKey    *waCommon.MessageKey   `protobuf:"bytes,6,opt,name=requestMessageKey" json:"requestMessageKey,omitempty"`
	ExpiryTimestamp      *uint64                `protobuf:"varint,7,opt,name=expiryTimestamp" json:"expiryTimestamp,omitempty"`
	Futureproofed        *bool                  `protobuf:"varint,8,opt,name=futureproofed" json:"futureproofed,omitempty"`
	Currency             *string                `protobuf:"bytes,9,opt,name=currency" json:"currency,omitempty"`
	TxnStatus            *PaymentInfo_TxnStatus `protobuf:"varint,10,opt,name=txnStatus,enum=WAWebProtobufsWeb.PaymentInfo_TxnStatus" json:"txnStatus,omitempty"`
	UseNoviFiatFormat    *bool                  `protobuf:"varint,11,opt,name=useNoviFiatFormat" json:"useNoviFiatFormat,omitempty"`
	PrimaryAmount        *waE2E.Money           `protobuf:"bytes,12,opt,name=primaryAmount" json:"primaryAmount,omitempty"`
	ExchangeAmount       *waE2E.Money           `protobuf:"bytes,13,opt,name=exchangeAmount" json:"exchangeAmount,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PaymentInfo) Reset() {
	*x = PaymentInfo{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentInfo) ProtoMessage() {}

func (x *PaymentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentInfo.ProtoReflect.Descriptor instead.
func (*PaymentInfo) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{1}
}

func (x *PaymentInfo) GetCurrencyDeprecated() PaymentInfo_Currency {
	if x != nil && x.CurrencyDeprecated != nil {
		return *x.CurrencyDeprecated
	}
	return PaymentInfo_UNKNOWN_CURRENCY
}

func (x *PaymentInfo) GetAmount1000() uint64 {
	if x != nil && x.Amount1000 != nil {
		return *x.Amount1000
	}
	return 0
}

func (x *PaymentInfo) GetReceiverJID() string {
	if x != nil && x.ReceiverJID != nil {
		return *x.ReceiverJID
	}
	return ""
}

func (x *PaymentInfo) GetStatus() PaymentInfo_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return PaymentInfo_UNKNOWN_STATUS
}

func (x *PaymentInfo) GetTransactionTimestamp() uint64 {
	if x != nil && x.TransactionTimestamp != nil {
		return *x.TransactionTimestamp
	}
	return 0
}

func (x *PaymentInfo) GetRequestMessageKey() *waCommon.MessageKey {
	if x != nil {
		return x.RequestMessageKey
	}
	return nil
}

func (x *PaymentInfo) GetExpiryTimestamp() uint64 {
	if x != nil && x.ExpiryTimestamp != nil {
		return *x.ExpiryTimestamp
	}
	return 0
}

func (x *PaymentInfo) GetFutureproofed() bool {
	if x != nil && x.Futureproofed != nil {
		return *x.Futureproofed
	}
	return false
}

func (x *PaymentInfo) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *PaymentInfo) GetTxnStatus() PaymentInfo_TxnStatus {
	if x != nil && x.TxnStatus != nil {
		return *x.TxnStatus
	}
	return PaymentInfo_UNKNOWN
}

func (x *PaymentInfo) GetUseNoviFiatFormat() bool {
	if x != nil && x.UseNoviFiatFormat != nil {
		return *x.UseNoviFiatFormat
	}
	return false
}

func (x *PaymentInfo) GetPrimaryAmount() *waE2E.Money {
	if x != nil {
		return x.PrimaryAmount
	}
	return nil
}

func (x *PaymentInfo) GetExchangeAmount() *waE2E.Money {
	if x != nil {
		return x.ExchangeAmount
	}
	return nil
}

type WebFeatures struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	LabelsDisplay                *WebFeatures_Flag      `protobuf:"varint,1,opt,name=labelsDisplay,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"labelsDisplay,omitempty"`
	VoipIndividualOutgoing       *WebFeatures_Flag      `protobuf:"varint,2,opt,name=voipIndividualOutgoing,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"voipIndividualOutgoing,omitempty"`
	GroupsV3                     *WebFeatures_Flag      `protobuf:"varint,3,opt,name=groupsV3,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"groupsV3,omitempty"`
	GroupsV3Create               *WebFeatures_Flag      `protobuf:"varint,4,opt,name=groupsV3Create,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"groupsV3Create,omitempty"`
	ChangeNumberV2               *WebFeatures_Flag      `protobuf:"varint,5,opt,name=changeNumberV2,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"changeNumberV2,omitempty"`
	QueryStatusV3Thumbnail       *WebFeatures_Flag      `protobuf:"varint,6,opt,name=queryStatusV3Thumbnail,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"queryStatusV3Thumbnail,omitempty"`
	LiveLocations                *WebFeatures_Flag      `protobuf:"varint,7,opt,name=liveLocations,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"liveLocations,omitempty"`
	QueryVname                   *WebFeatures_Flag      `protobuf:"varint,8,opt,name=queryVname,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"queryVname,omitempty"`
	VoipIndividualIncoming       *WebFeatures_Flag      `protobuf:"varint,9,opt,name=voipIndividualIncoming,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"voipIndividualIncoming,omitempty"`
	QuickRepliesQuery            *WebFeatures_Flag      `protobuf:"varint,10,opt,name=quickRepliesQuery,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"quickRepliesQuery,omitempty"`
	Payments                     *WebFeatures_Flag      `protobuf:"varint,11,opt,name=payments,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"payments,omitempty"`
	StickerPackQuery             *WebFeatures_Flag      `protobuf:"varint,12,opt,name=stickerPackQuery,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"stickerPackQuery,omitempty"`
	LiveLocationsFinal           *WebFeatures_Flag      `protobuf:"varint,13,opt,name=liveLocationsFinal,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"liveLocationsFinal,omitempty"`
	LabelsEdit                   *WebFeatures_Flag      `protobuf:"varint,14,opt,name=labelsEdit,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"labelsEdit,omitempty"`
	MediaUpload                  *WebFeatures_Flag      `protobuf:"varint,15,opt,name=mediaUpload,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"mediaUpload,omitempty"`
	MediaUploadRichQuickReplies  *WebFeatures_Flag      `protobuf:"varint,18,opt,name=mediaUploadRichQuickReplies,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"mediaUploadRichQuickReplies,omitempty"`
	VnameV2                      *WebFeatures_Flag      `protobuf:"varint,19,opt,name=vnameV2,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"vnameV2,omitempty"`
	VideoPlaybackURL             *WebFeatures_Flag      `protobuf:"varint,20,opt,name=videoPlaybackURL,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"videoPlaybackURL,omitempty"`
	StatusRanking                *WebFeatures_Flag      `protobuf:"varint,21,opt,name=statusRanking,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"statusRanking,omitempty"`
	VoipIndividualVideo          *WebFeatures_Flag      `protobuf:"varint,22,opt,name=voipIndividualVideo,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"voipIndividualVideo,omitempty"`
	ThirdPartyStickers           *WebFeatures_Flag      `protobuf:"varint,23,opt,name=thirdPartyStickers,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"thirdPartyStickers,omitempty"`
	FrequentlyForwardedSetting   *WebFeatures_Flag      `protobuf:"varint,24,opt,name=frequentlyForwardedSetting,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"frequentlyForwardedSetting,omitempty"`
	GroupsV4JoinPermission       *WebFeatures_Flag      `protobuf:"varint,25,opt,name=groupsV4JoinPermission,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"groupsV4JoinPermission,omitempty"`
	RecentStickers               *WebFeatures_Flag      `protobuf:"varint,26,opt,name=recentStickers,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"recentStickers,omitempty"`
	Catalog                      *WebFeatures_Flag      `protobuf:"varint,27,opt,name=catalog,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"catalog,omitempty"`
	StarredStickers              *WebFeatures_Flag      `protobuf:"varint,28,opt,name=starredStickers,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"starredStickers,omitempty"`
	VoipGroupCall                *WebFeatures_Flag      `protobuf:"varint,29,opt,name=voipGroupCall,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"voipGroupCall,omitempty"`
	TemplateMessage              *WebFeatures_Flag      `protobuf:"varint,30,opt,name=templateMessage,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"templateMessage,omitempty"`
	TemplateMessageInteractivity *WebFeatures_Flag      `protobuf:"varint,31,opt,name=templateMessageInteractivity,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"templateMessageInteractivity,omitempty"`
	EphemeralMessages            *WebFeatures_Flag      `protobuf:"varint,32,opt,name=ephemeralMessages,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"ephemeralMessages,omitempty"`
	E2ENotificationSync          *WebFeatures_Flag      `protobuf:"varint,33,opt,name=e2ENotificationSync,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"e2ENotificationSync,omitempty"`
	RecentStickersV2             *WebFeatures_Flag      `protobuf:"varint,34,opt,name=recentStickersV2,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"recentStickersV2,omitempty"`
	RecentStickersV3             *WebFeatures_Flag      `protobuf:"varint,36,opt,name=recentStickersV3,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"recentStickersV3,omitempty"`
	UserNotice                   *WebFeatures_Flag      `protobuf:"varint,37,opt,name=userNotice,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"userNotice,omitempty"`
	Support                      *WebFeatures_Flag      `protobuf:"varint,39,opt,name=support,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"support,omitempty"`
	GroupUiiCleanup              *WebFeatures_Flag      `protobuf:"varint,40,opt,name=groupUiiCleanup,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"groupUiiCleanup,omitempty"`
	GroupDogfoodingInternalOnly  *WebFeatures_Flag      `protobuf:"varint,41,opt,name=groupDogfoodingInternalOnly,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"groupDogfoodingInternalOnly,omitempty"`
	SettingsSync                 *WebFeatures_Flag      `protobuf:"varint,42,opt,name=settingsSync,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"settingsSync,omitempty"`
	ArchiveV2                    *WebFeatures_Flag      `protobuf:"varint,43,opt,name=archiveV2,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"archiveV2,omitempty"`
	EphemeralAllowGroupMembers   *WebFeatures_Flag      `protobuf:"varint,44,opt,name=ephemeralAllowGroupMembers,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"ephemeralAllowGroupMembers,omitempty"`
	Ephemeral24HDuration         *WebFeatures_Flag      `protobuf:"varint,45,opt,name=ephemeral24HDuration,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"ephemeral24HDuration,omitempty"`
	MdForceUpgrade               *WebFeatures_Flag      `protobuf:"varint,46,opt,name=mdForceUpgrade,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"mdForceUpgrade,omitempty"`
	DisappearingMode             *WebFeatures_Flag      `protobuf:"varint,47,opt,name=disappearingMode,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"disappearingMode,omitempty"`
	ExternalMdOptInAvailable     *WebFeatures_Flag      `protobuf:"varint,48,opt,name=externalMdOptInAvailable,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"externalMdOptInAvailable,omitempty"`
	NoDeleteMessageTimeLimit     *WebFeatures_Flag      `protobuf:"varint,49,opt,name=noDeleteMessageTimeLimit,enum=WAWebProtobufsWeb.WebFeatures_Flag" json:"noDeleteMessageTimeLimit,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *WebFeatures) Reset() {
	*x = WebFeatures{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebFeatures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebFeatures) ProtoMessage() {}

func (x *WebFeatures) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebFeatures.ProtoReflect.Descriptor instead.
func (*WebFeatures) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{2}
}

func (x *WebFeatures) GetLabelsDisplay() WebFeatures_Flag {
	if x != nil && x.LabelsDisplay != nil {
		return *x.LabelsDisplay
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetVoipIndividualOutgoing() WebFeatures_Flag {
	if x != nil && x.VoipIndividualOutgoing != nil {
		return *x.VoipIndividualOutgoing
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetGroupsV3() WebFeatures_Flag {
	if x != nil && x.GroupsV3 != nil {
		return *x.GroupsV3
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetGroupsV3Create() WebFeatures_Flag {
	if x != nil && x.GroupsV3Create != nil {
		return *x.GroupsV3Create
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetChangeNumberV2() WebFeatures_Flag {
	if x != nil && x.ChangeNumberV2 != nil {
		return *x.ChangeNumberV2
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetQueryStatusV3Thumbnail() WebFeatures_Flag {
	if x != nil && x.QueryStatusV3Thumbnail != nil {
		return *x.QueryStatusV3Thumbnail
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetLiveLocations() WebFeatures_Flag {
	if x != nil && x.LiveLocations != nil {
		return *x.LiveLocations
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetQueryVname() WebFeatures_Flag {
	if x != nil && x.QueryVname != nil {
		return *x.QueryVname
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetVoipIndividualIncoming() WebFeatures_Flag {
	if x != nil && x.VoipIndividualIncoming != nil {
		return *x.VoipIndividualIncoming
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetQuickRepliesQuery() WebFeatures_Flag {
	if x != nil && x.QuickRepliesQuery != nil {
		return *x.QuickRepliesQuery
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetPayments() WebFeatures_Flag {
	if x != nil && x.Payments != nil {
		return *x.Payments
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetStickerPackQuery() WebFeatures_Flag {
	if x != nil && x.StickerPackQuery != nil {
		return *x.StickerPackQuery
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetLiveLocationsFinal() WebFeatures_Flag {
	if x != nil && x.LiveLocationsFinal != nil {
		return *x.LiveLocationsFinal
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetLabelsEdit() WebFeatures_Flag {
	if x != nil && x.LabelsEdit != nil {
		return *x.LabelsEdit
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetMediaUpload() WebFeatures_Flag {
	if x != nil && x.MediaUpload != nil {
		return *x.MediaUpload
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetMediaUploadRichQuickReplies() WebFeatures_Flag {
	if x != nil && x.MediaUploadRichQuickReplies != nil {
		return *x.MediaUploadRichQuickReplies
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetVnameV2() WebFeatures_Flag {
	if x != nil && x.VnameV2 != nil {
		return *x.VnameV2
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetVideoPlaybackURL() WebFeatures_Flag {
	if x != nil && x.VideoPlaybackURL != nil {
		return *x.VideoPlaybackURL
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetStatusRanking() WebFeatures_Flag {
	if x != nil && x.StatusRanking != nil {
		return *x.StatusRanking
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetVoipIndividualVideo() WebFeatures_Flag {
	if x != nil && x.VoipIndividualVideo != nil {
		return *x.VoipIndividualVideo
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetThirdPartyStickers() WebFeatures_Flag {
	if x != nil && x.ThirdPartyStickers != nil {
		return *x.ThirdPartyStickers
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetFrequentlyForwardedSetting() WebFeatures_Flag {
	if x != nil && x.FrequentlyForwardedSetting != nil {
		return *x.FrequentlyForwardedSetting
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetGroupsV4JoinPermission() WebFeatures_Flag {
	if x != nil && x.GroupsV4JoinPermission != nil {
		return *x.GroupsV4JoinPermission
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetRecentStickers() WebFeatures_Flag {
	if x != nil && x.RecentStickers != nil {
		return *x.RecentStickers
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetCatalog() WebFeatures_Flag {
	if x != nil && x.Catalog != nil {
		return *x.Catalog
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetStarredStickers() WebFeatures_Flag {
	if x != nil && x.StarredStickers != nil {
		return *x.StarredStickers
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetVoipGroupCall() WebFeatures_Flag {
	if x != nil && x.VoipGroupCall != nil {
		return *x.VoipGroupCall
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetTemplateMessage() WebFeatures_Flag {
	if x != nil && x.TemplateMessage != nil {
		return *x.TemplateMessage
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetTemplateMessageInteractivity() WebFeatures_Flag {
	if x != nil && x.TemplateMessageInteractivity != nil {
		return *x.TemplateMessageInteractivity
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetEphemeralMessages() WebFeatures_Flag {
	if x != nil && x.EphemeralMessages != nil {
		return *x.EphemeralMessages
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetE2ENotificationSync() WebFeatures_Flag {
	if x != nil && x.E2ENotificationSync != nil {
		return *x.E2ENotificationSync
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetRecentStickersV2() WebFeatures_Flag {
	if x != nil && x.RecentStickersV2 != nil {
		return *x.RecentStickersV2
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetRecentStickersV3() WebFeatures_Flag {
	if x != nil && x.RecentStickersV3 != nil {
		return *x.RecentStickersV3
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetUserNotice() WebFeatures_Flag {
	if x != nil && x.UserNotice != nil {
		return *x.UserNotice
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetSupport() WebFeatures_Flag {
	if x != nil && x.Support != nil {
		return *x.Support
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetGroupUiiCleanup() WebFeatures_Flag {
	if x != nil && x.GroupUiiCleanup != nil {
		return *x.GroupUiiCleanup
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetGroupDogfoodingInternalOnly() WebFeatures_Flag {
	if x != nil && x.GroupDogfoodingInternalOnly != nil {
		return *x.GroupDogfoodingInternalOnly
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetSettingsSync() WebFeatures_Flag {
	if x != nil && x.SettingsSync != nil {
		return *x.SettingsSync
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetArchiveV2() WebFeatures_Flag {
	if x != nil && x.ArchiveV2 != nil {
		return *x.ArchiveV2
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetEphemeralAllowGroupMembers() WebFeatures_Flag {
	if x != nil && x.EphemeralAllowGroupMembers != nil {
		return *x.EphemeralAllowGroupMembers
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetEphemeral24HDuration() WebFeatures_Flag {
	if x != nil && x.Ephemeral24HDuration != nil {
		return *x.Ephemeral24HDuration
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetMdForceUpgrade() WebFeatures_Flag {
	if x != nil && x.MdForceUpgrade != nil {
		return *x.MdForceUpgrade
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetDisappearingMode() WebFeatures_Flag {
	if x != nil && x.DisappearingMode != nil {
		return *x.DisappearingMode
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetExternalMdOptInAvailable() WebFeatures_Flag {
	if x != nil && x.ExternalMdOptInAvailable != nil {
		return *x.ExternalMdOptInAvailable
	}
	return WebFeatures_NOT_STARTED
}

func (x *WebFeatures) GetNoDeleteMessageTimeLimit() WebFeatures_Flag {
	if x != nil && x.NoDeleteMessageTimeLimit != nil {
		return *x.NoDeleteMessageTimeLimit
	}
	return WebFeatures_NOT_STARTED
}

type PinInChat struct {
	state                   protoimpl.MessageState   `protogen:"open.v1"`
	Type                    *PinInChat_Type          `protobuf:"varint,1,opt,name=type,enum=WAWebProtobufsWeb.PinInChat_Type" json:"type,omitempty"`
	Key                     *waCommon.MessageKey     `protobuf:"bytes,2,opt,name=key" json:"key,omitempty"`
	SenderTimestampMS       *int64                   `protobuf:"varint,3,opt,name=senderTimestampMS" json:"senderTimestampMS,omitempty"`
	ServerTimestampMS       *int64                   `protobuf:"varint,4,opt,name=serverTimestampMS" json:"serverTimestampMS,omitempty"`
	MessageAddOnContextInfo *MessageAddOnContextInfo `protobuf:"bytes,5,opt,name=messageAddOnContextInfo" json:"messageAddOnContextInfo,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *PinInChat) Reset() {
	*x = PinInChat{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PinInChat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinInChat) ProtoMessage() {}

func (x *PinInChat) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinInChat.ProtoReflect.Descriptor instead.
func (*PinInChat) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{3}
}

func (x *PinInChat) GetType() PinInChat_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return PinInChat_UNKNOWN_TYPE
}

func (x *PinInChat) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *PinInChat) GetSenderTimestampMS() int64 {
	if x != nil && x.SenderTimestampMS != nil {
		return *x.SenderTimestampMS
	}
	return 0
}

func (x *PinInChat) GetServerTimestampMS() int64 {
	if x != nil && x.ServerTimestampMS != nil {
		return *x.ServerTimestampMS
	}
	return 0
}

func (x *PinInChat) GetMessageAddOnContextInfo() *MessageAddOnContextInfo {
	if x != nil {
		return x.MessageAddOnContextInfo
	}
	return nil
}

type MessageAddOn struct {
	state             protoimpl.MessageState         `protogen:"open.v1"`
	MessageAddOnType  *MessageAddOn_MessageAddOnType `protobuf:"varint,1,opt,name=messageAddOnType,enum=WAWebProtobufsWeb.MessageAddOn_MessageAddOnType" json:"messageAddOnType,omitempty"`
	MessageAddOn      *waE2E.Message                 `protobuf:"bytes,2,opt,name=messageAddOn" json:"messageAddOn,omitempty"`
	SenderTimestampMS *int64                         `protobuf:"varint,3,opt,name=senderTimestampMS" json:"senderTimestampMS,omitempty"`
	ServerTimestampMS *int64                         `protobuf:"varint,4,opt,name=serverTimestampMS" json:"serverTimestampMS,omitempty"`
	Status            *WebMessageInfo_Status         `protobuf:"varint,5,opt,name=status,enum=WAWebProtobufsWeb.WebMessageInfo_Status" json:"status,omitempty"`
	AddOnContextInfo  *MessageAddOnContextInfo       `protobuf:"bytes,6,opt,name=addOnContextInfo" json:"addOnContextInfo,omitempty"`
	MessageAddOnKey   *waCommon.MessageKey           `protobuf:"bytes,7,opt,name=messageAddOnKey" json:"messageAddOnKey,omitempty"`
	LegacyMessage     *LegacyMessage                 `protobuf:"bytes,8,opt,name=legacyMessage" json:"legacyMessage,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MessageAddOn) Reset() {
	*x = MessageAddOn{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageAddOn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageAddOn) ProtoMessage() {}

func (x *MessageAddOn) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageAddOn.ProtoReflect.Descriptor instead.
func (*MessageAddOn) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{4}
}

func (x *MessageAddOn) GetMessageAddOnType() MessageAddOn_MessageAddOnType {
	if x != nil && x.MessageAddOnType != nil {
		return *x.MessageAddOnType
	}
	return MessageAddOn_UNDEFINED
}

func (x *MessageAddOn) GetMessageAddOn() *waE2E.Message {
	if x != nil {
		return x.MessageAddOn
	}
	return nil
}

func (x *MessageAddOn) GetSenderTimestampMS() int64 {
	if x != nil && x.SenderTimestampMS != nil {
		return *x.SenderTimestampMS
	}
	return 0
}

func (x *MessageAddOn) GetServerTimestampMS() int64 {
	if x != nil && x.ServerTimestampMS != nil {
		return *x.ServerTimestampMS
	}
	return 0
}

func (x *MessageAddOn) GetStatus() WebMessageInfo_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return WebMessageInfo_ERROR
}

func (x *MessageAddOn) GetAddOnContextInfo() *MessageAddOnContextInfo {
	if x != nil {
		return x.AddOnContextInfo
	}
	return nil
}

func (x *MessageAddOn) GetMessageAddOnKey() *waCommon.MessageKey {
	if x != nil {
		return x.MessageAddOnKey
	}
	return nil
}

func (x *MessageAddOn) GetLegacyMessage() *LegacyMessage {
	if x != nil {
		return x.LegacyMessage
	}
	return nil
}

type CommentMetadata struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	CommentParentKey *waCommon.MessageKey   `protobuf:"bytes,1,opt,name=commentParentKey" json:"commentParentKey,omitempty"`
	ReplyCount       *uint32                `protobuf:"varint,2,opt,name=replyCount" json:"replyCount,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CommentMetadata) Reset() {
	*x = CommentMetadata{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommentMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentMetadata) ProtoMessage() {}

func (x *CommentMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentMetadata.ProtoReflect.Descriptor instead.
func (*CommentMetadata) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{5}
}

func (x *CommentMetadata) GetCommentParentKey() *waCommon.MessageKey {
	if x != nil {
		return x.CommentParentKey
	}
	return nil
}

func (x *CommentMetadata) GetReplyCount() uint32 {
	if x != nil && x.ReplyCount != nil {
		return *x.ReplyCount
	}
	return 0
}

type WebNotificationsInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Timestamp          *uint64                `protobuf:"varint,2,opt,name=timestamp" json:"timestamp,omitempty"`
	UnreadChats        *uint32                `protobuf:"varint,3,opt,name=unreadChats" json:"unreadChats,omitempty"`
	NotifyMessageCount *uint32                `protobuf:"varint,4,opt,name=notifyMessageCount" json:"notifyMessageCount,omitempty"`
	NotifyMessages     []*WebMessageInfo      `protobuf:"bytes,5,rep,name=notifyMessages" json:"notifyMessages,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *WebNotificationsInfo) Reset() {
	*x = WebNotificationsInfo{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebNotificationsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebNotificationsInfo) ProtoMessage() {}

func (x *WebNotificationsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebNotificationsInfo.ProtoReflect.Descriptor instead.
func (*WebNotificationsInfo) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{6}
}

func (x *WebNotificationsInfo) GetTimestamp() uint64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *WebNotificationsInfo) GetUnreadChats() uint32 {
	if x != nil && x.UnreadChats != nil {
		return *x.UnreadChats
	}
	return 0
}

func (x *WebNotificationsInfo) GetNotifyMessageCount() uint32 {
	if x != nil && x.NotifyMessageCount != nil {
		return *x.NotifyMessageCount
	}
	return 0
}

func (x *WebNotificationsInfo) GetNotifyMessages() []*WebMessageInfo {
	if x != nil {
		return x.NotifyMessages
	}
	return nil
}

type NotificationMessageInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Key              *waCommon.MessageKey   `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Message          *waE2E.Message         `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	MessageTimestamp *uint64                `protobuf:"varint,3,opt,name=messageTimestamp" json:"messageTimestamp,omitempty"`
	Participant      *string                `protobuf:"bytes,4,opt,name=participant" json:"participant,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *NotificationMessageInfo) Reset() {
	*x = NotificationMessageInfo{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationMessageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationMessageInfo) ProtoMessage() {}

func (x *NotificationMessageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationMessageInfo.ProtoReflect.Descriptor instead.
func (*NotificationMessageInfo) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{7}
}

func (x *NotificationMessageInfo) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *NotificationMessageInfo) GetMessage() *waE2E.Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *NotificationMessageInfo) GetMessageTimestamp() uint64 {
	if x != nil && x.MessageTimestamp != nil {
		return *x.MessageTimestamp
	}
	return 0
}

func (x *NotificationMessageInfo) GetParticipant() string {
	if x != nil && x.Participant != nil {
		return *x.Participant
	}
	return ""
}

type ReportingTokenInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportingTag  []byte                 `protobuf:"bytes,1,opt,name=reportingTag" json:"reportingTag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportingTokenInfo) Reset() {
	*x = ReportingTokenInfo{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportingTokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportingTokenInfo) ProtoMessage() {}

func (x *ReportingTokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportingTokenInfo.ProtoReflect.Descriptor instead.
func (*ReportingTokenInfo) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{8}
}

func (x *ReportingTokenInfo) GetReportingTag() []byte {
	if x != nil {
		return x.ReportingTag
	}
	return nil
}

type MediaData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LocalPath     *string                `protobuf:"bytes,1,opt,name=localPath" json:"localPath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaData) Reset() {
	*x = MediaData{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaData) ProtoMessage() {}

func (x *MediaData) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaData.ProtoReflect.Descriptor instead.
func (*MediaData) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{9}
}

func (x *MediaData) GetLocalPath() string {
	if x != nil && x.LocalPath != nil {
		return *x.LocalPath
	}
	return ""
}

type PhotoChange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OldPhoto      []byte                 `protobuf:"bytes,1,opt,name=oldPhoto" json:"oldPhoto,omitempty"`
	NewPhoto      []byte                 `protobuf:"bytes,2,opt,name=newPhoto" json:"newPhoto,omitempty"`
	NewPhotoID    *uint32                `protobuf:"varint,3,opt,name=newPhotoID" json:"newPhotoID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PhotoChange) Reset() {
	*x = PhotoChange{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhotoChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhotoChange) ProtoMessage() {}

func (x *PhotoChange) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhotoChange.ProtoReflect.Descriptor instead.
func (*PhotoChange) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{10}
}

func (x *PhotoChange) GetOldPhoto() []byte {
	if x != nil {
		return x.OldPhoto
	}
	return nil
}

func (x *PhotoChange) GetNewPhoto() []byte {
	if x != nil {
		return x.NewPhoto
	}
	return nil
}

func (x *PhotoChange) GetNewPhotoID() uint32 {
	if x != nil && x.NewPhotoID != nil {
		return *x.NewPhotoID
	}
	return 0
}

type StatusPSA struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	CampaignID                  *uint64                `protobuf:"varint,44,req,name=campaignID" json:"campaignID,omitempty"`
	CampaignExpirationTimestamp *uint64                `protobuf:"varint,45,opt,name=campaignExpirationTimestamp" json:"campaignExpirationTimestamp,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *StatusPSA) Reset() {
	*x = StatusPSA{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusPSA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusPSA) ProtoMessage() {}

func (x *StatusPSA) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusPSA.ProtoReflect.Descriptor instead.
func (*StatusPSA) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{11}
}

func (x *StatusPSA) GetCampaignID() uint64 {
	if x != nil && x.CampaignID != nil {
		return *x.CampaignID
	}
	return 0
}

func (x *StatusPSA) GetCampaignExpirationTimestamp() uint64 {
	if x != nil && x.CampaignExpirationTimestamp != nil {
		return *x.CampaignExpirationTimestamp
	}
	return 0
}

type UserReceipt struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	UserJID            *string                `protobuf:"bytes,1,req,name=userJID" json:"userJID,omitempty"`
	ReceiptTimestamp   *int64                 `protobuf:"varint,2,opt,name=receiptTimestamp" json:"receiptTimestamp,omitempty"`
	ReadTimestamp      *int64                 `protobuf:"varint,3,opt,name=readTimestamp" json:"readTimestamp,omitempty"`
	PlayedTimestamp    *int64                 `protobuf:"varint,4,opt,name=playedTimestamp" json:"playedTimestamp,omitempty"`
	PendingDeviceJID   []string               `protobuf:"bytes,5,rep,name=pendingDeviceJID" json:"pendingDeviceJID,omitempty"`
	DeliveredDeviceJID []string               `protobuf:"bytes,6,rep,name=deliveredDeviceJID" json:"deliveredDeviceJID,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UserReceipt) Reset() {
	*x = UserReceipt{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserReceipt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserReceipt) ProtoMessage() {}

func (x *UserReceipt) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserReceipt.ProtoReflect.Descriptor instead.
func (*UserReceipt) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{12}
}

func (x *UserReceipt) GetUserJID() string {
	if x != nil && x.UserJID != nil {
		return *x.UserJID
	}
	return ""
}

func (x *UserReceipt) GetReceiptTimestamp() int64 {
	if x != nil && x.ReceiptTimestamp != nil {
		return *x.ReceiptTimestamp
	}
	return 0
}

func (x *UserReceipt) GetReadTimestamp() int64 {
	if x != nil && x.ReadTimestamp != nil {
		return *x.ReadTimestamp
	}
	return 0
}

func (x *UserReceipt) GetPlayedTimestamp() int64 {
	if x != nil && x.PlayedTimestamp != nil {
		return *x.PlayedTimestamp
	}
	return 0
}

func (x *UserReceipt) GetPendingDeviceJID() []string {
	if x != nil {
		return x.PendingDeviceJID
	}
	return nil
}

func (x *UserReceipt) GetDeliveredDeviceJID() []string {
	if x != nil {
		return x.DeliveredDeviceJID
	}
	return nil
}

type Reaction struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Key               *waCommon.MessageKey   `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Text              *string                `protobuf:"bytes,2,opt,name=text" json:"text,omitempty"`
	GroupingKey       *string                `protobuf:"bytes,3,opt,name=groupingKey" json:"groupingKey,omitempty"`
	SenderTimestampMS *int64                 `protobuf:"varint,4,opt,name=senderTimestampMS" json:"senderTimestampMS,omitempty"`
	Unread            *bool                  `protobuf:"varint,5,opt,name=unread" json:"unread,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Reaction) Reset() {
	*x = Reaction{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reaction) ProtoMessage() {}

func (x *Reaction) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reaction.ProtoReflect.Descriptor instead.
func (*Reaction) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{13}
}

func (x *Reaction) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Reaction) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

func (x *Reaction) GetGroupingKey() string {
	if x != nil && x.GroupingKey != nil {
		return *x.GroupingKey
	}
	return ""
}

func (x *Reaction) GetSenderTimestampMS() int64 {
	if x != nil && x.SenderTimestampMS != nil {
		return *x.SenderTimestampMS
	}
	return 0
}

func (x *Reaction) GetUnread() bool {
	if x != nil && x.Unread != nil {
		return *x.Unread
	}
	return false
}

type PollUpdate struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	PollUpdateMessageKey *waCommon.MessageKey   `protobuf:"bytes,1,opt,name=pollUpdateMessageKey" json:"pollUpdateMessageKey,omitempty"`
	Vote                 *waE2E.PollVoteMessage `protobuf:"bytes,2,opt,name=vote" json:"vote,omitempty"`
	SenderTimestampMS    *int64                 `protobuf:"varint,3,opt,name=senderTimestampMS" json:"senderTimestampMS,omitempty"`
	ServerTimestampMS    *int64                 `protobuf:"varint,4,opt,name=serverTimestampMS" json:"serverTimestampMS,omitempty"`
	Unread               *bool                  `protobuf:"varint,5,opt,name=unread" json:"unread,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PollUpdate) Reset() {
	*x = PollUpdate{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PollUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollUpdate) ProtoMessage() {}

func (x *PollUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollUpdate.ProtoReflect.Descriptor instead.
func (*PollUpdate) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{14}
}

func (x *PollUpdate) GetPollUpdateMessageKey() *waCommon.MessageKey {
	if x != nil {
		return x.PollUpdateMessageKey
	}
	return nil
}

func (x *PollUpdate) GetVote() *waE2E.PollVoteMessage {
	if x != nil {
		return x.Vote
	}
	return nil
}

func (x *PollUpdate) GetSenderTimestampMS() int64 {
	if x != nil && x.SenderTimestampMS != nil {
		return *x.SenderTimestampMS
	}
	return 0
}

func (x *PollUpdate) GetServerTimestampMS() int64 {
	if x != nil && x.ServerTimestampMS != nil {
		return *x.ServerTimestampMS
	}
	return 0
}

func (x *PollUpdate) GetUnread() bool {
	if x != nil && x.Unread != nil {
		return *x.Unread
	}
	return false
}

type PollAdditionalMetadata struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PollInvalidated *bool                  `protobuf:"varint,1,opt,name=pollInvalidated" json:"pollInvalidated,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PollAdditionalMetadata) Reset() {
	*x = PollAdditionalMetadata{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PollAdditionalMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollAdditionalMetadata) ProtoMessage() {}

func (x *PollAdditionalMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollAdditionalMetadata.ProtoReflect.Descriptor instead.
func (*PollAdditionalMetadata) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{15}
}

func (x *PollAdditionalMetadata) GetPollInvalidated() bool {
	if x != nil && x.PollInvalidated != nil {
		return *x.PollInvalidated
	}
	return false
}

type EventAdditionalMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsStale       *bool                  `protobuf:"varint,1,opt,name=isStale" json:"isStale,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventAdditionalMetadata) Reset() {
	*x = EventAdditionalMetadata{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventAdditionalMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventAdditionalMetadata) ProtoMessage() {}

func (x *EventAdditionalMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventAdditionalMetadata.ProtoReflect.Descriptor instead.
func (*EventAdditionalMetadata) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{16}
}

func (x *EventAdditionalMetadata) GetIsStale() bool {
	if x != nil && x.IsStale != nil {
		return *x.IsStale
	}
	return false
}

type KeepInChat struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	KeepType          *waE2E.KeepType        `protobuf:"varint,1,opt,name=keepType,enum=WAWebProtobufsE2E.KeepType" json:"keepType,omitempty"`
	ServerTimestamp   *int64                 `protobuf:"varint,2,opt,name=serverTimestamp" json:"serverTimestamp,omitempty"`
	Key               *waCommon.MessageKey   `protobuf:"bytes,3,opt,name=key" json:"key,omitempty"`
	DeviceJID         *string                `protobuf:"bytes,4,opt,name=deviceJID" json:"deviceJID,omitempty"`
	ClientTimestampMS *int64                 `protobuf:"varint,5,opt,name=clientTimestampMS" json:"clientTimestampMS,omitempty"`
	ServerTimestampMS *int64                 `protobuf:"varint,6,opt,name=serverTimestampMS" json:"serverTimestampMS,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *KeepInChat) Reset() {
	*x = KeepInChat{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KeepInChat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepInChat) ProtoMessage() {}

func (x *KeepInChat) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepInChat.ProtoReflect.Descriptor instead.
func (*KeepInChat) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{17}
}

func (x *KeepInChat) GetKeepType() waE2E.KeepType {
	if x != nil && x.KeepType != nil {
		return *x.KeepType
	}
	return waE2E.KeepType(0)
}

func (x *KeepInChat) GetServerTimestamp() int64 {
	if x != nil && x.ServerTimestamp != nil {
		return *x.ServerTimestamp
	}
	return 0
}

func (x *KeepInChat) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *KeepInChat) GetDeviceJID() string {
	if x != nil && x.DeviceJID != nil {
		return *x.DeviceJID
	}
	return ""
}

func (x *KeepInChat) GetClientTimestampMS() int64 {
	if x != nil && x.ClientTimestampMS != nil {
		return *x.ClientTimestampMS
	}
	return 0
}

func (x *KeepInChat) GetServerTimestampMS() int64 {
	if x != nil && x.ServerTimestampMS != nil {
		return *x.ServerTimestampMS
	}
	return 0
}

type MessageAddOnContextInfo struct {
	state                      protoimpl.MessageState                           `protogen:"open.v1"`
	MessageAddOnDurationInSecs *uint32                                          `protobuf:"varint,1,opt,name=messageAddOnDurationInSecs" json:"messageAddOnDurationInSecs,omitempty"`
	MessageAddOnExpiryType     *waE2E.MessageContextInfo_MessageAddonExpiryType `protobuf:"varint,2,opt,name=messageAddOnExpiryType,enum=WAWebProtobufsE2E.MessageContextInfo_MessageAddonExpiryType" json:"messageAddOnExpiryType,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *MessageAddOnContextInfo) Reset() {
	*x = MessageAddOnContextInfo{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageAddOnContextInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageAddOnContextInfo) ProtoMessage() {}

func (x *MessageAddOnContextInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageAddOnContextInfo.ProtoReflect.Descriptor instead.
func (*MessageAddOnContextInfo) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{18}
}

func (x *MessageAddOnContextInfo) GetMessageAddOnDurationInSecs() uint32 {
	if x != nil && x.MessageAddOnDurationInSecs != nil {
		return *x.MessageAddOnDurationInSecs
	}
	return 0
}

func (x *MessageAddOnContextInfo) GetMessageAddOnExpiryType() waE2E.MessageContextInfo_MessageAddonExpiryType {
	if x != nil && x.MessageAddOnExpiryType != nil {
		return *x.MessageAddOnExpiryType
	}
	return waE2E.MessageContextInfo_MessageAddonExpiryType(1)
}

type PremiumMessageInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ServerCampaignID *string                `protobuf:"bytes,1,opt,name=serverCampaignID" json:"serverCampaignID,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PremiumMessageInfo) Reset() {
	*x = PremiumMessageInfo{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PremiumMessageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PremiumMessageInfo) ProtoMessage() {}

func (x *PremiumMessageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PremiumMessageInfo.ProtoReflect.Descriptor instead.
func (*PremiumMessageInfo) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{19}
}

func (x *PremiumMessageInfo) GetServerCampaignID() string {
	if x != nil && x.ServerCampaignID != nil {
		return *x.ServerCampaignID
	}
	return ""
}

type EventResponse struct {
	state                   protoimpl.MessageState      `protogen:"open.v1"`
	EventResponseMessageKey *waCommon.MessageKey        `protobuf:"bytes,1,opt,name=eventResponseMessageKey" json:"eventResponseMessageKey,omitempty"`
	TimestampMS             *int64                      `protobuf:"varint,2,opt,name=timestampMS" json:"timestampMS,omitempty"`
	EventResponseMessage    *waE2E.EventResponseMessage `protobuf:"bytes,3,opt,name=eventResponseMessage" json:"eventResponseMessage,omitempty"`
	Unread                  *bool                       `protobuf:"varint,4,opt,name=unread" json:"unread,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *EventResponse) Reset() {
	*x = EventResponse{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventResponse) ProtoMessage() {}

func (x *EventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventResponse.ProtoReflect.Descriptor instead.
func (*EventResponse) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{20}
}

func (x *EventResponse) GetEventResponseMessageKey() *waCommon.MessageKey {
	if x != nil {
		return x.EventResponseMessageKey
	}
	return nil
}

func (x *EventResponse) GetTimestampMS() int64 {
	if x != nil && x.TimestampMS != nil {
		return *x.TimestampMS
	}
	return 0
}

func (x *EventResponse) GetEventResponseMessage() *waE2E.EventResponseMessage {
	if x != nil {
		return x.EventResponseMessage
	}
	return nil
}

func (x *EventResponse) GetUnread() bool {
	if x != nil && x.Unread != nil {
		return *x.Unread
	}
	return false
}

type LegacyMessage struct {
	state                protoimpl.MessageState      `protogen:"open.v1"`
	EventResponseMessage *waE2E.EventResponseMessage `protobuf:"bytes,1,opt,name=eventResponseMessage" json:"eventResponseMessage,omitempty"`
	PollVote             *waE2E.PollVoteMessage      `protobuf:"bytes,2,opt,name=pollVote" json:"pollVote,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *LegacyMessage) Reset() {
	*x = LegacyMessage{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LegacyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LegacyMessage) ProtoMessage() {}

func (x *LegacyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LegacyMessage.ProtoReflect.Descriptor instead.
func (*LegacyMessage) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{21}
}

func (x *LegacyMessage) GetEventResponseMessage() *waE2E.EventResponseMessage {
	if x != nil {
		return x.EventResponseMessage
	}
	return nil
}

func (x *LegacyMessage) GetPollVote() *waE2E.PollVoteMessage {
	if x != nil {
		return x.PollVote
	}
	return nil
}

type StatusMentionMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QuotedStatus  *waE2E.Message         `protobuf:"bytes,1,opt,name=quotedStatus" json:"quotedStatus,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatusMentionMessage) Reset() {
	*x = StatusMentionMessage{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusMentionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusMentionMessage) ProtoMessage() {}

func (x *StatusMentionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusMentionMessage.ProtoReflect.Descriptor instead.
func (*StatusMentionMessage) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{22}
}

func (x *StatusMentionMessage) GetQuotedStatus() *waE2E.Message {
	if x != nil {
		return x.QuotedStatus
	}
	return nil
}

type Citation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         *string                `protobuf:"bytes,1,req,name=title" json:"title,omitempty"`
	Subtitle      *string                `protobuf:"bytes,2,req,name=subtitle" json:"subtitle,omitempty"`
	CmsID         *string                `protobuf:"bytes,3,req,name=cmsID" json:"cmsID,omitempty"`
	ImageURL      *string                `protobuf:"bytes,4,req,name=imageURL" json:"imageURL,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Citation) Reset() {
	*x = Citation{}
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Citation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Citation) ProtoMessage() {}

func (x *Citation) ProtoReflect() protoreflect.Message {
	mi := &file_waWeb_WAWebProtobufsWeb_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Citation.ProtoReflect.Descriptor instead.
func (*Citation) Descriptor() ([]byte, []int) {
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP(), []int{23}
}

func (x *Citation) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *Citation) GetSubtitle() string {
	if x != nil && x.Subtitle != nil {
		return *x.Subtitle
	}
	return ""
}

func (x *Citation) GetCmsID() string {
	if x != nil && x.CmsID != nil {
		return *x.CmsID
	}
	return ""
}

func (x *Citation) GetImageURL() string {
	if x != nil && x.ImageURL != nil {
		return *x.ImageURL
	}
	return ""
}

var File_waWeb_WAWebProtobufsWeb_proto protoreflect.FileDescriptor

const file_waWeb_WAWebProtobufsWeb_proto_rawDesc = "" +
	"\n" +
	"\x1dwaWeb/WAWebProtobufsWeb.proto\x12\x11WAWebProtobufsWeb\x1a\x1dwaE2E/WAWebProtobufsE2E.proto\x1a\x17waCommon/WACommon.proto\"\xfaV\n" +
	"\x0eWebMessageInfo\x12&\n" +
	"\x03key\x18\x01 \x02(\v2\x14.WACommon.MessageKeyR\x03key\x124\n" +
	"\amessage\x18\x02 \x01(\v2\x1a.WAWebProtobufsE2E.MessageR\amessage\x12*\n" +
	"\x10messageTimestamp\x18\x03 \x01(\x04R\x10messageTimestamp\x12@\n" +
	"\x06status\x18\x04 \x01(\x0e2(.WAWebProtobufsWeb.WebMessageInfo.StatusR\x06status\x12 \n" +
	"\vparticipant\x18\x05 \x01(\tR\vparticipant\x120\n" +
	"\x13messageC2STimestamp\x18\x06 \x01(\x04R\x13messageC2STimestamp\x12\x16\n" +
	"\x06ignore\x18\x10 \x01(\bR\x06ignore\x12\x18\n" +
	"\astarred\x18\x11 \x01(\bR\astarred\x12\x1c\n" +
	"\tbroadcast\x18\x12 \x01(\bR\tbroadcast\x12\x1a\n" +
	"\bpushName\x18\x13 \x01(\tR\bpushName\x124\n" +
	"\x15mediaCiphertextSHA256\x18\x14 \x01(\fR\x15mediaCiphertextSHA256\x12\x1c\n" +
	"\tmulticast\x18\x15 \x01(\bR\tmulticast\x12\x18\n" +
	"\aurlText\x18\x16 \x01(\bR\aurlText\x12\x1c\n" +
	"\turlNumber\x18\x17 \x01(\bR\turlNumber\x12T\n" +
	"\x0fmessageStubType\x18\x18 \x01(\x0e2*.WAWebProtobufsWeb.WebMessageInfo.StubTypeR\x0fmessageStubType\x12\x1e\n" +
	"\n" +
	"clearMedia\x18\x19 \x01(\bR\n" +
	"clearMedia\x124\n" +
	"\x15messageStubParameters\x18\x1a \x03(\tR\x15messageStubParameters\x12\x1a\n" +
	"\bduration\x18\x1b \x01(\rR\bduration\x12\x16\n" +
	"\x06labels\x18\x1c \x03(\tR\x06labels\x12@\n" +
	"\vpaymentInfo\x18\x1d \x01(\v2\x1e.WAWebProtobufsWeb.PaymentInfoR\vpaymentInfo\x12T\n" +
	"\x11finalLiveLocation\x18\x1e \x01(\v2&.WAWebProtobufsE2E.LiveLocationMessageR\x11finalLiveLocation\x12L\n" +
	"\x11quotedPaymentInfo\x18\x1f \x01(\v2\x1e.WAWebProtobufsWeb.PaymentInfoR\x11quotedPaymentInfo\x128\n" +
	"\x17ephemeralStartTimestamp\x18  \x01(\x04R\x17ephemeralStartTimestamp\x12,\n" +
	"\x11ephemeralDuration\x18! \x01(\rR\x11ephemeralDuration\x12*\n" +
	"\x10ephemeralOffToOn\x18\" \x01(\bR\x10ephemeralOffToOn\x12.\n" +
	"\x12ephemeralOutOfSync\x18# \x01(\bR\x12ephemeralOutOfSync\x12^\n" +
	"\x10bizPrivacyStatus\x18$ \x01(\x0e22.WAWebProtobufsWeb.WebMessageInfo.BizPrivacyStatusR\x10bizPrivacyStatus\x12(\n" +
	"\x0fverifiedBizName\x18% \x01(\tR\x0fverifiedBizName\x12:\n" +
	"\tmediaData\x18& \x01(\v2\x1c.WAWebProtobufsWeb.MediaDataR\tmediaData\x12@\n" +
	"\vphotoChange\x18' \x01(\v2\x1e.WAWebProtobufsWeb.PhotoChangeR\vphotoChange\x12@\n" +
	"\vuserReceipt\x18( \x03(\v2\x1e.WAWebProtobufsWeb.UserReceiptR\vuserReceipt\x129\n" +
	"\treactions\x18) \x03(\v2\x1b.WAWebProtobufsWeb.ReactionR\treactions\x12J\n" +
	"\x11quotedStickerData\x18* \x01(\v2\x1c.WAWebProtobufsWeb.MediaDataR\x11quotedStickerData\x12(\n" +
	"\x0ffutureproofData\x18+ \x01(\fR\x0ffutureproofData\x12:\n" +
	"\tstatusPsa\x18, \x01(\v2\x1c.WAWebProtobufsWeb.StatusPSAR\tstatusPsa\x12?\n" +
	"\vpollUpdates\x18- \x03(\v2\x1d.WAWebProtobufsWeb.PollUpdateR\vpollUpdates\x12a\n" +
	"\x16pollAdditionalMetadata\x18. \x01(\v2).WAWebProtobufsWeb.PollAdditionalMetadataR\x16pollAdditionalMetadata\x12\x18\n" +
	"\aagentID\x18/ \x01(\tR\aagentID\x120\n" +
	"\x13statusAlreadyViewed\x180 \x01(\bR\x13statusAlreadyViewed\x12$\n" +
	"\rmessageSecret\x181 \x01(\fR\rmessageSecret\x12=\n" +
	"\n" +
	"keepInChat\x182 \x01(\v2\x1d.WAWebProtobufsWeb.KeepInChatR\n" +
	"keepInChat\x12H\n" +
	"\x1foriginalSelfAuthorUserJIDString\x183 \x01(\tR\x1foriginalSelfAuthorUserJIDString\x126\n" +
	"\x16revokeMessageTimestamp\x184 \x01(\x04R\x16revokeMessageTimestamp\x12:\n" +
	"\tpinInChat\x186 \x01(\v2\x1c.WAWebProtobufsWeb.PinInChatR\tpinInChat\x12U\n" +
	"\x12premiumMessageInfo\x187 \x01(\v2%.WAWebProtobufsWeb.PremiumMessageInfoR\x12premiumMessageInfo\x12,\n" +
	"\x11is1PBizBotMessage\x188 \x01(\bR\x11is1PBizBotMessage\x124\n" +
	"\x15isGroupHistoryMessage\x189 \x01(\bR\x15isGroupHistoryMessage\x122\n" +
	"\x14botMessageInvokerJID\x18: \x01(\tR\x14botMessageInvokerJID\x12L\n" +
	"\x0fcommentMetadata\x18; \x01(\v2\".WAWebProtobufsWeb.CommentMetadataR\x0fcommentMetadata\x12H\n" +
	"\x0eeventResponses\x18= \x03(\v2 .WAWebProtobufsWeb.EventResponseR\x0eeventResponses\x12U\n" +
	"\x12reportingTokenInfo\x18> \x01(\v2%.WAWebProtobufsWeb.ReportingTokenInfoR\x12reportingTokenInfo\x12.\n" +
	"\x12newsletterServerID\x18? \x01(\x04R\x12newsletterServerID\x12d\n" +
	"\x17eventAdditionalMetadata\x18@ \x01(\v2*.WAWebProtobufsWeb.EventAdditionalMetadataR\x17eventAdditionalMetadata\x120\n" +
	"\x13isMentionedInStatus\x18A \x01(\bR\x13isMentionedInStatus\x12&\n" +
	"\x0estatusMentions\x18B \x03(\tR\x0estatusMentions\x12>\n" +
	"\x0ftargetMessageID\x18C \x01(\v2\x14.WACommon.MessageKeyR\x0ftargetMessageID\x12E\n" +
	"\rmessageAddOns\x18D \x03(\v2\x1f.WAWebProtobufsWeb.MessageAddOnR\rmessageAddOns\x12c\n" +
	"\x18statusMentionMessageInfo\x18E \x01(\v2'.WAWebProtobufsWeb.StatusMentionMessageR\x18statusMentionMessageInfo\x12.\n" +
	"\x12isSupportAiMessage\x18F \x01(\bR\x12isSupportAiMessage\x122\n" +
	"\x14statusMentionSources\x18G \x03(\tR\x14statusMentionSources\x12K\n" +
	"\x12supportAiCitations\x18H \x03(\v2\x1b.WAWebProtobufsWeb.CitationR\x12supportAiCitations\x12 \n" +
	"\vbotTargetID\x18I \x01(\tR\vbotTargetID\"=\n" +
	"\x10BizPrivacyStatus\x12\b\n" +
	"\x04E2EE\x10\x00\x12\x06\n" +
	"\x02FB\x10\x02\x12\a\n" +
	"\x03BSP\x10\x01\x12\x0e\n" +
	"\n" +
	"BSP_AND_FB\x10\x03\"\xd5:\n" +
	"\bStubType\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\n" +
	"\n" +
	"\x06REVOKE\x10\x01\x12\x0e\n" +
	"\n" +
	"CIPHERTEXT\x10\x02\x12\x0f\n" +
	"\vFUTUREPROOF\x10\x03\x12\x1b\n" +
	"\x17NON_VERIFIED_TRANSITION\x10\x04\x12\x19\n" +
	"\x15UNVERIFIED_TRANSITION\x10\x05\x12\x17\n" +
	"\x13VERIFIED_TRANSITION\x10\x06\x12\x18\n" +
	"\x14VERIFIED_LOW_UNKNOWN\x10\a\x12\x11\n" +
	"\rVERIFIED_HIGH\x10\b\x12\x1c\n" +
	"\x18VERIFIED_INITIAL_UNKNOWN\x10\t\x12\x18\n" +
	"\x14VERIFIED_INITIAL_LOW\x10\n" +
	"\x12\x19\n" +
	"\x15VERIFIED_INITIAL_HIGH\x10\v\x12#\n" +
	"\x1fVERIFIED_TRANSITION_ANY_TO_NONE\x10\f\x12#\n" +
	"\x1fVERIFIED_TRANSITION_ANY_TO_HIGH\x10\r\x12#\n" +
	"\x1fVERIFIED_TRANSITION_HIGH_TO_LOW\x10\x0e\x12'\n" +
	"#VERIFIED_TRANSITION_HIGH_TO_UNKNOWN\x10\x0f\x12&\n" +
	"\"VERIFIED_TRANSITION_UNKNOWN_TO_LOW\x10\x10\x12&\n" +
	"\"VERIFIED_TRANSITION_LOW_TO_UNKNOWN\x10\x11\x12#\n" +
	"\x1fVERIFIED_TRANSITION_NONE_TO_LOW\x10\x12\x12'\n" +
	"#VERIFIED_TRANSITION_NONE_TO_UNKNOWN\x10\x13\x12\x10\n" +
	"\fGROUP_CREATE\x10\x14\x12\x18\n" +
	"\x14GROUP_CHANGE_SUBJECT\x10\x15\x12\x15\n" +
	"\x11GROUP_CHANGE_ICON\x10\x16\x12\x1c\n" +
	"\x18GROUP_CHANGE_INVITE_LINK\x10\x17\x12\x1c\n" +
	"\x18GROUP_CHANGE_DESCRIPTION\x10\x18\x12\x19\n" +
	"\x15GROUP_CHANGE_RESTRICT\x10\x19\x12\x19\n" +
	"\x15GROUP_CHANGE_ANNOUNCE\x10\x1a\x12\x19\n" +
	"\x15GROUP_PARTICIPANT_ADD\x10\x1b\x12\x1c\n" +
	"\x18GROUP_PARTICIPANT_REMOVE\x10\x1c\x12\x1d\n" +
	"\x19GROUP_PARTICIPANT_PROMOTE\x10\x1d\x12\x1c\n" +
	"\x18GROUP_PARTICIPANT_DEMOTE\x10\x1e\x12\x1c\n" +
	"\x18GROUP_PARTICIPANT_INVITE\x10\x1f\x12\x1b\n" +
	"\x17GROUP_PARTICIPANT_LEAVE\x10 \x12#\n" +
	"\x1fGROUP_PARTICIPANT_CHANGE_NUMBER\x10!\x12\x14\n" +
	"\x10BROADCAST_CREATE\x10\"\x12\x11\n" +
	"\rBROADCAST_ADD\x10#\x12\x14\n" +
	"\x10BROADCAST_REMOVE\x10$\x12\x18\n" +
	"\x14GENERIC_NOTIFICATION\x10%\x12\x18\n" +
	"\x14E2E_IDENTITY_CHANGED\x10&\x12\x11\n" +
	"\rE2E_ENCRYPTED\x10'\x12\x15\n" +
	"\x11CALL_MISSED_VOICE\x10(\x12\x15\n" +
	"\x11CALL_MISSED_VIDEO\x10)\x12\x1c\n" +
	"\x18INDIVIDUAL_CHANGE_NUMBER\x10*\x12\x10\n" +
	"\fGROUP_DELETE\x10+\x12&\n" +
	"\"GROUP_ANNOUNCE_MODE_MESSAGE_BOUNCE\x10,\x12\x1b\n" +
	"\x17CALL_MISSED_GROUP_VOICE\x10-\x12\x1b\n" +
	"\x17CALL_MISSED_GROUP_VIDEO\x10.\x12\x16\n" +
	"\x12PAYMENT_CIPHERTEXT\x10/\x12\x17\n" +
	"\x13PAYMENT_FUTUREPROOF\x100\x12,\n" +
	"(PAYMENT_TRANSACTION_STATUS_UPDATE_FAILED\x101\x12.\n" +
	"*PAYMENT_TRANSACTION_STATUS_UPDATE_REFUNDED\x102\x123\n" +
	"/PAYMENT_TRANSACTION_STATUS_UPDATE_REFUND_FAILED\x103\x125\n" +
	"1PAYMENT_TRANSACTION_STATUS_RECEIVER_PENDING_SETUP\x104\x12<\n" +
	"8PAYMENT_TRANSACTION_STATUS_RECEIVER_SUCCESS_AFTER_HICCUP\x105\x12)\n" +
	"%PAYMENT_ACTION_ACCOUNT_SETUP_REMINDER\x106\x12(\n" +
	"$PAYMENT_ACTION_SEND_PAYMENT_REMINDER\x107\x12*\n" +
	"&PAYMENT_ACTION_SEND_PAYMENT_INVITATION\x108\x12#\n" +
	"\x1fPAYMENT_ACTION_REQUEST_DECLINED\x109\x12\"\n" +
	"\x1ePAYMENT_ACTION_REQUEST_EXPIRED\x10:\x12$\n" +
	" PAYMENT_ACTION_REQUEST_CANCELLED\x10;\x12)\n" +
	"%BIZ_VERIFIED_TRANSITION_TOP_TO_BOTTOM\x10<\x12)\n" +
	"%BIZ_VERIFIED_TRANSITION_BOTTOM_TO_TOP\x10=\x12\x11\n" +
	"\rBIZ_INTRO_TOP\x10>\x12\x14\n" +
	"\x10BIZ_INTRO_BOTTOM\x10?\x12\x13\n" +
	"\x0fBIZ_NAME_CHANGE\x10@\x12\x1c\n" +
	"\x18BIZ_MOVE_TO_CONSUMER_APP\x10A\x12\x1e\n" +
	"\x1aBIZ_TWO_TIER_MIGRATION_TOP\x10B\x12!\n" +
	"\x1dBIZ_TWO_TIER_MIGRATION_BOTTOM\x10C\x12\r\n" +
	"\tOVERSIZED\x10D\x12(\n" +
	"$GROUP_CHANGE_NO_FREQUENTLY_FORWARDED\x10E\x12\x1c\n" +
	"\x18GROUP_V4_ADD_INVITE_SENT\x10F\x12&\n" +
	"\"GROUP_PARTICIPANT_ADD_REQUEST_JOIN\x10G\x12\x1c\n" +
	"\x18CHANGE_EPHEMERAL_SETTING\x10H\x12\x16\n" +
	"\x12E2E_DEVICE_CHANGED\x10I\x12\x0f\n" +
	"\vVIEWED_ONCE\x10J\x12\x15\n" +
	"\x11E2E_ENCRYPTED_NOW\x10K\x12\"\n" +
	"\x1eBLUE_MSG_BSP_FB_TO_BSP_PREMISE\x10L\x12\x1e\n" +
	"\x1aBLUE_MSG_BSP_FB_TO_SELF_FB\x10M\x12#\n" +
	"\x1fBLUE_MSG_BSP_FB_TO_SELF_PREMISE\x10N\x12\x1e\n" +
	"\x1aBLUE_MSG_BSP_FB_UNVERIFIED\x10O\x127\n" +
	"3BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED\x10P\x12\x1c\n" +
	"\x18BLUE_MSG_BSP_FB_VERIFIED\x10Q\x127\n" +
	"3BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED\x10R\x12(\n" +
	"$BLUE_MSG_BSP_PREMISE_TO_SELF_PREMISE\x10S\x12#\n" +
	"\x1fBLUE_MSG_BSP_PREMISE_UNVERIFIED\x10T\x12<\n" +
	"8BLUE_MSG_BSP_PREMISE_UNVERIFIED_TO_SELF_PREMISE_VERIFIED\x10U\x12!\n" +
	"\x1dBLUE_MSG_BSP_PREMISE_VERIFIED\x10V\x12<\n" +
	"8BLUE_MSG_BSP_PREMISE_VERIFIED_TO_SELF_PREMISE_UNVERIFIED\x10W\x12*\n" +
	"&BLUE_MSG_CONSUMER_TO_BSP_FB_UNVERIFIED\x10X\x12/\n" +
	"+BLUE_MSG_CONSUMER_TO_BSP_PREMISE_UNVERIFIED\x10Y\x12+\n" +
	"'BLUE_MSG_CONSUMER_TO_SELF_FB_UNVERIFIED\x10Z\x120\n" +
	",BLUE_MSG_CONSUMER_TO_SELF_PREMISE_UNVERIFIED\x10[\x12#\n" +
	"\x1fBLUE_MSG_SELF_FB_TO_BSP_PREMISE\x10\\\x12$\n" +
	" BLUE_MSG_SELF_FB_TO_SELF_PREMISE\x10]\x12\x1f\n" +
	"\x1bBLUE_MSG_SELF_FB_UNVERIFIED\x10^\x128\n" +
	"4BLUE_MSG_SELF_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED\x10_\x12\x1d\n" +
	"\x19BLUE_MSG_SELF_FB_VERIFIED\x10`\x128\n" +
	"4BLUE_MSG_SELF_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED\x10a\x12(\n" +
	"$BLUE_MSG_SELF_PREMISE_TO_BSP_PREMISE\x10b\x12$\n" +
	" BLUE_MSG_SELF_PREMISE_UNVERIFIED\x10c\x12\"\n" +
	"\x1eBLUE_MSG_SELF_PREMISE_VERIFIED\x10d\x12\x16\n" +
	"\x12BLUE_MSG_TO_BSP_FB\x10e\x12\x18\n" +
	"\x14BLUE_MSG_TO_CONSUMER\x10f\x12\x17\n" +
	"\x13BLUE_MSG_TO_SELF_FB\x10g\x12*\n" +
	"&BLUE_MSG_UNVERIFIED_TO_BSP_FB_VERIFIED\x10h\x12/\n" +
	"+BLUE_MSG_UNVERIFIED_TO_BSP_PREMISE_VERIFIED\x10i\x12+\n" +
	"'BLUE_MSG_UNVERIFIED_TO_SELF_FB_VERIFIED\x10j\x12#\n" +
	"\x1fBLUE_MSG_UNVERIFIED_TO_VERIFIED\x10k\x12*\n" +
	"&BLUE_MSG_VERIFIED_TO_BSP_FB_UNVERIFIED\x10l\x12/\n" +
	"+BLUE_MSG_VERIFIED_TO_BSP_PREMISE_UNVERIFIED\x10m\x12+\n" +
	"'BLUE_MSG_VERIFIED_TO_SELF_FB_UNVERIFIED\x10n\x12#\n" +
	"\x1fBLUE_MSG_VERIFIED_TO_UNVERIFIED\x10o\x126\n" +
	"2BLUE_MSG_BSP_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED\x10p\x122\n" +
	".BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_FB_VERIFIED\x10q\x126\n" +
	"2BLUE_MSG_BSP_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED\x10r\x122\n" +
	".BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_FB_UNVERIFIED\x10s\x127\n" +
	"3BLUE_MSG_SELF_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED\x10t\x127\n" +
	"3BLUE_MSG_SELF_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED\x10u\x12\x1c\n" +
	"\x18E2E_IDENTITY_UNAVAILABLE\x10v\x12\x12\n" +
	"\x0eGROUP_CREATING\x10w\x12\x17\n" +
	"\x13GROUP_CREATE_FAILED\x10x\x12\x11\n" +
	"\rGROUP_BOUNCED\x10y\x12\x11\n" +
	"\rBLOCK_CONTACT\x10z\x12!\n" +
	"\x1dEPHEMERAL_SETTING_NOT_APPLIED\x10{\x12\x0f\n" +
	"\vSYNC_FAILED\x10|\x12\v\n" +
	"\aSYNCING\x10}\x12\x1c\n" +
	"\x18BIZ_PRIVACY_MODE_INIT_FB\x10~\x12\x1d\n" +
	"\x19BIZ_PRIVACY_MODE_INIT_BSP\x10\x7f\x12\x1b\n" +
	"\x16BIZ_PRIVACY_MODE_TO_FB\x10\x80\x01\x12\x1c\n" +
	"\x17BIZ_PRIVACY_MODE_TO_BSP\x10\x81\x01\x12\x16\n" +
	"\x11DISAPPEARING_MODE\x10\x82\x01\x12\x1c\n" +
	"\x17E2E_DEVICE_FETCH_FAILED\x10\x83\x01\x12\x11\n" +
	"\fADMIN_REVOKE\x10\x84\x01\x12$\n" +
	"\x1fGROUP_INVITE_LINK_GROWTH_LOCKED\x10\x85\x01\x12 \n" +
	"\x1bCOMMUNITY_LINK_PARENT_GROUP\x10\x86\x01\x12!\n" +
	"\x1cCOMMUNITY_LINK_SIBLING_GROUP\x10\x87\x01\x12\x1d\n" +
	"\x18COMMUNITY_LINK_SUB_GROUP\x10\x88\x01\x12\"\n" +
	"\x1dCOMMUNITY_UNLINK_PARENT_GROUP\x10\x89\x01\x12#\n" +
	"\x1eCOMMUNITY_UNLINK_SIBLING_GROUP\x10\x8a\x01\x12\x1f\n" +
	"\x1aCOMMUNITY_UNLINK_SUB_GROUP\x10\x8b\x01\x12\x1d\n" +
	"\x18GROUP_PARTICIPANT_ACCEPT\x10\x8c\x01\x12(\n" +
	"#GROUP_PARTICIPANT_LINKED_GROUP_JOIN\x10\x8d\x01\x12\x15\n" +
	"\x10COMMUNITY_CREATE\x10\x8e\x01\x12\x1b\n" +
	"\x16EPHEMERAL_KEEP_IN_CHAT\x10\x8f\x01\x12+\n" +
	"&GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST\x10\x90\x01\x12(\n" +
	"#GROUP_MEMBERSHIP_JOIN_APPROVAL_MODE\x10\x91\x01\x12\"\n" +
	"\x1dINTEGRITY_UNLINK_PARENT_GROUP\x10\x92\x01\x12\"\n" +
	"\x1dCOMMUNITY_PARTICIPANT_PROMOTE\x10\x93\x01\x12!\n" +
	"\x1cCOMMUNITY_PARTICIPANT_DEMOTE\x10\x94\x01\x12#\n" +
	"\x1eCOMMUNITY_PARENT_GROUP_DELETED\x10\x95\x01\x124\n" +
	"/COMMUNITY_LINK_PARENT_GROUP_MEMBERSHIP_APPROVAL\x10\x96\x01\x124\n" +
	"/GROUP_PARTICIPANT_JOINED_GROUP_AND_PARENT_GROUP\x10\x97\x01\x12\x1a\n" +
	"\x15MASKED_THREAD_CREATED\x10\x98\x01\x12\x1b\n" +
	"\x16MASKED_THREAD_UNMASKED\x10\x99\x01\x12\x18\n" +
	"\x13BIZ_CHAT_ASSIGNMENT\x10\x9a\x01\x12\r\n" +
	"\bCHAT_PSA\x10\x9b\x01\x12\x1f\n" +
	"\x1aCHAT_POLL_CREATION_MESSAGE\x10\x9c\x01\x12\x1e\n" +
	"\x19CAG_MASKED_THREAD_CREATED\x10\x9d\x01\x12+\n" +
	"&COMMUNITY_PARENT_GROUP_SUBJECT_CHANGED\x10\x9e\x01\x12\x18\n" +
	"\x13CAG_INVITE_AUTO_ADD\x10\x9f\x01\x12!\n" +
	"\x1cBIZ_CHAT_ASSIGNMENT_UNASSIGN\x10\xa0\x01\x12\x1b\n" +
	"\x16CAG_INVITE_AUTO_JOINED\x10\xa1\x01\x12!\n" +
	"\x1cSCHEDULED_CALL_START_MESSAGE\x10\xa2\x01\x12\x1a\n" +
	"\x15COMMUNITY_INVITE_RICH\x10\xa3\x01\x12#\n" +
	"\x1eCOMMUNITY_INVITE_AUTO_ADD_RICH\x10\xa4\x01\x12\x1a\n" +
	"\x15SUB_GROUP_INVITE_RICH\x10\xa5\x01\x12#\n" +
	"\x1eSUB_GROUP_PARTICIPANT_ADD_RICH\x10\xa6\x01\x12%\n" +
	" COMMUNITY_LINK_PARENT_GROUP_RICH\x10\xa7\x01\x12#\n" +
	"\x1eCOMMUNITY_PARTICIPANT_ADD_RICH\x10\xa8\x01\x12\"\n" +
	"\x1dSILENCED_UNKNOWN_CALLER_AUDIO\x10\xa9\x01\x12\"\n" +
	"\x1dSILENCED_UNKNOWN_CALLER_VIDEO\x10\xaa\x01\x12\x1a\n" +
	"\x15GROUP_MEMBER_ADD_MODE\x10\xab\x01\x129\n" +
	"4GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST_NON_ADMIN_ADD\x10\xac\x01\x12!\n" +
	"\x1cCOMMUNITY_CHANGE_DESCRIPTION\x10\xad\x01\x12\x12\n" +
	"\rSENDER_INVITE\x10\xae\x01\x12\x14\n" +
	"\x0fRECEIVER_INVITE\x10\xaf\x01\x12(\n" +
	"#COMMUNITY_ALLOW_MEMBER_ADDED_GROUPS\x10\xb0\x01\x12\x1b\n" +
	"\x16PINNED_MESSAGE_IN_CHAT\x10\xb1\x01\x12!\n" +
	"\x1cPAYMENT_INVITE_SETUP_INVITER\x10\xb2\x01\x12.\n" +
	")PAYMENT_INVITE_SETUP_INVITEE_RECEIVE_ONLY\x10\xb3\x01\x122\n" +
	"-PAYMENT_INVITE_SETUP_INVITEE_SEND_AND_RECEIVE\x10\xb4\x01\x12\x1c\n" +
	"\x17LINKED_GROUP_CALL_START\x10\xb5\x01\x12#\n" +
	"\x1eREPORT_TO_ADMIN_ENABLED_STATUS\x10\xb6\x01\x12\x1a\n" +
	"\x15EMPTY_SUBGROUP_CREATE\x10\xb7\x01\x12\x1a\n" +
	"\x15SCHEDULED_CALL_CANCEL\x10\xb8\x01\x12+\n" +
	"&SUBGROUP_ADMIN_TRIGGERED_AUTO_ADD_RICH\x10\xb9\x01\x12(\n" +
	"#GROUP_CHANGE_RECENT_HISTORY_SHARING\x10\xba\x01\x12$\n" +
	"\x1fPAID_MESSAGE_SERVER_CAMPAIGN_ID\x10\xbb\x01\x12\x18\n" +
	"\x13GENERAL_CHAT_CREATE\x10\xbc\x01\x12\x15\n" +
	"\x10GENERAL_CHAT_ADD\x10\xbd\x01\x12#\n" +
	"\x1eGENERAL_CHAT_AUTO_ADD_DISABLED\x10\xbe\x01\x12 \n" +
	"\x1bSUGGESTED_SUBGROUP_ANNOUNCE\x10\xbf\x01\x12!\n" +
	"\x1cBIZ_BOT_1P_MESSAGING_ENABLED\x10\xc0\x01\x12\x14\n" +
	"\x0fCHANGE_USERNAME\x10\xc1\x01\x12\x1f\n" +
	"\x1aBIZ_COEX_PRIVACY_INIT_SELF\x10\xc2\x01\x12%\n" +
	" BIZ_COEX_PRIVACY_TRANSITION_SELF\x10\xc3\x01\x12\x19\n" +
	"\x14SUPPORT_AI_EDUCATION\x10\xc4\x01\x12!\n" +
	"\x1cBIZ_BOT_3P_MESSAGING_ENABLED\x10\xc5\x01\x12\x1b\n" +
	"\x16REMINDER_SETUP_MESSAGE\x10\xc6\x01\x12\x1a\n" +
	"\x15REMINDER_SENT_MESSAGE\x10\xc7\x01\x12\x1c\n" +
	"\x17REMINDER_CANCEL_MESSAGE\x10\xc8\x01\x12\x1a\n" +
	"\x15BIZ_COEX_PRIVACY_INIT\x10\xc9\x01\x12 \n" +
	"\x1bBIZ_COEX_PRIVACY_TRANSITION\x10\xca\x01\x12\x16\n" +
	"\x11GROUP_DEACTIVATED\x10\xcb\x01\x12'\n" +
	"\"COMMUNITY_DEACTIVATE_SIBLING_GROUP\x10\xcc\x01\x12\x12\n" +
	"\rEVENT_UPDATED\x10\xcd\x01\x12\x13\n" +
	"\x0eEVENT_CANCELED\x10\xce\x01\x12\x1c\n" +
	"\x17COMMUNITY_OWNER_UPDATED\x10\xcf\x01\x12*\n" +
	"%COMMUNITY_SUB_GROUP_VISIBILITY_HIDDEN\x10\xd0\x01\x12$\n" +
	"\x1fCAPI_GROUP_NE2EE_SYSTEM_MESSAGE\x10\xd1\x01\x12\x13\n" +
	"\x0eSTATUS_MENTION\x10\xd2\x01\x12!\n" +
	"\x1cUSER_CONTROLS_SYSTEM_MESSAGE\x10\xd3\x01\x12\x1b\n" +
	"\x16SUPPORT_SYSTEM_MESSAGE\x10\xd4\x01\x12\x0f\n" +
	"\n" +
	"CHANGE_LID\x10\xd5\x01\x121\n" +
	",BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_IN_MESSAGE\x10\xd6\x01\x122\n" +
	"-BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_OUT_MESSAGE\x10\xd7\x01\x12\x19\n" +
	"\x14CHANGE_LIMIT_SHARING\x10\xd8\x01\x12\x1b\n" +
	"\x16GROUP_MEMBER_LINK_MODE\x10\xd9\x01\x122\n" +
	"-BIZ_AUTOMATICALLY_LABELED_CHAT_SYSTEM_MESSAGE\x10\xda\x01\x120\n" +
	"+PHONE_NUMBER_HIDING_CHAT_DEPRECATED_MESSAGE\x10\xdb\x01\"X\n" +
	"\x06Status\x12\t\n" +
	"\x05ERROR\x10\x00\x12\v\n" +
	"\aPENDING\x10\x01\x12\x0e\n" +
	"\n" +
	"SERVER_ACK\x10\x02\x12\x10\n" +
	"\fDELIVERY_ACK\x10\x03\x12\b\n" +
	"\x04READ\x10\x04\x12\n" +
	"\n" +
	"\x06PLAYED\x10\x05\"\xd9\f\n" +
	"\vPaymentInfo\x12W\n" +
	"\x12currencyDeprecated\x18\x01 \x01(\x0e2'.WAWebProtobufsWeb.PaymentInfo.CurrencyR\x12currencyDeprecated\x12\x1e\n" +
	"\n" +
	"amount1000\x18\x02 \x01(\x04R\n" +
	"amount1000\x12 \n" +
	"\vreceiverJID\x18\x03 \x01(\tR\vreceiverJID\x12=\n" +
	"\x06status\x18\x04 \x01(\x0e2%.WAWebProtobufsWeb.PaymentInfo.StatusR\x06status\x122\n" +
	"\x14transactionTimestamp\x18\x05 \x01(\x04R\x14transactionTimestamp\x12B\n" +
	"\x11requestMessageKey\x18\x06 \x01(\v2\x14.WACommon.MessageKeyR\x11requestMessageKey\x12(\n" +
	"\x0fexpiryTimestamp\x18\a \x01(\x04R\x0fexpiryTimestamp\x12$\n" +
	"\rfutureproofed\x18\b \x01(\bR\rfutureproofed\x12\x1a\n" +
	"\bcurrency\x18\t \x01(\tR\bcurrency\x12F\n" +
	"\ttxnStatus\x18\n" +
	" \x01(\x0e2(.WAWebProtobufsWeb.PaymentInfo.TxnStatusR\ttxnStatus\x12,\n" +
	"\x11useNoviFiatFormat\x18\v \x01(\bR\x11useNoviFiatFormat\x12>\n" +
	"\rprimaryAmount\x18\f \x01(\v2\x18.WAWebProtobufsE2E.MoneyR\rprimaryAmount\x12@\n" +
	"\x0eexchangeAmount\x18\r \x01(\v2\x18.WAWebProtobufsE2E.MoneyR\x0eexchangeAmount\"\x99\x05\n" +
	"\tTxnStatus\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\x11\n" +
	"\rPENDING_SETUP\x10\x01\x12\x1a\n" +
	"\x16PENDING_RECEIVER_SETUP\x10\x02\x12\b\n" +
	"\x04INIT\x10\x03\x12\v\n" +
	"\aSUCCESS\x10\x04\x12\r\n" +
	"\tCOMPLETED\x10\x05\x12\n" +
	"\n" +
	"\x06FAILED\x10\x06\x12\x0f\n" +
	"\vFAILED_RISK\x10\a\x12\x15\n" +
	"\x11FAILED_PROCESSING\x10\b\x12\x1e\n" +
	"\x1aFAILED_RECEIVER_PROCESSING\x10\t\x12\r\n" +
	"\tFAILED_DA\x10\n" +
	"\x12\x13\n" +
	"\x0fFAILED_DA_FINAL\x10\v\x12\x10\n" +
	"\fREFUNDED_TXN\x10\f\x12\x11\n" +
	"\rREFUND_FAILED\x10\r\x12\x1c\n" +
	"\x18REFUND_FAILED_PROCESSING\x10\x0e\x12\x14\n" +
	"\x10REFUND_FAILED_DA\x10\x0f\x12\x0f\n" +
	"\vEXPIRED_TXN\x10\x10\x12\x11\n" +
	"\rAUTH_CANCELED\x10\x11\x12!\n" +
	"\x1dAUTH_CANCEL_FAILED_PROCESSING\x10\x12\x12\x16\n" +
	"\x12AUTH_CANCEL_FAILED\x10\x13\x12\x10\n" +
	"\fCOLLECT_INIT\x10\x14\x12\x13\n" +
	"\x0fCOLLECT_SUCCESS\x10\x15\x12\x12\n" +
	"\x0eCOLLECT_FAILED\x10\x16\x12\x17\n" +
	"\x13COLLECT_FAILED_RISK\x10\x17\x12\x14\n" +
	"\x10COLLECT_REJECTED\x10\x18\x12\x13\n" +
	"\x0fCOLLECT_EXPIRED\x10\x19\x12\x14\n" +
	"\x10COLLECT_CANCELED\x10\x1a\x12\x16\n" +
	"\x12COLLECT_CANCELLING\x10\x1b\x12\r\n" +
	"\tIN_REVIEW\x10\x1c\x12\x14\n" +
	"\x10REVERSAL_SUCCESS\x10\x1d\x12\x14\n" +
	"\x10REVERSAL_PENDING\x10\x1e\x12\x12\n" +
	"\x0eREFUND_PENDING\x10\x1f\"\xcc\x01\n" +
	"\x06Status\x12\x12\n" +
	"\x0eUNKNOWN_STATUS\x10\x00\x12\x0e\n" +
	"\n" +
	"PROCESSING\x10\x01\x12\b\n" +
	"\x04SENT\x10\x02\x12\x12\n" +
	"\x0eNEED_TO_ACCEPT\x10\x03\x12\f\n" +
	"\bCOMPLETE\x10\x04\x12\x16\n" +
	"\x12COULD_NOT_COMPLETE\x10\x05\x12\f\n" +
	"\bREFUNDED\x10\x06\x12\v\n" +
	"\aEXPIRED\x10\a\x12\f\n" +
	"\bREJECTED\x10\b\x12\r\n" +
	"\tCANCELLED\x10\t\x12\x15\n" +
	"\x11WAITING_FOR_PAYER\x10\n" +
	"\x12\v\n" +
	"\aWAITING\x10\v\")\n" +
	"\bCurrency\x12\x14\n" +
	"\x10UNKNOWN_CURRENCY\x10\x00\x12\a\n" +
	"\x03INR\x10\x01\"\xab\x1d\n" +
	"\vWebFeatures\x12I\n" +
	"\rlabelsDisplay\x18\x01 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\rlabelsDisplay\x12[\n" +
	"\x16voipIndividualOutgoing\x18\x02 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x16voipIndividualOutgoing\x12?\n" +
	"\bgroupsV3\x18\x03 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\bgroupsV3\x12K\n" +
	"\x0egroupsV3Create\x18\x04 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x0egroupsV3Create\x12K\n" +
	"\x0echangeNumberV2\x18\x05 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x0echangeNumberV2\x12[\n" +
	"\x16queryStatusV3Thumbnail\x18\x06 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x16queryStatusV3Thumbnail\x12I\n" +
	"\rliveLocations\x18\a \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\rliveLocations\x12C\n" +
	"\n" +
	"queryVname\x18\b \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\n" +
	"queryVname\x12[\n" +
	"\x16voipIndividualIncoming\x18\t \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x16voipIndividualIncoming\x12Q\n" +
	"\x11quickRepliesQuery\x18\n" +
	" \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x11quickRepliesQuery\x12?\n" +
	"\bpayments\x18\v \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\bpayments\x12O\n" +
	"\x10stickerPackQuery\x18\f \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x10stickerPackQuery\x12S\n" +
	"\x12liveLocationsFinal\x18\r \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x12liveLocationsFinal\x12C\n" +
	"\n" +
	"labelsEdit\x18\x0e \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\n" +
	"labelsEdit\x12E\n" +
	"\vmediaUpload\x18\x0f \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\vmediaUpload\x12e\n" +
	"\x1bmediaUploadRichQuickReplies\x18\x12 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x1bmediaUploadRichQuickReplies\x12=\n" +
	"\avnameV2\x18\x13 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\avnameV2\x12O\n" +
	"\x10videoPlaybackURL\x18\x14 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x10videoPlaybackURL\x12I\n" +
	"\rstatusRanking\x18\x15 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\rstatusRanking\x12U\n" +
	"\x13voipIndividualVideo\x18\x16 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x13voipIndividualVideo\x12S\n" +
	"\x12thirdPartyStickers\x18\x17 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x12thirdPartyStickers\x12c\n" +
	"\x1afrequentlyForwardedSetting\x18\x18 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x1afrequentlyForwardedSetting\x12[\n" +
	"\x16groupsV4JoinPermission\x18\x19 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x16groupsV4JoinPermission\x12K\n" +
	"\x0erecentStickers\x18\x1a \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x0erecentStickers\x12=\n" +
	"\acatalog\x18\x1b \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\acatalog\x12M\n" +
	"\x0fstarredStickers\x18\x1c \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x0fstarredStickers\x12I\n" +
	"\rvoipGroupCall\x18\x1d \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\rvoipGroupCall\x12M\n" +
	"\x0ftemplateMessage\x18\x1e \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x0ftemplateMessage\x12g\n" +
	"\x1ctemplateMessageInteractivity\x18\x1f \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x1ctemplateMessageInteractivity\x12Q\n" +
	"\x11ephemeralMessages\x18  \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x11ephemeralMessages\x12U\n" +
	"\x13e2ENotificationSync\x18! \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x13e2ENotificationSync\x12O\n" +
	"\x10recentStickersV2\x18\" \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x10recentStickersV2\x12O\n" +
	"\x10recentStickersV3\x18$ \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x10recentStickersV3\x12C\n" +
	"\n" +
	"userNotice\x18% \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\n" +
	"userNotice\x12=\n" +
	"\asupport\x18' \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\asupport\x12M\n" +
	"\x0fgroupUiiCleanup\x18( \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x0fgroupUiiCleanup\x12e\n" +
	"\x1bgroupDogfoodingInternalOnly\x18) \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x1bgroupDogfoodingInternalOnly\x12G\n" +
	"\fsettingsSync\x18* \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\fsettingsSync\x12A\n" +
	"\tarchiveV2\x18+ \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\tarchiveV2\x12c\n" +
	"\x1aephemeralAllowGroupMembers\x18, \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x1aephemeralAllowGroupMembers\x12W\n" +
	"\x14ephemeral24HDuration\x18- \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x14ephemeral24HDuration\x12K\n" +
	"\x0emdForceUpgrade\x18. \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x0emdForceUpgrade\x12O\n" +
	"\x10disappearingMode\x18/ \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x10disappearingMode\x12_\n" +
	"\x18externalMdOptInAvailable\x180 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x18externalMdOptInAvailable\x12_\n" +
	"\x18noDeleteMessageTimeLimit\x181 \x01(\x0e2#.WAWebProtobufsWeb.WebFeatures.FlagR\x18noDeleteMessageTimeLimit\"K\n" +
	"\x04Flag\x12\x0f\n" +
	"\vNOT_STARTED\x10\x00\x12\x11\n" +
	"\rFORCE_UPGRADE\x10\x01\x12\x0f\n" +
	"\vDEVELOPMENT\x10\x02\x12\x0e\n" +
	"\n" +
	"PRODUCTION\x10\x03\"\xea\x02\n" +
	"\tPinInChat\x125\n" +
	"\x04type\x18\x01 \x01(\x0e2!.WAWebProtobufsWeb.PinInChat.TypeR\x04type\x12&\n" +
	"\x03key\x18\x02 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x12,\n" +
	"\x11senderTimestampMS\x18\x03 \x01(\x03R\x11senderTimestampMS\x12,\n" +
	"\x11serverTimestampMS\x18\x04 \x01(\x03R\x11serverTimestampMS\x12d\n" +
	"\x17messageAddOnContextInfo\x18\x05 \x01(\v2*.WAWebProtobufsWeb.MessageAddOnContextInfoR\x17messageAddOnContextInfo\"<\n" +
	"\x04Type\x12\x10\n" +
	"\fUNKNOWN_TYPE\x10\x00\x12\x0f\n" +
	"\vPIN_FOR_ALL\x10\x01\x12\x11\n" +
	"\rUNPIN_FOR_ALL\x10\x02\"\x91\x05\n" +
	"\fMessageAddOn\x12\\\n" +
	"\x10messageAddOnType\x18\x01 \x01(\x0e20.WAWebProtobufsWeb.MessageAddOn.MessageAddOnTypeR\x10messageAddOnType\x12>\n" +
	"\fmessageAddOn\x18\x02 \x01(\v2\x1a.WAWebProtobufsE2E.MessageR\fmessageAddOn\x12,\n" +
	"\x11senderTimestampMS\x18\x03 \x01(\x03R\x11senderTimestampMS\x12,\n" +
	"\x11serverTimestampMS\x18\x04 \x01(\x03R\x11serverTimestampMS\x12@\n" +
	"\x06status\x18\x05 \x01(\x0e2(.WAWebProtobufsWeb.WebMessageInfo.StatusR\x06status\x12V\n" +
	"\x10addOnContextInfo\x18\x06 \x01(\v2*.WAWebProtobufsWeb.MessageAddOnContextInfoR\x10addOnContextInfo\x12>\n" +
	"\x0fmessageAddOnKey\x18\a \x01(\v2\x14.WACommon.MessageKeyR\x0fmessageAddOnKey\x12F\n" +
	"\rlegacyMessage\x18\b \x01(\v2 .WAWebProtobufsWeb.LegacyMessageR\rlegacyMessage\"e\n" +
	"\x10MessageAddOnType\x12\r\n" +
	"\tUNDEFINED\x10\x00\x12\f\n" +
	"\bREACTION\x10\x01\x12\x12\n" +
	"\x0eEVENT_RESPONSE\x10\x02\x12\x0f\n" +
	"\vPOLL_UPDATE\x10\x03\x12\x0f\n" +
	"\vPIN_IN_CHAT\x10\x04\"s\n" +
	"\x0fCommentMetadata\x12@\n" +
	"\x10commentParentKey\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x10commentParentKey\x12\x1e\n" +
	"\n" +
	"replyCount\x18\x02 \x01(\rR\n" +
	"replyCount\"\xd1\x01\n" +
	"\x14WebNotificationsInfo\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x04R\ttimestamp\x12 \n" +
	"\vunreadChats\x18\x03 \x01(\rR\vunreadChats\x12.\n" +
	"\x12notifyMessageCount\x18\x04 \x01(\rR\x12notifyMessageCount\x12I\n" +
	"\x0enotifyMessages\x18\x05 \x03(\v2!.WAWebProtobufsWeb.WebMessageInfoR\x0enotifyMessages\"\xc5\x01\n" +
	"\x17NotificationMessageInfo\x12&\n" +
	"\x03key\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x124\n" +
	"\amessage\x18\x02 \x01(\v2\x1a.WAWebProtobufsE2E.MessageR\amessage\x12*\n" +
	"\x10messageTimestamp\x18\x03 \x01(\x04R\x10messageTimestamp\x12 \n" +
	"\vparticipant\x18\x04 \x01(\tR\vparticipant\"8\n" +
	"\x12ReportingTokenInfo\x12\"\n" +
	"\freportingTag\x18\x01 \x01(\fR\freportingTag\")\n" +
	"\tMediaData\x12\x1c\n" +
	"\tlocalPath\x18\x01 \x01(\tR\tlocalPath\"e\n" +
	"\vPhotoChange\x12\x1a\n" +
	"\boldPhoto\x18\x01 \x01(\fR\boldPhoto\x12\x1a\n" +
	"\bnewPhoto\x18\x02 \x01(\fR\bnewPhoto\x12\x1e\n" +
	"\n" +
	"newPhotoID\x18\x03 \x01(\rR\n" +
	"newPhotoID\"m\n" +
	"\tStatusPSA\x12\x1e\n" +
	"\n" +
	"campaignID\x18, \x02(\x04R\n" +
	"campaignID\x12@\n" +
	"\x1bcampaignExpirationTimestamp\x18- \x01(\x04R\x1bcampaignExpirationTimestamp\"\xff\x01\n" +
	"\vUserReceipt\x12\x18\n" +
	"\auserJID\x18\x01 \x02(\tR\auserJID\x12*\n" +
	"\x10receiptTimestamp\x18\x02 \x01(\x03R\x10receiptTimestamp\x12$\n" +
	"\rreadTimestamp\x18\x03 \x01(\x03R\rreadTimestamp\x12(\n" +
	"\x0fplayedTimestamp\x18\x04 \x01(\x03R\x0fplayedTimestamp\x12*\n" +
	"\x10pendingDeviceJID\x18\x05 \x03(\tR\x10pendingDeviceJID\x12.\n" +
	"\x12deliveredDeviceJID\x18\x06 \x03(\tR\x12deliveredDeviceJID\"\xae\x01\n" +
	"\bReaction\x12&\n" +
	"\x03key\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12 \n" +
	"\vgroupingKey\x18\x03 \x01(\tR\vgroupingKey\x12,\n" +
	"\x11senderTimestampMS\x18\x04 \x01(\x03R\x11senderTimestampMS\x12\x16\n" +
	"\x06unread\x18\x05 \x01(\bR\x06unread\"\x82\x02\n" +
	"\n" +
	"PollUpdate\x12H\n" +
	"\x14pollUpdateMessageKey\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x14pollUpdateMessageKey\x126\n" +
	"\x04vote\x18\x02 \x01(\v2\".WAWebProtobufsE2E.PollVoteMessageR\x04vote\x12,\n" +
	"\x11senderTimestampMS\x18\x03 \x01(\x03R\x11senderTimestampMS\x12,\n" +
	"\x11serverTimestampMS\x18\x04 \x01(\x03R\x11serverTimestampMS\x12\x16\n" +
	"\x06unread\x18\x05 \x01(\bR\x06unread\"B\n" +
	"\x16PollAdditionalMetadata\x12(\n" +
	"\x0fpollInvalidated\x18\x01 \x01(\bR\x0fpollInvalidated\"3\n" +
	"\x17EventAdditionalMetadata\x12\x18\n" +
	"\aisStale\x18\x01 \x01(\bR\aisStale\"\x91\x02\n" +
	"\n" +
	"KeepInChat\x127\n" +
	"\bkeepType\x18\x01 \x01(\x0e2\x1b.WAWebProtobufsE2E.KeepTypeR\bkeepType\x12(\n" +
	"\x0fserverTimestamp\x18\x02 \x01(\x03R\x0fserverTimestamp\x12&\n" +
	"\x03key\x18\x03 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x12\x1c\n" +
	"\tdeviceJID\x18\x04 \x01(\tR\tdeviceJID\x12,\n" +
	"\x11clientTimestampMS\x18\x05 \x01(\x03R\x11clientTimestampMS\x12,\n" +
	"\x11serverTimestampMS\x18\x06 \x01(\x03R\x11serverTimestampMS\"\xcf\x01\n" +
	"\x17MessageAddOnContextInfo\x12>\n" +
	"\x1amessageAddOnDurationInSecs\x18\x01 \x01(\rR\x1amessageAddOnDurationInSecs\x12t\n" +
	"\x16messageAddOnExpiryType\x18\x02 \x01(\x0e2<.WAWebProtobufsE2E.MessageContextInfo.MessageAddonExpiryTypeR\x16messageAddOnExpiryType\"@\n" +
	"\x12PremiumMessageInfo\x12*\n" +
	"\x10serverCampaignID\x18\x01 \x01(\tR\x10serverCampaignID\"\xf6\x01\n" +
	"\rEventResponse\x12N\n" +
	"\x17eventResponseMessageKey\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x17eventResponseMessageKey\x12 \n" +
	"\vtimestampMS\x18\x02 \x01(\x03R\vtimestampMS\x12[\n" +
	"\x14eventResponseMessage\x18\x03 \x01(\v2'.WAWebProtobufsE2E.EventResponseMessageR\x14eventResponseMessage\x12\x16\n" +
	"\x06unread\x18\x04 \x01(\bR\x06unread\"\xac\x01\n" +
	"\rLegacyMessage\x12[\n" +
	"\x14eventResponseMessage\x18\x01 \x01(\v2'.WAWebProtobufsE2E.EventResponseMessageR\x14eventResponseMessage\x12>\n" +
	"\bpollVote\x18\x02 \x01(\v2\".WAWebProtobufsE2E.PollVoteMessageR\bpollVote\"V\n" +
	"\x14StatusMentionMessage\x12>\n" +
	"\fquotedStatus\x18\x01 \x01(\v2\x1a.WAWebProtobufsE2E.MessageR\fquotedStatus\"n\n" +
	"\bCitation\x12\x14\n" +
	"\x05title\x18\x01 \x02(\tR\x05title\x12\x1a\n" +
	"\bsubtitle\x18\x02 \x02(\tR\bsubtitle\x12\x14\n" +
	"\x05cmsID\x18\x03 \x02(\tR\x05cmsID\x12\x1a\n" +
	"\bimageURL\x18\x04 \x02(\tR\bimageURLB!Z\x1fgo.mau.fi/whatsmeow/proto/waWeb"

var (
	file_waWeb_WAWebProtobufsWeb_proto_rawDescOnce sync.Once
	file_waWeb_WAWebProtobufsWeb_proto_rawDescData []byte
)

func file_waWeb_WAWebProtobufsWeb_proto_rawDescGZIP() []byte {
	file_waWeb_WAWebProtobufsWeb_proto_rawDescOnce.Do(func() {
		file_waWeb_WAWebProtobufsWeb_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waWeb_WAWebProtobufsWeb_proto_rawDesc), len(file_waWeb_WAWebProtobufsWeb_proto_rawDesc)))
	})
	return file_waWeb_WAWebProtobufsWeb_proto_rawDescData
}

var file_waWeb_WAWebProtobufsWeb_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_waWeb_WAWebProtobufsWeb_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_waWeb_WAWebProtobufsWeb_proto_goTypes = []any{
	(WebMessageInfo_BizPrivacyStatus)(0),                 // 0: WAWebProtobufsWeb.WebMessageInfo.BizPrivacyStatus
	(WebMessageInfo_StubType)(0),                         // 1: WAWebProtobufsWeb.WebMessageInfo.StubType
	(WebMessageInfo_Status)(0),                           // 2: WAWebProtobufsWeb.WebMessageInfo.Status
	(PaymentInfo_TxnStatus)(0),                           // 3: WAWebProtobufsWeb.PaymentInfo.TxnStatus
	(PaymentInfo_Status)(0),                              // 4: WAWebProtobufsWeb.PaymentInfo.Status
	(PaymentInfo_Currency)(0),                            // 5: WAWebProtobufsWeb.PaymentInfo.Currency
	(WebFeatures_Flag)(0),                                // 6: WAWebProtobufsWeb.WebFeatures.Flag
	(PinInChat_Type)(0),                                  // 7: WAWebProtobufsWeb.PinInChat.Type
	(MessageAddOn_MessageAddOnType)(0),                   // 8: WAWebProtobufsWeb.MessageAddOn.MessageAddOnType
	(*WebMessageInfo)(nil),                               // 9: WAWebProtobufsWeb.WebMessageInfo
	(*PaymentInfo)(nil),                                  // 10: WAWebProtobufsWeb.PaymentInfo
	(*WebFeatures)(nil),                                  // 11: WAWebProtobufsWeb.WebFeatures
	(*PinInChat)(nil),                                    // 12: WAWebProtobufsWeb.PinInChat
	(*MessageAddOn)(nil),                                 // 13: WAWebProtobufsWeb.MessageAddOn
	(*CommentMetadata)(nil),                              // 14: WAWebProtobufsWeb.CommentMetadata
	(*WebNotificationsInfo)(nil),                         // 15: WAWebProtobufsWeb.WebNotificationsInfo
	(*NotificationMessageInfo)(nil),                      // 16: WAWebProtobufsWeb.NotificationMessageInfo
	(*ReportingTokenInfo)(nil),                           // 17: WAWebProtobufsWeb.ReportingTokenInfo
	(*MediaData)(nil),                                    // 18: WAWebProtobufsWeb.MediaData
	(*PhotoChange)(nil),                                  // 19: WAWebProtobufsWeb.PhotoChange
	(*StatusPSA)(nil),                                    // 20: WAWebProtobufsWeb.StatusPSA
	(*UserReceipt)(nil),                                  // 21: WAWebProtobufsWeb.UserReceipt
	(*Reaction)(nil),                                     // 22: WAWebProtobufsWeb.Reaction
	(*PollUpdate)(nil),                                   // 23: WAWebProtobufsWeb.PollUpdate
	(*PollAdditionalMetadata)(nil),                       // 24: WAWebProtobufsWeb.PollAdditionalMetadata
	(*EventAdditionalMetadata)(nil),                      // 25: WAWebProtobufsWeb.EventAdditionalMetadata
	(*KeepInChat)(nil),                                   // 26: WAWebProtobufsWeb.KeepInChat
	(*MessageAddOnContextInfo)(nil),                      // 27: WAWebProtobufsWeb.MessageAddOnContextInfo
	(*PremiumMessageInfo)(nil),                           // 28: WAWebProtobufsWeb.PremiumMessageInfo
	(*EventResponse)(nil),                                // 29: WAWebProtobufsWeb.EventResponse
	(*LegacyMessage)(nil),                                // 30: WAWebProtobufsWeb.LegacyMessage
	(*StatusMentionMessage)(nil),                         // 31: WAWebProtobufsWeb.StatusMentionMessage
	(*Citation)(nil),                                     // 32: WAWebProtobufsWeb.Citation
	(*waCommon.MessageKey)(nil),                          // 33: WACommon.MessageKey
	(*waE2E.Message)(nil),                                // 34: WAWebProtobufsE2E.Message
	(*waE2E.LiveLocationMessage)(nil),                    // 35: WAWebProtobufsE2E.LiveLocationMessage
	(*waE2E.Money)(nil),                                  // 36: WAWebProtobufsE2E.Money
	(*waE2E.PollVoteMessage)(nil),                        // 37: WAWebProtobufsE2E.PollVoteMessage
	(waE2E.KeepType)(0),                                  // 38: WAWebProtobufsE2E.KeepType
	(waE2E.MessageContextInfo_MessageAddonExpiryType)(0), // 39: WAWebProtobufsE2E.MessageContextInfo.MessageAddonExpiryType
	(*waE2E.EventResponseMessage)(nil),                   // 40: WAWebProtobufsE2E.EventResponseMessage
}
var file_waWeb_WAWebProtobufsWeb_proto_depIdxs = []int32{
	33,  // 0: WAWebProtobufsWeb.WebMessageInfo.key:type_name -> WACommon.MessageKey
	34,  // 1: WAWebProtobufsWeb.WebMessageInfo.message:type_name -> WAWebProtobufsE2E.Message
	2,   // 2: WAWebProtobufsWeb.WebMessageInfo.status:type_name -> WAWebProtobufsWeb.WebMessageInfo.Status
	1,   // 3: WAWebProtobufsWeb.WebMessageInfo.messageStubType:type_name -> WAWebProtobufsWeb.WebMessageInfo.StubType
	10,  // 4: WAWebProtobufsWeb.WebMessageInfo.paymentInfo:type_name -> WAWebProtobufsWeb.PaymentInfo
	35,  // 5: WAWebProtobufsWeb.WebMessageInfo.finalLiveLocation:type_name -> WAWebProtobufsE2E.LiveLocationMessage
	10,  // 6: WAWebProtobufsWeb.WebMessageInfo.quotedPaymentInfo:type_name -> WAWebProtobufsWeb.PaymentInfo
	0,   // 7: WAWebProtobufsWeb.WebMessageInfo.bizPrivacyStatus:type_name -> WAWebProtobufsWeb.WebMessageInfo.BizPrivacyStatus
	18,  // 8: WAWebProtobufsWeb.WebMessageInfo.mediaData:type_name -> WAWebProtobufsWeb.MediaData
	19,  // 9: WAWebProtobufsWeb.WebMessageInfo.photoChange:type_name -> WAWebProtobufsWeb.PhotoChange
	21,  // 10: WAWebProtobufsWeb.WebMessageInfo.userReceipt:type_name -> WAWebProtobufsWeb.UserReceipt
	22,  // 11: WAWebProtobufsWeb.WebMessageInfo.reactions:type_name -> WAWebProtobufsWeb.Reaction
	18,  // 12: WAWebProtobufsWeb.WebMessageInfo.quotedStickerData:type_name -> WAWebProtobufsWeb.MediaData
	20,  // 13: WAWebProtobufsWeb.WebMessageInfo.statusPsa:type_name -> WAWebProtobufsWeb.StatusPSA
	23,  // 14: WAWebProtobufsWeb.WebMessageInfo.pollUpdates:type_name -> WAWebProtobufsWeb.PollUpdate
	24,  // 15: WAWebProtobufsWeb.WebMessageInfo.pollAdditionalMetadata:type_name -> WAWebProtobufsWeb.PollAdditionalMetadata
	26,  // 16: WAWebProtobufsWeb.WebMessageInfo.keepInChat:type_name -> WAWebProtobufsWeb.KeepInChat
	12,  // 17: WAWebProtobufsWeb.WebMessageInfo.pinInChat:type_name -> WAWebProtobufsWeb.PinInChat
	28,  // 18: WAWebProtobufsWeb.WebMessageInfo.premiumMessageInfo:type_name -> WAWebProtobufsWeb.PremiumMessageInfo
	14,  // 19: WAWebProtobufsWeb.WebMessageInfo.commentMetadata:type_name -> WAWebProtobufsWeb.CommentMetadata
	29,  // 20: WAWebProtobufsWeb.WebMessageInfo.eventResponses:type_name -> WAWebProtobufsWeb.EventResponse
	17,  // 21: WAWebProtobufsWeb.WebMessageInfo.reportingTokenInfo:type_name -> WAWebProtobufsWeb.ReportingTokenInfo
	25,  // 22: WAWebProtobufsWeb.WebMessageInfo.eventAdditionalMetadata:type_name -> WAWebProtobufsWeb.EventAdditionalMetadata
	33,  // 23: WAWebProtobufsWeb.WebMessageInfo.targetMessageID:type_name -> WACommon.MessageKey
	13,  // 24: WAWebProtobufsWeb.WebMessageInfo.messageAddOns:type_name -> WAWebProtobufsWeb.MessageAddOn
	31,  // 25: WAWebProtobufsWeb.WebMessageInfo.statusMentionMessageInfo:type_name -> WAWebProtobufsWeb.StatusMentionMessage
	32,  // 26: WAWebProtobufsWeb.WebMessageInfo.supportAiCitations:type_name -> WAWebProtobufsWeb.Citation
	5,   // 27: WAWebProtobufsWeb.PaymentInfo.currencyDeprecated:type_name -> WAWebProtobufsWeb.PaymentInfo.Currency
	4,   // 28: WAWebProtobufsWeb.PaymentInfo.status:type_name -> WAWebProtobufsWeb.PaymentInfo.Status
	33,  // 29: WAWebProtobufsWeb.PaymentInfo.requestMessageKey:type_name -> WACommon.MessageKey
	3,   // 30: WAWebProtobufsWeb.PaymentInfo.txnStatus:type_name -> WAWebProtobufsWeb.PaymentInfo.TxnStatus
	36,  // 31: WAWebProtobufsWeb.PaymentInfo.primaryAmount:type_name -> WAWebProtobufsE2E.Money
	36,  // 32: WAWebProtobufsWeb.PaymentInfo.exchangeAmount:type_name -> WAWebProtobufsE2E.Money
	6,   // 33: WAWebProtobufsWeb.WebFeatures.labelsDisplay:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 34: WAWebProtobufsWeb.WebFeatures.voipIndividualOutgoing:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 35: WAWebProtobufsWeb.WebFeatures.groupsV3:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 36: WAWebProtobufsWeb.WebFeatures.groupsV3Create:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 37: WAWebProtobufsWeb.WebFeatures.changeNumberV2:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 38: WAWebProtobufsWeb.WebFeatures.queryStatusV3Thumbnail:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 39: WAWebProtobufsWeb.WebFeatures.liveLocations:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 40: WAWebProtobufsWeb.WebFeatures.queryVname:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 41: WAWebProtobufsWeb.WebFeatures.voipIndividualIncoming:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 42: WAWebProtobufsWeb.WebFeatures.quickRepliesQuery:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 43: WAWebProtobufsWeb.WebFeatures.payments:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 44: WAWebProtobufsWeb.WebFeatures.stickerPackQuery:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 45: WAWebProtobufsWeb.WebFeatures.liveLocationsFinal:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 46: WAWebProtobufsWeb.WebFeatures.labelsEdit:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 47: WAWebProtobufsWeb.WebFeatures.mediaUpload:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 48: WAWebProtobufsWeb.WebFeatures.mediaUploadRichQuickReplies:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 49: WAWebProtobufsWeb.WebFeatures.vnameV2:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 50: WAWebProtobufsWeb.WebFeatures.videoPlaybackURL:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 51: WAWebProtobufsWeb.WebFeatures.statusRanking:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 52: WAWebProtobufsWeb.WebFeatures.voipIndividualVideo:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 53: WAWebProtobufsWeb.WebFeatures.thirdPartyStickers:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 54: WAWebProtobufsWeb.WebFeatures.frequentlyForwardedSetting:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 55: WAWebProtobufsWeb.WebFeatures.groupsV4JoinPermission:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 56: WAWebProtobufsWeb.WebFeatures.recentStickers:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 57: WAWebProtobufsWeb.WebFeatures.catalog:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 58: WAWebProtobufsWeb.WebFeatures.starredStickers:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 59: WAWebProtobufsWeb.WebFeatures.voipGroupCall:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 60: WAWebProtobufsWeb.WebFeatures.templateMessage:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 61: WAWebProtobufsWeb.WebFeatures.templateMessageInteractivity:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 62: WAWebProtobufsWeb.WebFeatures.ephemeralMessages:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 63: WAWebProtobufsWeb.WebFeatures.e2ENotificationSync:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 64: WAWebProtobufsWeb.WebFeatures.recentStickersV2:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 65: WAWebProtobufsWeb.WebFeatures.recentStickersV3:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 66: WAWebProtobufsWeb.WebFeatures.userNotice:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 67: WAWebProtobufsWeb.WebFeatures.support:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 68: WAWebProtobufsWeb.WebFeatures.groupUiiCleanup:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 69: WAWebProtobufsWeb.WebFeatures.groupDogfoodingInternalOnly:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 70: WAWebProtobufsWeb.WebFeatures.settingsSync:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 71: WAWebProtobufsWeb.WebFeatures.archiveV2:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 72: WAWebProtobufsWeb.WebFeatures.ephemeralAllowGroupMembers:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 73: WAWebProtobufsWeb.WebFeatures.ephemeral24HDuration:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 74: WAWebProtobufsWeb.WebFeatures.mdForceUpgrade:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 75: WAWebProtobufsWeb.WebFeatures.disappearingMode:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 76: WAWebProtobufsWeb.WebFeatures.externalMdOptInAvailable:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	6,   // 77: WAWebProtobufsWeb.WebFeatures.noDeleteMessageTimeLimit:type_name -> WAWebProtobufsWeb.WebFeatures.Flag
	7,   // 78: WAWebProtobufsWeb.PinInChat.type:type_name -> WAWebProtobufsWeb.PinInChat.Type
	33,  // 79: WAWebProtobufsWeb.PinInChat.key:type_name -> WACommon.MessageKey
	27,  // 80: WAWebProtobufsWeb.PinInChat.messageAddOnContextInfo:type_name -> WAWebProtobufsWeb.MessageAddOnContextInfo
	8,   // 81: WAWebProtobufsWeb.MessageAddOn.messageAddOnType:type_name -> WAWebProtobufsWeb.MessageAddOn.MessageAddOnType
	34,  // 82: WAWebProtobufsWeb.MessageAddOn.messageAddOn:type_name -> WAWebProtobufsE2E.Message
	2,   // 83: WAWebProtobufsWeb.MessageAddOn.status:type_name -> WAWebProtobufsWeb.WebMessageInfo.Status
	27,  // 84: WAWebProtobufsWeb.MessageAddOn.addOnContextInfo:type_name -> WAWebProtobufsWeb.MessageAddOnContextInfo
	33,  // 85: WAWebProtobufsWeb.MessageAddOn.messageAddOnKey:type_name -> WACommon.MessageKey
	30,  // 86: WAWebProtobufsWeb.MessageAddOn.legacyMessage:type_name -> WAWebProtobufsWeb.LegacyMessage
	33,  // 87: WAWebProtobufsWeb.CommentMetadata.commentParentKey:type_name -> WACommon.MessageKey
	9,   // 88: WAWebProtobufsWeb.WebNotificationsInfo.notifyMessages:type_name -> WAWebProtobufsWeb.WebMessageInfo
	33,  // 89: WAWebProtobufsWeb.NotificationMessageInfo.key:type_name -> WACommon.MessageKey
	34,  // 90: WAWebProtobufsWeb.NotificationMessageInfo.message:type_name -> WAWebProtobufsE2E.Message
	33,  // 91: WAWebProtobufsWeb.Reaction.key:type_name -> WACommon.MessageKey
	33,  // 92: WAWebProtobufsWeb.PollUpdate.pollUpdateMessageKey:type_name -> WACommon.MessageKey
	37,  // 93: WAWebProtobufsWeb.PollUpdate.vote:type_name -> WAWebProtobufsE2E.PollVoteMessage
	38,  // 94: WAWebProtobufsWeb.KeepInChat.keepType:type_name -> WAWebProtobufsE2E.KeepType
	33,  // 95: WAWebProtobufsWeb.KeepInChat.key:type_name -> WACommon.MessageKey
	39,  // 96: WAWebProtobufsWeb.MessageAddOnContextInfo.messageAddOnExpiryType:type_name -> WAWebProtobufsE2E.MessageContextInfo.MessageAddonExpiryType
	33,  // 97: WAWebProtobufsWeb.EventResponse.eventResponseMessageKey:type_name -> WACommon.MessageKey
	40,  // 98: WAWebProtobufsWeb.EventResponse.eventResponseMessage:type_name -> WAWebProtobufsE2E.EventResponseMessage
	40,  // 99: WAWebProtobufsWeb.LegacyMessage.eventResponseMessage:type_name -> WAWebProtobufsE2E.EventResponseMessage
	37,  // 100: WAWebProtobufsWeb.LegacyMessage.pollVote:type_name -> WAWebProtobufsE2E.PollVoteMessage
	34,  // 101: WAWebProtobufsWeb.StatusMentionMessage.quotedStatus:type_name -> WAWebProtobufsE2E.Message
	102, // [102:102] is the sub-list for method output_type
	102, // [102:102] is the sub-list for method input_type
	102, // [102:102] is the sub-list for extension type_name
	102, // [102:102] is the sub-list for extension extendee
	0,   // [0:102] is the sub-list for field type_name
}

func init() { file_waWeb_WAWebProtobufsWeb_proto_init() }
func file_waWeb_WAWebProtobufsWeb_proto_init() {
	if File_waWeb_WAWebProtobufsWeb_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waWeb_WAWebProtobufsWeb_proto_rawDesc), len(file_waWeb_WAWebProtobufsWeb_proto_rawDesc)),
			NumEnums:      9,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waWeb_WAWebProtobufsWeb_proto_goTypes,
		DependencyIndexes: file_waWeb_WAWebProtobufsWeb_proto_depIdxs,
		EnumInfos:         file_waWeb_WAWebProtobufsWeb_proto_enumTypes,
		MessageInfos:      file_waWeb_WAWebProtobufsWeb_proto_msgTypes,
	}.Build()
	File_waWeb_WAWebProtobufsWeb_proto = out.File
	file_waWeb_WAWebProtobufsWeb_proto_goTypes = nil
	file_waWeb_WAWebProtobufsWeb_proto_depIdxs = nil
}
