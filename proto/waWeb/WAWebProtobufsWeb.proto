syntax = "proto2";
package WAWebProtobufsWeb;
option go_package = "go.mau.fi/whatsmeow/proto/waWeb";

import "waE2E/WAWebProtobufsE2E.proto";
import "waCommon/WACommon.proto";

message WebMessageInfo {
	enum BizPrivacyStatus {
		E2EE = 0;
		FB = 2;
		BSP = 1;
		BSP_AND_FB = 3;
	}

	enum StubType {
		UNKNOWN = 0;
		REVOKE = 1;
		CIPHERTEXT = 2;
		FUTUREPROOF = 3;
		NON_VERIFIED_TRANSITION = 4;
		UNVERIFIED_TRANSITION = 5;
		VERIFIED_TRANSITION = 6;
		VERIFIED_LOW_UNKNOWN = 7;
		VERIFIED_HIGH = 8;
		VERIFIED_INITIAL_UNKNOWN = 9;
		VERIFIED_INITIAL_LOW = 10;
		VERIFIED_INITIAL_HIGH = 11;
		VERIFIED_TRANSITION_ANY_TO_NONE = 12;
		VERIFIED_TRANSITION_ANY_TO_HIGH = 13;
		VERIFIED_TRANSITION_HIGH_TO_LOW = 14;
		VERIFIED_TRANSITION_HIGH_TO_UNKNOWN = 15;
		VERIFIED_TRANSITION_UNKNOWN_TO_LOW = 16;
		VERIFIED_TRANSITION_LOW_TO_UNKNOWN = 17;
		VERIFIED_TRANSITION_NONE_TO_LOW = 18;
		VERIFIED_TRANSITION_NONE_TO_UNKNOWN = 19;
		GROUP_CREATE = 20;
		GROUP_CHANGE_SUBJECT = 21;
		GROUP_CHANGE_ICON = 22;
		GROUP_CHANGE_INVITE_LINK = 23;
		GROUP_CHANGE_DESCRIPTION = 24;
		GROUP_CHANGE_RESTRICT = 25;
		GROUP_CHANGE_ANNOUNCE = 26;
		GROUP_PARTICIPANT_ADD = 27;
		GROUP_PARTICIPANT_REMOVE = 28;
		GROUP_PARTICIPANT_PROMOTE = 29;
		GROUP_PARTICIPANT_DEMOTE = 30;
		GROUP_PARTICIPANT_INVITE = 31;
		GROUP_PARTICIPANT_LEAVE = 32;
		GROUP_PARTICIPANT_CHANGE_NUMBER = 33;
		BROADCAST_CREATE = 34;
		BROADCAST_ADD = 35;
		BROADCAST_REMOVE = 36;
		GENERIC_NOTIFICATION = 37;
		E2E_IDENTITY_CHANGED = 38;
		E2E_ENCRYPTED = 39;
		CALL_MISSED_VOICE = 40;
		CALL_MISSED_VIDEO = 41;
		INDIVIDUAL_CHANGE_NUMBER = 42;
		GROUP_DELETE = 43;
		GROUP_ANNOUNCE_MODE_MESSAGE_BOUNCE = 44;
		CALL_MISSED_GROUP_VOICE = 45;
		CALL_MISSED_GROUP_VIDEO = 46;
		PAYMENT_CIPHERTEXT = 47;
		PAYMENT_FUTUREPROOF = 48;
		PAYMENT_TRANSACTION_STATUS_UPDATE_FAILED = 49;
		PAYMENT_TRANSACTION_STATUS_UPDATE_REFUNDED = 50;
		PAYMENT_TRANSACTION_STATUS_UPDATE_REFUND_FAILED = 51;
		PAYMENT_TRANSACTION_STATUS_RECEIVER_PENDING_SETUP = 52;
		PAYMENT_TRANSACTION_STATUS_RECEIVER_SUCCESS_AFTER_HICCUP = 53;
		PAYMENT_ACTION_ACCOUNT_SETUP_REMINDER = 54;
		PAYMENT_ACTION_SEND_PAYMENT_REMINDER = 55;
		PAYMENT_ACTION_SEND_PAYMENT_INVITATION = 56;
		PAYMENT_ACTION_REQUEST_DECLINED = 57;
		PAYMENT_ACTION_REQUEST_EXPIRED = 58;
		PAYMENT_ACTION_REQUEST_CANCELLED = 59;
		BIZ_VERIFIED_TRANSITION_TOP_TO_BOTTOM = 60;
		BIZ_VERIFIED_TRANSITION_BOTTOM_TO_TOP = 61;
		BIZ_INTRO_TOP = 62;
		BIZ_INTRO_BOTTOM = 63;
		BIZ_NAME_CHANGE = 64;
		BIZ_MOVE_TO_CONSUMER_APP = 65;
		BIZ_TWO_TIER_MIGRATION_TOP = 66;
		BIZ_TWO_TIER_MIGRATION_BOTTOM = 67;
		OVERSIZED = 68;
		GROUP_CHANGE_NO_FREQUENTLY_FORWARDED = 69;
		GROUP_V4_ADD_INVITE_SENT = 70;
		GROUP_PARTICIPANT_ADD_REQUEST_JOIN = 71;
		CHANGE_EPHEMERAL_SETTING = 72;
		E2E_DEVICE_CHANGED = 73;
		VIEWED_ONCE = 74;
		E2E_ENCRYPTED_NOW = 75;
		BLUE_MSG_BSP_FB_TO_BSP_PREMISE = 76;
		BLUE_MSG_BSP_FB_TO_SELF_FB = 77;
		BLUE_MSG_BSP_FB_TO_SELF_PREMISE = 78;
		BLUE_MSG_BSP_FB_UNVERIFIED = 79;
		BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED = 80;
		BLUE_MSG_BSP_FB_VERIFIED = 81;
		BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED = 82;
		BLUE_MSG_BSP_PREMISE_TO_SELF_PREMISE = 83;
		BLUE_MSG_BSP_PREMISE_UNVERIFIED = 84;
		BLUE_MSG_BSP_PREMISE_UNVERIFIED_TO_SELF_PREMISE_VERIFIED = 85;
		BLUE_MSG_BSP_PREMISE_VERIFIED = 86;
		BLUE_MSG_BSP_PREMISE_VERIFIED_TO_SELF_PREMISE_UNVERIFIED = 87;
		BLUE_MSG_CONSUMER_TO_BSP_FB_UNVERIFIED = 88;
		BLUE_MSG_CONSUMER_TO_BSP_PREMISE_UNVERIFIED = 89;
		BLUE_MSG_CONSUMER_TO_SELF_FB_UNVERIFIED = 90;
		BLUE_MSG_CONSUMER_TO_SELF_PREMISE_UNVERIFIED = 91;
		BLUE_MSG_SELF_FB_TO_BSP_PREMISE = 92;
		BLUE_MSG_SELF_FB_TO_SELF_PREMISE = 93;
		BLUE_MSG_SELF_FB_UNVERIFIED = 94;
		BLUE_MSG_SELF_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED = 95;
		BLUE_MSG_SELF_FB_VERIFIED = 96;
		BLUE_MSG_SELF_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED = 97;
		BLUE_MSG_SELF_PREMISE_TO_BSP_PREMISE = 98;
		BLUE_MSG_SELF_PREMISE_UNVERIFIED = 99;
		BLUE_MSG_SELF_PREMISE_VERIFIED = 100;
		BLUE_MSG_TO_BSP_FB = 101;
		BLUE_MSG_TO_CONSUMER = 102;
		BLUE_MSG_TO_SELF_FB = 103;
		BLUE_MSG_UNVERIFIED_TO_BSP_FB_VERIFIED = 104;
		BLUE_MSG_UNVERIFIED_TO_BSP_PREMISE_VERIFIED = 105;
		BLUE_MSG_UNVERIFIED_TO_SELF_FB_VERIFIED = 106;
		BLUE_MSG_UNVERIFIED_TO_VERIFIED = 107;
		BLUE_MSG_VERIFIED_TO_BSP_FB_UNVERIFIED = 108;
		BLUE_MSG_VERIFIED_TO_BSP_PREMISE_UNVERIFIED = 109;
		BLUE_MSG_VERIFIED_TO_SELF_FB_UNVERIFIED = 110;
		BLUE_MSG_VERIFIED_TO_UNVERIFIED = 111;
		BLUE_MSG_BSP_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED = 112;
		BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_FB_VERIFIED = 113;
		BLUE_MSG_BSP_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED = 114;
		BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_FB_UNVERIFIED = 115;
		BLUE_MSG_SELF_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED = 116;
		BLUE_MSG_SELF_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED = 117;
		E2E_IDENTITY_UNAVAILABLE = 118;
		GROUP_CREATING = 119;
		GROUP_CREATE_FAILED = 120;
		GROUP_BOUNCED = 121;
		BLOCK_CONTACT = 122;
		EPHEMERAL_SETTING_NOT_APPLIED = 123;
		SYNC_FAILED = 124;
		SYNCING = 125;
		BIZ_PRIVACY_MODE_INIT_FB = 126;
		BIZ_PRIVACY_MODE_INIT_BSP = 127;
		BIZ_PRIVACY_MODE_TO_FB = 128;
		BIZ_PRIVACY_MODE_TO_BSP = 129;
		DISAPPEARING_MODE = 130;
		E2E_DEVICE_FETCH_FAILED = 131;
		ADMIN_REVOKE = 132;
		GROUP_INVITE_LINK_GROWTH_LOCKED = 133;
		COMMUNITY_LINK_PARENT_GROUP = 134;
		COMMUNITY_LINK_SIBLING_GROUP = 135;
		COMMUNITY_LINK_SUB_GROUP = 136;
		COMMUNITY_UNLINK_PARENT_GROUP = 137;
		COMMUNITY_UNLINK_SIBLING_GROUP = 138;
		COMMUNITY_UNLINK_SUB_GROUP = 139;
		GROUP_PARTICIPANT_ACCEPT = 140;
		GROUP_PARTICIPANT_LINKED_GROUP_JOIN = 141;
		COMMUNITY_CREATE = 142;
		EPHEMERAL_KEEP_IN_CHAT = 143;
		GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST = 144;
		GROUP_MEMBERSHIP_JOIN_APPROVAL_MODE = 145;
		INTEGRITY_UNLINK_PARENT_GROUP = 146;
		COMMUNITY_PARTICIPANT_PROMOTE = 147;
		COMMUNITY_PARTICIPANT_DEMOTE = 148;
		COMMUNITY_PARENT_GROUP_DELETED = 149;
		COMMUNITY_LINK_PARENT_GROUP_MEMBERSHIP_APPROVAL = 150;
		GROUP_PARTICIPANT_JOINED_GROUP_AND_PARENT_GROUP = 151;
		MASKED_THREAD_CREATED = 152;
		MASKED_THREAD_UNMASKED = 153;
		BIZ_CHAT_ASSIGNMENT = 154;
		CHAT_PSA = 155;
		CHAT_POLL_CREATION_MESSAGE = 156;
		CAG_MASKED_THREAD_CREATED = 157;
		COMMUNITY_PARENT_GROUP_SUBJECT_CHANGED = 158;
		CAG_INVITE_AUTO_ADD = 159;
		BIZ_CHAT_ASSIGNMENT_UNASSIGN = 160;
		CAG_INVITE_AUTO_JOINED = 161;
		SCHEDULED_CALL_START_MESSAGE = 162;
		COMMUNITY_INVITE_RICH = 163;
		COMMUNITY_INVITE_AUTO_ADD_RICH = 164;
		SUB_GROUP_INVITE_RICH = 165;
		SUB_GROUP_PARTICIPANT_ADD_RICH = 166;
		COMMUNITY_LINK_PARENT_GROUP_RICH = 167;
		COMMUNITY_PARTICIPANT_ADD_RICH = 168;
		SILENCED_UNKNOWN_CALLER_AUDIO = 169;
		SILENCED_UNKNOWN_CALLER_VIDEO = 170;
		GROUP_MEMBER_ADD_MODE = 171;
		GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST_NON_ADMIN_ADD = 172;
		COMMUNITY_CHANGE_DESCRIPTION = 173;
		SENDER_INVITE = 174;
		RECEIVER_INVITE = 175;
		COMMUNITY_ALLOW_MEMBER_ADDED_GROUPS = 176;
		PINNED_MESSAGE_IN_CHAT = 177;
		PAYMENT_INVITE_SETUP_INVITER = 178;
		PAYMENT_INVITE_SETUP_INVITEE_RECEIVE_ONLY = 179;
		PAYMENT_INVITE_SETUP_INVITEE_SEND_AND_RECEIVE = 180;
		LINKED_GROUP_CALL_START = 181;
		REPORT_TO_ADMIN_ENABLED_STATUS = 182;
		EMPTY_SUBGROUP_CREATE = 183;
		SCHEDULED_CALL_CANCEL = 184;
		SUBGROUP_ADMIN_TRIGGERED_AUTO_ADD_RICH = 185;
		GROUP_CHANGE_RECENT_HISTORY_SHARING = 186;
		PAID_MESSAGE_SERVER_CAMPAIGN_ID = 187;
		GENERAL_CHAT_CREATE = 188;
		GENERAL_CHAT_ADD = 189;
		GENERAL_CHAT_AUTO_ADD_DISABLED = 190;
		SUGGESTED_SUBGROUP_ANNOUNCE = 191;
		BIZ_BOT_1P_MESSAGING_ENABLED = 192;
		CHANGE_USERNAME = 193;
		BIZ_COEX_PRIVACY_INIT_SELF = 194;
		BIZ_COEX_PRIVACY_TRANSITION_SELF = 195;
		SUPPORT_AI_EDUCATION = 196;
		BIZ_BOT_3P_MESSAGING_ENABLED = 197;
		REMINDER_SETUP_MESSAGE = 198;
		REMINDER_SENT_MESSAGE = 199;
		REMINDER_CANCEL_MESSAGE = 200;
		BIZ_COEX_PRIVACY_INIT = 201;
		BIZ_COEX_PRIVACY_TRANSITION = 202;
		GROUP_DEACTIVATED = 203;
		COMMUNITY_DEACTIVATE_SIBLING_GROUP = 204;
		EVENT_UPDATED = 205;
		EVENT_CANCELED = 206;
		COMMUNITY_OWNER_UPDATED = 207;
		COMMUNITY_SUB_GROUP_VISIBILITY_HIDDEN = 208;
		CAPI_GROUP_NE2EE_SYSTEM_MESSAGE = 209;
		STATUS_MENTION = 210;
		USER_CONTROLS_SYSTEM_MESSAGE = 211;
		SUPPORT_SYSTEM_MESSAGE = 212;
		CHANGE_LID = 213;
		BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_IN_MESSAGE = 214;
		BIZ_CUSTOMER_3PD_DATA_SHARING_OPT_OUT_MESSAGE = 215;
		CHANGE_LIMIT_SHARING = 216;
		GROUP_MEMBER_LINK_MODE = 217;
		BIZ_AUTOMATICALLY_LABELED_CHAT_SYSTEM_MESSAGE = 218;
		PHONE_NUMBER_HIDING_CHAT_DEPRECATED_MESSAGE = 219;
	}

	enum Status {
		ERROR = 0;
		PENDING = 1;
		SERVER_ACK = 2;
		DELIVERY_ACK = 3;
		READ = 4;
		PLAYED = 5;
	}

	required WACommon.MessageKey key = 1;
	optional WAWebProtobufsE2E.Message message = 2;
	optional uint64 messageTimestamp = 3;
	optional Status status = 4;
	optional string participant = 5;
	optional uint64 messageC2STimestamp = 6;
	optional bool ignore = 16;
	optional bool starred = 17;
	optional bool broadcast = 18;
	optional string pushName = 19;
	optional bytes mediaCiphertextSHA256 = 20;
	optional bool multicast = 21;
	optional bool urlText = 22;
	optional bool urlNumber = 23;
	optional StubType messageStubType = 24;
	optional bool clearMedia = 25;
	repeated string messageStubParameters = 26;
	optional uint32 duration = 27;
	repeated string labels = 28;
	optional PaymentInfo paymentInfo = 29;
	optional WAWebProtobufsE2E.LiveLocationMessage finalLiveLocation = 30;
	optional PaymentInfo quotedPaymentInfo = 31;
	optional uint64 ephemeralStartTimestamp = 32;
	optional uint32 ephemeralDuration = 33;
	optional bool ephemeralOffToOn = 34;
	optional bool ephemeralOutOfSync = 35;
	optional BizPrivacyStatus bizPrivacyStatus = 36;
	optional string verifiedBizName = 37;
	optional MediaData mediaData = 38;
	optional PhotoChange photoChange = 39;
	repeated UserReceipt userReceipt = 40;
	repeated Reaction reactions = 41;
	optional MediaData quotedStickerData = 42;
	optional bytes futureproofData = 43;
	optional StatusPSA statusPsa = 44;
	repeated PollUpdate pollUpdates = 45;
	optional PollAdditionalMetadata pollAdditionalMetadata = 46;
	optional string agentID = 47;
	optional bool statusAlreadyViewed = 48;
	optional bytes messageSecret = 49;
	optional KeepInChat keepInChat = 50;
	optional string originalSelfAuthorUserJIDString = 51;
	optional uint64 revokeMessageTimestamp = 52;
	optional PinInChat pinInChat = 54;
	optional PremiumMessageInfo premiumMessageInfo = 55;
	optional bool is1PBizBotMessage = 56;
	optional bool isGroupHistoryMessage = 57;
	optional string botMessageInvokerJID = 58;
	optional CommentMetadata commentMetadata = 59;
	repeated EventResponse eventResponses = 61;
	optional ReportingTokenInfo reportingTokenInfo = 62;
	optional uint64 newsletterServerID = 63;
	optional EventAdditionalMetadata eventAdditionalMetadata = 64;
	optional bool isMentionedInStatus = 65;
	repeated string statusMentions = 66;
	optional WACommon.MessageKey targetMessageID = 67;
	repeated MessageAddOn messageAddOns = 68;
	optional StatusMentionMessage statusMentionMessageInfo = 69;
	optional bool isSupportAiMessage = 70;
	repeated string statusMentionSources = 71;
	repeated Citation supportAiCitations = 72;
	optional string botTargetID = 73;
}

message PaymentInfo {
	enum TxnStatus {
		UNKNOWN = 0;
		PENDING_SETUP = 1;
		PENDING_RECEIVER_SETUP = 2;
		INIT = 3;
		SUCCESS = 4;
		COMPLETED = 5;
		FAILED = 6;
		FAILED_RISK = 7;
		FAILED_PROCESSING = 8;
		FAILED_RECEIVER_PROCESSING = 9;
		FAILED_DA = 10;
		FAILED_DA_FINAL = 11;
		REFUNDED_TXN = 12;
		REFUND_FAILED = 13;
		REFUND_FAILED_PROCESSING = 14;
		REFUND_FAILED_DA = 15;
		EXPIRED_TXN = 16;
		AUTH_CANCELED = 17;
		AUTH_CANCEL_FAILED_PROCESSING = 18;
		AUTH_CANCEL_FAILED = 19;
		COLLECT_INIT = 20;
		COLLECT_SUCCESS = 21;
		COLLECT_FAILED = 22;
		COLLECT_FAILED_RISK = 23;
		COLLECT_REJECTED = 24;
		COLLECT_EXPIRED = 25;
		COLLECT_CANCELED = 26;
		COLLECT_CANCELLING = 27;
		IN_REVIEW = 28;
		REVERSAL_SUCCESS = 29;
		REVERSAL_PENDING = 30;
		REFUND_PENDING = 31;
	}

	enum Status {
		UNKNOWN_STATUS = 0;
		PROCESSING = 1;
		SENT = 2;
		NEED_TO_ACCEPT = 3;
		COMPLETE = 4;
		COULD_NOT_COMPLETE = 5;
		REFUNDED = 6;
		EXPIRED = 7;
		REJECTED = 8;
		CANCELLED = 9;
		WAITING_FOR_PAYER = 10;
		WAITING = 11;
	}

	enum Currency {
		UNKNOWN_CURRENCY = 0;
		INR = 1;
	}

	optional Currency currencyDeprecated = 1;
	optional uint64 amount1000 = 2;
	optional string receiverJID = 3;
	optional Status status = 4;
	optional uint64 transactionTimestamp = 5;
	optional WACommon.MessageKey requestMessageKey = 6;
	optional uint64 expiryTimestamp = 7;
	optional bool futureproofed = 8;
	optional string currency = 9;
	optional TxnStatus txnStatus = 10;
	optional bool useNoviFiatFormat = 11;
	optional WAWebProtobufsE2E.Money primaryAmount = 12;
	optional WAWebProtobufsE2E.Money exchangeAmount = 13;
}

message WebFeatures {
	enum Flag {
		NOT_STARTED = 0;
		FORCE_UPGRADE = 1;
		DEVELOPMENT = 2;
		PRODUCTION = 3;
	}

	optional Flag labelsDisplay = 1;
	optional Flag voipIndividualOutgoing = 2;
	optional Flag groupsV3 = 3;
	optional Flag groupsV3Create = 4;
	optional Flag changeNumberV2 = 5;
	optional Flag queryStatusV3Thumbnail = 6;
	optional Flag liveLocations = 7;
	optional Flag queryVname = 8;
	optional Flag voipIndividualIncoming = 9;
	optional Flag quickRepliesQuery = 10;
	optional Flag payments = 11;
	optional Flag stickerPackQuery = 12;
	optional Flag liveLocationsFinal = 13;
	optional Flag labelsEdit = 14;
	optional Flag mediaUpload = 15;
	optional Flag mediaUploadRichQuickReplies = 18;
	optional Flag vnameV2 = 19;
	optional Flag videoPlaybackURL = 20;
	optional Flag statusRanking = 21;
	optional Flag voipIndividualVideo = 22;
	optional Flag thirdPartyStickers = 23;
	optional Flag frequentlyForwardedSetting = 24;
	optional Flag groupsV4JoinPermission = 25;
	optional Flag recentStickers = 26;
	optional Flag catalog = 27;
	optional Flag starredStickers = 28;
	optional Flag voipGroupCall = 29;
	optional Flag templateMessage = 30;
	optional Flag templateMessageInteractivity = 31;
	optional Flag ephemeralMessages = 32;
	optional Flag e2ENotificationSync = 33;
	optional Flag recentStickersV2 = 34;
	optional Flag recentStickersV3 = 36;
	optional Flag userNotice = 37;
	optional Flag support = 39;
	optional Flag groupUiiCleanup = 40;
	optional Flag groupDogfoodingInternalOnly = 41;
	optional Flag settingsSync = 42;
	optional Flag archiveV2 = 43;
	optional Flag ephemeralAllowGroupMembers = 44;
	optional Flag ephemeral24HDuration = 45;
	optional Flag mdForceUpgrade = 46;
	optional Flag disappearingMode = 47;
	optional Flag externalMdOptInAvailable = 48;
	optional Flag noDeleteMessageTimeLimit = 49;
}

message PinInChat {
	enum Type {
		UNKNOWN_TYPE = 0;
		PIN_FOR_ALL = 1;
		UNPIN_FOR_ALL = 2;
	}

	optional Type type = 1;
	optional WACommon.MessageKey key = 2;
	optional int64 senderTimestampMS = 3;
	optional int64 serverTimestampMS = 4;
	optional MessageAddOnContextInfo messageAddOnContextInfo = 5;
}

message MessageAddOn {
	enum MessageAddOnType {
		UNDEFINED = 0;
		REACTION = 1;
		EVENT_RESPONSE = 2;
		POLL_UPDATE = 3;
		PIN_IN_CHAT = 4;
	}

	optional MessageAddOnType messageAddOnType = 1;
	optional WAWebProtobufsE2E.Message messageAddOn = 2;
	optional int64 senderTimestampMS = 3;
	optional int64 serverTimestampMS = 4;
	optional WebMessageInfo.Status status = 5;
	optional MessageAddOnContextInfo addOnContextInfo = 6;
	optional WACommon.MessageKey messageAddOnKey = 7;
	optional LegacyMessage legacyMessage = 8;
}

message CommentMetadata {
	optional WACommon.MessageKey commentParentKey = 1;
	optional uint32 replyCount = 2;
}

message WebNotificationsInfo {
	optional uint64 timestamp = 2;
	optional uint32 unreadChats = 3;
	optional uint32 notifyMessageCount = 4;
	repeated WebMessageInfo notifyMessages = 5;
}

message NotificationMessageInfo {
	optional WACommon.MessageKey key = 1;
	optional WAWebProtobufsE2E.Message message = 2;
	optional uint64 messageTimestamp = 3;
	optional string participant = 4;
}

message ReportingTokenInfo {
	optional bytes reportingTag = 1;
}

message MediaData {
	optional string localPath = 1;
}

message PhotoChange {
	optional bytes oldPhoto = 1;
	optional bytes newPhoto = 2;
	optional uint32 newPhotoID = 3;
}

message StatusPSA {
	required uint64 campaignID = 44;
	optional uint64 campaignExpirationTimestamp = 45;
}

message UserReceipt {
	required string userJID = 1;
	optional int64 receiptTimestamp = 2;
	optional int64 readTimestamp = 3;
	optional int64 playedTimestamp = 4;
	repeated string pendingDeviceJID = 5;
	repeated string deliveredDeviceJID = 6;
}

message Reaction {
	optional WACommon.MessageKey key = 1;
	optional string text = 2;
	optional string groupingKey = 3;
	optional int64 senderTimestampMS = 4;
	optional bool unread = 5;
}

message PollUpdate {
	optional WACommon.MessageKey pollUpdateMessageKey = 1;
	optional WAWebProtobufsE2E.PollVoteMessage vote = 2;
	optional int64 senderTimestampMS = 3;
	optional int64 serverTimestampMS = 4;
	optional bool unread = 5;
}

message PollAdditionalMetadata {
	optional bool pollInvalidated = 1;
}

message EventAdditionalMetadata {
	optional bool isStale = 1;
}

message KeepInChat {
	optional WAWebProtobufsE2E.KeepType keepType = 1;
	optional int64 serverTimestamp = 2;
	optional WACommon.MessageKey key = 3;
	optional string deviceJID = 4;
	optional int64 clientTimestampMS = 5;
	optional int64 serverTimestampMS = 6;
}

message MessageAddOnContextInfo {
	optional uint32 messageAddOnDurationInSecs = 1;
	optional WAWebProtobufsE2E.MessageContextInfo.MessageAddonExpiryType messageAddOnExpiryType = 2;
}

message PremiumMessageInfo {
	optional string serverCampaignID = 1;
}

message EventResponse {
	optional WACommon.MessageKey eventResponseMessageKey = 1;
	optional int64 timestampMS = 2;
	optional WAWebProtobufsE2E.EventResponseMessage eventResponseMessage = 3;
	optional bool unread = 4;
}

message LegacyMessage {
	optional WAWebProtobufsE2E.EventResponseMessage eventResponseMessage = 1;
	optional WAWebProtobufsE2E.PollVoteMessage pollVote = 2;
}

message StatusMentionMessage {
	optional WAWebProtobufsE2E.Message quotedStatus = 1;
}

message Citation {
	required string title = 1;
	required string subtitle = 2;
	required string cmsID = 3;
	required string imageURL = 4;
}
