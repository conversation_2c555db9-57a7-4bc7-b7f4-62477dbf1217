// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waWinUIApi/WAWinUIApi.proto

package waWinUIApi

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PositronDataSource int32

const (
	PositronDataSource_MESSAGES           PositronDataSource = 1
	PositronDataSource_CHATS              PositronDataSource = 2
	PositronDataSource_CONTACTS           PositronDataSource = 3
	PositronDataSource_GROUP_METADATA     PositronDataSource = 4
	PositronDataSource_GROUP_PARTICIPANTS PositronDataSource = 5
	PositronDataSource_REACTIONS          PositronDataSource = 6
)

// Enum value maps for PositronDataSource.
var (
	PositronDataSource_name = map[int32]string{
		1: "MESSAGES",
		2: "CHATS",
		3: "CONTACTS",
		4: "GROUP_METADATA",
		5: "GROUP_PARTICIPANTS",
		6: "REACTIONS",
	}
	PositronDataSource_value = map[string]int32{
		"MESSAGES":           1,
		"CHATS":              2,
		"CONTACTS":           3,
		"GROUP_METADATA":     4,
		"GROUP_PARTICIPANTS": 5,
		"REACTIONS":          6,
	}
)

func (x PositronDataSource) Enum() *PositronDataSource {
	p := new(PositronDataSource)
	*p = x
	return p
}

func (x PositronDataSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PositronDataSource) Descriptor() protoreflect.EnumDescriptor {
	return file_waWinUIApi_WAWinUIApi_proto_enumTypes[0].Descriptor()
}

func (PositronDataSource) Type() protoreflect.EnumType {
	return &file_waWinUIApi_WAWinUIApi_proto_enumTypes[0]
}

func (x PositronDataSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PositronDataSource) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PositronDataSource(num)
	return nil
}

// Deprecated: Use PositronDataSource.Descriptor instead.
func (PositronDataSource) EnumDescriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{0}
}

type PositronMessage struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Timestamp     *int64                  `protobuf:"varint,1,opt,name=timestamp" json:"timestamp,omitempty"`
	Type          *string                 `protobuf:"bytes,2,opt,name=type" json:"type,omitempty"`
	Body          *string                 `protobuf:"bytes,3,opt,name=body" json:"body,omitempty"`
	ID            *PositronMessage_MsgKey `protobuf:"bytes,4,opt,name=ID" json:"ID,omitempty"`
	JSON          *string                 `protobuf:"bytes,99,opt,name=JSON" json:"JSON,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositronMessage) Reset() {
	*x = PositronMessage{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronMessage) ProtoMessage() {}

func (x *PositronMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronMessage.ProtoReflect.Descriptor instead.
func (*PositronMessage) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{0}
}

func (x *PositronMessage) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *PositronMessage) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *PositronMessage) GetBody() string {
	if x != nil && x.Body != nil {
		return *x.Body
	}
	return ""
}

func (x *PositronMessage) GetID() *PositronMessage_MsgKey {
	if x != nil {
		return x.ID
	}
	return nil
}

func (x *PositronMessage) GetJSON() string {
	if x != nil && x.JSON != nil {
		return *x.JSON
	}
	return ""
}

type PositronChat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            *string                `protobuf:"bytes,1,opt,name=ID" json:"ID,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Timestamp     *int64                 `protobuf:"varint,3,opt,name=timestamp" json:"timestamp,omitempty"`
	UnreadCount   *int64                 `protobuf:"varint,4,opt,name=unreadCount" json:"unreadCount,omitempty"`
	JSON          *string                `protobuf:"bytes,99,opt,name=JSON" json:"JSON,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositronChat) Reset() {
	*x = PositronChat{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronChat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronChat) ProtoMessage() {}

func (x *PositronChat) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronChat.ProtoReflect.Descriptor instead.
func (*PositronChat) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{1}
}

func (x *PositronChat) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *PositronChat) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *PositronChat) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *PositronChat) GetUnreadCount() int64 {
	if x != nil && x.UnreadCount != nil {
		return *x.UnreadCount
	}
	return 0
}

func (x *PositronChat) GetJSON() string {
	if x != nil && x.JSON != nil {
		return *x.JSON
	}
	return ""
}

type PositronContact struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ID                   *string                `protobuf:"bytes,1,opt,name=ID" json:"ID,omitempty"`
	PhoneNumber          *string                `protobuf:"bytes,2,opt,name=phoneNumber" json:"phoneNumber,omitempty"`
	Name                 *string                `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	IsAddressBookContact *bool                  `protobuf:"varint,4,opt,name=isAddressBookContact" json:"isAddressBookContact,omitempty"`
	JSON                 *string                `protobuf:"bytes,99,opt,name=JSON" json:"JSON,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PositronContact) Reset() {
	*x = PositronContact{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronContact) ProtoMessage() {}

func (x *PositronContact) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronContact.ProtoReflect.Descriptor instead.
func (*PositronContact) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{2}
}

func (x *PositronContact) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *PositronContact) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *PositronContact) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *PositronContact) GetIsAddressBookContact() bool {
	if x != nil && x.IsAddressBookContact != nil {
		return *x.IsAddressBookContact
	}
	return false
}

func (x *PositronContact) GetJSON() string {
	if x != nil && x.JSON != nil {
		return *x.JSON
	}
	return ""
}

type PositronGroupMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            *string                `protobuf:"bytes,1,opt,name=ID" json:"ID,omitempty"`
	Subject       *string                `protobuf:"bytes,2,opt,name=subject" json:"subject,omitempty"`
	JSON          *string                `protobuf:"bytes,99,opt,name=JSON" json:"JSON,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositronGroupMetadata) Reset() {
	*x = PositronGroupMetadata{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronGroupMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronGroupMetadata) ProtoMessage() {}

func (x *PositronGroupMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronGroupMetadata.ProtoReflect.Descriptor instead.
func (*PositronGroupMetadata) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{3}
}

func (x *PositronGroupMetadata) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *PositronGroupMetadata) GetSubject() string {
	if x != nil && x.Subject != nil {
		return *x.Subject
	}
	return ""
}

func (x *PositronGroupMetadata) GetJSON() string {
	if x != nil && x.JSON != nil {
		return *x.JSON
	}
	return ""
}

type PositronGroupParticipants struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            *string                `protobuf:"bytes,1,opt,name=ID" json:"ID,omitempty"`
	Participants  []string               `protobuf:"bytes,2,rep,name=participants" json:"participants,omitempty"`
	JSON          *string                `protobuf:"bytes,99,opt,name=JSON" json:"JSON,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositronGroupParticipants) Reset() {
	*x = PositronGroupParticipants{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronGroupParticipants) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronGroupParticipants) ProtoMessage() {}

func (x *PositronGroupParticipants) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronGroupParticipants.ProtoReflect.Descriptor instead.
func (*PositronGroupParticipants) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{4}
}

func (x *PositronGroupParticipants) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *PositronGroupParticipants) GetParticipants() []string {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *PositronGroupParticipants) GetJSON() string {
	if x != nil && x.JSON != nil {
		return *x.JSON
	}
	return ""
}

type PositronReaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            *string                `protobuf:"bytes,1,opt,name=ID" json:"ID,omitempty"`
	ParentMsgKey  *string                `protobuf:"bytes,2,opt,name=parentMsgKey" json:"parentMsgKey,omitempty"`
	ReactionText  *string                `protobuf:"bytes,3,opt,name=reactionText" json:"reactionText,omitempty"`
	Timestamp     *int64                 `protobuf:"varint,4,opt,name=timestamp" json:"timestamp,omitempty"`
	SenderUserJID *string                `protobuf:"bytes,5,opt,name=senderUserJID" json:"senderUserJID,omitempty"`
	JSON          *string                `protobuf:"bytes,99,opt,name=JSON" json:"JSON,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositronReaction) Reset() {
	*x = PositronReaction{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronReaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronReaction) ProtoMessage() {}

func (x *PositronReaction) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronReaction.ProtoReflect.Descriptor instead.
func (*PositronReaction) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{5}
}

func (x *PositronReaction) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *PositronReaction) GetParentMsgKey() string {
	if x != nil && x.ParentMsgKey != nil {
		return *x.ParentMsgKey
	}
	return ""
}

func (x *PositronReaction) GetReactionText() string {
	if x != nil && x.ReactionText != nil {
		return *x.ReactionText
	}
	return ""
}

func (x *PositronReaction) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *PositronReaction) GetSenderUserJID() string {
	if x != nil && x.SenderUserJID != nil {
		return *x.SenderUserJID
	}
	return ""
}

func (x *PositronReaction) GetJSON() string {
	if x != nil && x.JSON != nil {
		return *x.JSON
	}
	return ""
}

type PositronData struct {
	state             protoimpl.MessageState       `protogen:"open.v1"`
	DataSource        *PositronDataSource          `protobuf:"varint,1,opt,name=dataSource,enum=WAWinUIApi.PositronDataSource" json:"dataSource,omitempty"`
	Messages          []*PositronMessage           `protobuf:"bytes,2,rep,name=messages" json:"messages,omitempty"`
	Chats             []*PositronChat              `protobuf:"bytes,3,rep,name=chats" json:"chats,omitempty"`
	Contacts          []*PositronContact           `protobuf:"bytes,4,rep,name=contacts" json:"contacts,omitempty"`
	GroupMetadata     []*PositronGroupMetadata     `protobuf:"bytes,5,rep,name=groupMetadata" json:"groupMetadata,omitempty"`
	GroupParticipants []*PositronGroupParticipants `protobuf:"bytes,6,rep,name=groupParticipants" json:"groupParticipants,omitempty"`
	Reactions         []*PositronReaction          `protobuf:"bytes,7,rep,name=reactions" json:"reactions,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PositronData) Reset() {
	*x = PositronData{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronData) ProtoMessage() {}

func (x *PositronData) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronData.ProtoReflect.Descriptor instead.
func (*PositronData) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{6}
}

func (x *PositronData) GetDataSource() PositronDataSource {
	if x != nil && x.DataSource != nil {
		return *x.DataSource
	}
	return PositronDataSource_MESSAGES
}

func (x *PositronData) GetMessages() []*PositronMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *PositronData) GetChats() []*PositronChat {
	if x != nil {
		return x.Chats
	}
	return nil
}

func (x *PositronData) GetContacts() []*PositronContact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *PositronData) GetGroupMetadata() []*PositronGroupMetadata {
	if x != nil {
		return x.GroupMetadata
	}
	return nil
}

func (x *PositronData) GetGroupParticipants() []*PositronGroupParticipants {
	if x != nil {
		return x.GroupParticipants
	}
	return nil
}

func (x *PositronData) GetReactions() []*PositronReaction {
	if x != nil {
		return x.Reactions
	}
	return nil
}

type PositronMessage_MsgKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FromMe        *bool                  `protobuf:"varint,1,opt,name=fromMe" json:"fromMe,omitempty"`
	Remote        *PositronMessage_WID   `protobuf:"bytes,2,opt,name=remote" json:"remote,omitempty"`
	ID            *string                `protobuf:"bytes,3,opt,name=ID" json:"ID,omitempty"`
	Participant   *PositronMessage_WID   `protobuf:"bytes,4,opt,name=participant" json:"participant,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositronMessage_MsgKey) Reset() {
	*x = PositronMessage_MsgKey{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronMessage_MsgKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronMessage_MsgKey) ProtoMessage() {}

func (x *PositronMessage_MsgKey) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronMessage_MsgKey.ProtoReflect.Descriptor instead.
func (*PositronMessage_MsgKey) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PositronMessage_MsgKey) GetFromMe() bool {
	if x != nil && x.FromMe != nil {
		return *x.FromMe
	}
	return false
}

func (x *PositronMessage_MsgKey) GetRemote() *PositronMessage_WID {
	if x != nil {
		return x.Remote
	}
	return nil
}

func (x *PositronMessage_MsgKey) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *PositronMessage_MsgKey) GetParticipant() *PositronMessage_WID {
	if x != nil {
		return x.Participant
	}
	return nil
}

type PositronMessage_WID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Serialized    *string                `protobuf:"bytes,1,opt,name=serialized" json:"serialized,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PositronMessage_WID) Reset() {
	*x = PositronMessage_WID{}
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PositronMessage_WID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositronMessage_WID) ProtoMessage() {}

func (x *PositronMessage_WID) ProtoReflect() protoreflect.Message {
	mi := &file_waWinUIApi_WAWinUIApi_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositronMessage_WID.ProtoReflect.Descriptor instead.
func (*PositronMessage_WID) Descriptor() ([]byte, []int) {
	return file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP(), []int{0, 1}
}

func (x *PositronMessage_WID) GetSerialized() string {
	if x != nil && x.Serialized != nil {
		return *x.Serialized
	}
	return ""
}

var File_waWinUIApi_WAWinUIApi_proto protoreflect.FileDescriptor

const file_waWinUIApi_WAWinUIApi_proto_rawDesc = "" +
	"\n" +
	"\x1bwaWinUIApi/WAWinUIApi.proto\x12\n" +
	"WAWinUIApi\"\xf5\x02\n" +
	"\x0fPositronMessage\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x12\n" +
	"\x04body\x18\x03 \x01(\tR\x04body\x122\n" +
	"\x02ID\x18\x04 \x01(\v2\".WAWinUIApi.PositronMessage.MsgKeyR\x02ID\x12\x12\n" +
	"\x04JSON\x18c \x01(\tR\x04JSON\x1a\xac\x01\n" +
	"\x06MsgKey\x12\x16\n" +
	"\x06fromMe\x18\x01 \x01(\bR\x06fromMe\x127\n" +
	"\x06remote\x18\x02 \x01(\v2\x1f.WAWinUIApi.PositronMessage.WIDR\x06remote\x12\x0e\n" +
	"\x02ID\x18\x03 \x01(\tR\x02ID\x12A\n" +
	"\vparticipant\x18\x04 \x01(\v2\x1f.WAWinUIApi.PositronMessage.WIDR\vparticipant\x1a%\n" +
	"\x03WID\x12\x1e\n" +
	"\n" +
	"serialized\x18\x01 \x01(\tR\n" +
	"serialized\"\x86\x01\n" +
	"\fPositronChat\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1c\n" +
	"\ttimestamp\x18\x03 \x01(\x03R\ttimestamp\x12 \n" +
	"\vunreadCount\x18\x04 \x01(\x03R\vunreadCount\x12\x12\n" +
	"\x04JSON\x18c \x01(\tR\x04JSON\"\x9f\x01\n" +
	"\x0fPositronContact\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12 \n" +
	"\vphoneNumber\x18\x02 \x01(\tR\vphoneNumber\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x122\n" +
	"\x14isAddressBookContact\x18\x04 \x01(\bR\x14isAddressBookContact\x12\x12\n" +
	"\x04JSON\x18c \x01(\tR\x04JSON\"U\n" +
	"\x15PositronGroupMetadata\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x18\n" +
	"\asubject\x18\x02 \x01(\tR\asubject\x12\x12\n" +
	"\x04JSON\x18c \x01(\tR\x04JSON\"c\n" +
	"\x19PositronGroupParticipants\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\"\n" +
	"\fparticipants\x18\x02 \x03(\tR\fparticipants\x12\x12\n" +
	"\x04JSON\x18c \x01(\tR\x04JSON\"\xc2\x01\n" +
	"\x10PositronReaction\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\"\n" +
	"\fparentMsgKey\x18\x02 \x01(\tR\fparentMsgKey\x12\"\n" +
	"\freactionText\x18\x03 \x01(\tR\freactionText\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\x03R\ttimestamp\x12$\n" +
	"\rsenderUserJID\x18\x05 \x01(\tR\rsenderUserJID\x12\x12\n" +
	"\x04JSON\x18c \x01(\tR\x04JSON\"\xca\x03\n" +
	"\fPositronData\x12>\n" +
	"\n" +
	"dataSource\x18\x01 \x01(\x0e2\x1e.WAWinUIApi.PositronDataSourceR\n" +
	"dataSource\x127\n" +
	"\bmessages\x18\x02 \x03(\v2\x1b.WAWinUIApi.PositronMessageR\bmessages\x12.\n" +
	"\x05chats\x18\x03 \x03(\v2\x18.WAWinUIApi.PositronChatR\x05chats\x127\n" +
	"\bcontacts\x18\x04 \x03(\v2\x1b.WAWinUIApi.PositronContactR\bcontacts\x12G\n" +
	"\rgroupMetadata\x18\x05 \x03(\v2!.WAWinUIApi.PositronGroupMetadataR\rgroupMetadata\x12S\n" +
	"\x11groupParticipants\x18\x06 \x03(\v2%.WAWinUIApi.PositronGroupParticipantsR\x11groupParticipants\x12:\n" +
	"\treactions\x18\a \x03(\v2\x1c.WAWinUIApi.PositronReactionR\treactions*v\n" +
	"\x12PositronDataSource\x12\f\n" +
	"\bMESSAGES\x10\x01\x12\t\n" +
	"\x05CHATS\x10\x02\x12\f\n" +
	"\bCONTACTS\x10\x03\x12\x12\n" +
	"\x0eGROUP_METADATA\x10\x04\x12\x16\n" +
	"\x12GROUP_PARTICIPANTS\x10\x05\x12\r\n" +
	"\tREACTIONS\x10\x06B&Z$go.mau.fi/whatsmeow/proto/waWinUIApi"

var (
	file_waWinUIApi_WAWinUIApi_proto_rawDescOnce sync.Once
	file_waWinUIApi_WAWinUIApi_proto_rawDescData []byte
)

func file_waWinUIApi_WAWinUIApi_proto_rawDescGZIP() []byte {
	file_waWinUIApi_WAWinUIApi_proto_rawDescOnce.Do(func() {
		file_waWinUIApi_WAWinUIApi_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waWinUIApi_WAWinUIApi_proto_rawDesc), len(file_waWinUIApi_WAWinUIApi_proto_rawDesc)))
	})
	return file_waWinUIApi_WAWinUIApi_proto_rawDescData
}

var file_waWinUIApi_WAWinUIApi_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_waWinUIApi_WAWinUIApi_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_waWinUIApi_WAWinUIApi_proto_goTypes = []any{
	(PositronDataSource)(0),           // 0: WAWinUIApi.PositronDataSource
	(*PositronMessage)(nil),           // 1: WAWinUIApi.PositronMessage
	(*PositronChat)(nil),              // 2: WAWinUIApi.PositronChat
	(*PositronContact)(nil),           // 3: WAWinUIApi.PositronContact
	(*PositronGroupMetadata)(nil),     // 4: WAWinUIApi.PositronGroupMetadata
	(*PositronGroupParticipants)(nil), // 5: WAWinUIApi.PositronGroupParticipants
	(*PositronReaction)(nil),          // 6: WAWinUIApi.PositronReaction
	(*PositronData)(nil),              // 7: WAWinUIApi.PositronData
	(*PositronMessage_MsgKey)(nil),    // 8: WAWinUIApi.PositronMessage.MsgKey
	(*PositronMessage_WID)(nil),       // 9: WAWinUIApi.PositronMessage.WID
}
var file_waWinUIApi_WAWinUIApi_proto_depIdxs = []int32{
	8,  // 0: WAWinUIApi.PositronMessage.ID:type_name -> WAWinUIApi.PositronMessage.MsgKey
	0,  // 1: WAWinUIApi.PositronData.dataSource:type_name -> WAWinUIApi.PositronDataSource
	1,  // 2: WAWinUIApi.PositronData.messages:type_name -> WAWinUIApi.PositronMessage
	2,  // 3: WAWinUIApi.PositronData.chats:type_name -> WAWinUIApi.PositronChat
	3,  // 4: WAWinUIApi.PositronData.contacts:type_name -> WAWinUIApi.PositronContact
	4,  // 5: WAWinUIApi.PositronData.groupMetadata:type_name -> WAWinUIApi.PositronGroupMetadata
	5,  // 6: WAWinUIApi.PositronData.groupParticipants:type_name -> WAWinUIApi.PositronGroupParticipants
	6,  // 7: WAWinUIApi.PositronData.reactions:type_name -> WAWinUIApi.PositronReaction
	9,  // 8: WAWinUIApi.PositronMessage.MsgKey.remote:type_name -> WAWinUIApi.PositronMessage.WID
	9,  // 9: WAWinUIApi.PositronMessage.MsgKey.participant:type_name -> WAWinUIApi.PositronMessage.WID
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_waWinUIApi_WAWinUIApi_proto_init() }
func file_waWinUIApi_WAWinUIApi_proto_init() {
	if File_waWinUIApi_WAWinUIApi_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waWinUIApi_WAWinUIApi_proto_rawDesc), len(file_waWinUIApi_WAWinUIApi_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waWinUIApi_WAWinUIApi_proto_goTypes,
		DependencyIndexes: file_waWinUIApi_WAWinUIApi_proto_depIdxs,
		EnumInfos:         file_waWinUIApi_WAWinUIApi_proto_enumTypes,
		MessageInfos:      file_waWinUIApi_WAWinUIApi_proto_msgTypes,
	}.Build()
	File_waWinUIApi_WAWinUIApi_proto = out.File
	file_waWinUIApi_WAWinUIApi_proto_goTypes = nil
	file_waWinUIApi_WAWinUIApi_proto_depIdxs = nil
}
